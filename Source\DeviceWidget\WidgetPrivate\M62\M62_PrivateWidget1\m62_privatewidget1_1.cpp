#include "m62_privatewidget1_1.h"
#include "globalfont.h"
#include "ui_m62_privatewidget1_1.h"


M62_PrivateWidget1_1::M62_PrivateWidget1_1(QWidget* parent, QString name)
    : InputBase(parent)
    , WorkspaceObserver(name)
    , AppSettingsObserver(name)
    , ui(new Ui::M62_PrivateWidget1_1)
{
    ui->setupUi(this);
    resize(minimumWidth(), minimumHeight());
    QString style;
    style = "QFrame {"
            "   background-color: #161616;"
            "   border-radius: 10px;"
            "   border:none;"
            "}";
    ui->frame->setStyleSheet(style);
    style = "QLineEdit {"
            "   color: rgb(170, 170, 170);"
            "   background-color: rgb(22,22,22);"
            "   selection-color: rgb(0, 121, 107);"
            "   selection-background-color: rgb(224, 247, 250);"
            "   border-top-left-radius: 10px;"
            "   border-top-right-radius: 10px;"
            "   border-bottom-left-radius: 0px;"
            "   border-bottom-right-radius: 0px;"
            "   border-bottom: 1px solid rgb(31,31,31);"
            "}";
    ui->lineEdit->setStyleSheet(style);
    ui->lineEdit->setAttribute(Qt::WA_Hover);
    ui->lineEdit->installEventFilter(this);
    ui->lineEdit->setReadOnly(true);
    ui->lineEdit->setDisabled(true);
    ui->widgetVolumeMeter->setScaleLineHidden();
    ui->widgetVolumeMeter->setWidthRatio(30, 8, 20);
    ui->widgetVolumeMeter->setHeightRatio(3, 1, 82, 0);
    style = "QPushButton {"
            "   background-color: transparent;"
            "   image: url(:/Icon/WidgetCloseBlack.png);"
            "}"
            "QPushButton:hover {"
            "   border: 2px solid rgb(46, 46, 46);"
            "   border-radius: 3px;"
            "}";
    ui->pushButtonClose->setStyleSheet(style);
    ui->pushButtonClose->setParent(this);
    ui->pushButtonClose->setFocusPolicy(Qt::NoFocus);
    ui->pushButtonClose->hide();
    mTimer.setSingleShot(true);
    mTimer.setInterval(100);
    connect(&mTimer, SIGNAL(timeout()), this, SLOT(in_mTimer_timeout()), Qt::UniqueConnection);
}
M62_PrivateWidget1_1::~M62_PrivateWidget1_1()
{
    delete ui;
}


// override
bool M62_PrivateWidget1_1::eventFilter(QObject* obj, QEvent* e)
{
    if(obj == ui->lineEdit && isWidgetEnable() && isWidgetMovable())
    {
        if(e->type() == QEvent::HoverEnter)
        {
            if(ui->pushButtonClose->isHidden())
            {
                mTimer.start();
            }
            return true;
        }
        else if(e->type() == QEvent::HoverLeave)
        {
            QTimer::singleShot(0, [this](){
                if(!ui->lineEdit->underMouse() && !ui->pushButtonClose->underMouse())
                {
                    mTimer.stop();
                    ui->pushButtonClose->hide();
                }
            });
        }
        else if(e->type() == QEvent::MouseButtonPress)
        {
            mTimer.stop();
            ui->pushButtonClose->hide();
        }
    }
    return QWidget::eventFilter(obj, e);
}
void M62_PrivateWidget1_1::resizeEvent(QResizeEvent* e)
{
    Q_UNUSED(e);
    // W
    float wPixelPerRatio=size().width() / 100.0;
    int wSpace1=wPixelPerRatio * 15;
    int wMeter=wPixelPerRatio * 70;
    int xMeter=wSpace1;
    // H
    float hPixelPerRatio = size().height() / 100.0;
    int hLineEdit=hPixelPerRatio * 10;
    int hSpace1=hPixelPerRatio * 0;
    int hMeter=hPixelPerRatio * 90;
    int hSpace2=hPixelPerRatio * 1;
    int hDial=hPixelPerRatio * 15;
    int hSpace3=hPixelPerRatio * 1;
    int hButtonGroup2=hPixelPerRatio * 15;
    int hPushButtonClose=hLineEdit / 100.0 * 70;
    ui->lineEdit->setGeometry(0, 0, size().width(), hLineEdit);
    ui->pushButtonClose->setGeometry(size().width() - hPushButtonClose * 1.3, (hLineEdit - hPushButtonClose) / 2, hPushButtonClose, hPushButtonClose);
    ui->widgetVolumeMeter->setGeometry(xMeter, hLineEdit + hSpace1, wMeter, hMeter);
    mFont.setPixelSize(GLBFHandle.getSuitablePixelSize(mFont, ui->lineEdit->height())*75/100);
    ui->lineEdit->setFont(mFont);
}
void M62_PrivateWidget1_1::updateAttribute()
{

}

void M62_PrivateWidget1_1::setSoloState(bool state)
{

}

void M62_PrivateWidget1_1::setSoloStateLeft(bool state)
{

}

void M62_PrivateWidget1_1::setSoloStateRight(bool state)
{

}

void M62_PrivateWidget1_1::setMuteState(bool state)
{

}

void M62_PrivateWidget1_1::setMuteStateLeft(bool state)
{

}

void M62_PrivateWidget1_1::setMuteStateRight(bool state)
{

}

void M62_PrivateWidget1_1::setSoloClicked(bool state)
{

}

void M62_PrivateWidget1_1::setSoloClickedLeft(bool state)
{

}

void M62_PrivateWidget1_1::setSoloClickedRight(bool state)
{

}

void M62_PrivateWidget1_1::setMuteClicked(bool state)
{

}

void M62_PrivateWidget1_1::setMuteClickedLeft(bool state)
{

}

void M62_PrivateWidget1_1::setMuteClickedRight(bool state)
{

}

bool M62_PrivateWidget1_1::getSoloState()
{
    return false;
}

bool M62_PrivateWidget1_1::getSoloStateLeft()
{
    return false;
}

bool M62_PrivateWidget1_1::getSoloStateRight()
{
    return false;
}

bool M62_PrivateWidget1_1::getMuteState()
{
    return false;
}

bool M62_PrivateWidget1_1::getMuteStateLeft()
{
    return false;
}

bool M62_PrivateWidget1_1::getMuteStateRight()
{
    return false;
}

void M62_PrivateWidget1_1::loadSettings()
{

}
void M62_PrivateWidget1_1::AppSettingsChanged(QString objectName, QString attribute, QString value)
{
    Q_UNUSED(objectName);
    Q_UNUSED(attribute);
    Q_UNUSED(value);
}


// slot
void M62_PrivateWidget1_1::in_mTimer_timeout()
{
    ui->pushButtonClose->show();
}
void M62_PrivateWidget1_1::on_lineEdit_textChanged(const QString& arg1)
{
    QFont font=ui->lineEdit->font();
    font.setPointSize(9);
    if(!GLBFHandle.isSuitable(font, arg1, minimumWidth() - 10))
    {
        ui->lineEdit->setText(arg1.chopped(1));
    }
}
void M62_PrivateWidget1_1::on_lineEdit_editingFinished()
{
    if(ui->lineEdit->text().isEmpty())
    {
        ui->lineEdit->setText(getChannelName());
    }
    ui->lineEdit->clearFocus();
    emit attributeChanged("", "ChannelName", ui->lineEdit->text());
}
void M62_PrivateWidget1_1::on_pushButtonClose_clicked()
{
    emit attributeChanged(getChannelName(), "Hide", QString::number(static_cast<int>(true)));
    ui->pushButtonClose->hide();
}


// setter & getter
M62_PrivateWidget1_1& M62_PrivateWidget1_1::setName(QString name)
{
    setObjectName(name);
    WorkspaceObserver::setObserverName(name);
    AppSettingsObserver::setObserverName(name);
    return *this;
}
M62_PrivateWidget1_1& M62_PrivateWidget1_1::setFont(QFont font)
{
    mFont = font;
    ui->widgetVolumeMeter->setFont(font);
    QResizeEvent e(size(), size());
    resizeEvent(&e);
    update();
    return *this;
}
M62_PrivateWidget1_1& M62_PrivateWidget1_1::setVolumeMeterLeft(int value)
{
    ui->widgetVolumeMeter->setValueLeft(value);
    return *this;
}
M62_PrivateWidget1_1& M62_PrivateWidget1_1::setVolumeMeterRight(int value)
{
    ui->widgetVolumeMeter->setValueRight(value);
    return *this;
}

void M62_PrivateWidget1_1::setChannelName(const QString &name, bool isSendSig)
{
    if(!isSendSig){
        const QSignalBlocker blocker(ui->lineEdit);
        ui->lineEdit->setText(name);
    }else{
        ui->lineEdit->setText(name);
    }
}
