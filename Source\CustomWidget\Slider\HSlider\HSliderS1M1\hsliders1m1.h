#ifndef HSliderS1M1_H
#define HSliderS1M1_H

#include <QWidget>

class QLineEdit;
class HSliderS1M1 : public QWidget
{
    Q_OBJECT
public:
    explicit HSliderS1M1(QWidget *parent = nullptr);
    void setValue(float value, bool emitSignal = true);
    void setDefaultValue(float defaultValue);
    float value() const;
    void setFont(const QFont &font);

    void setBackgroundColor(const QColor &color);
    void setGrooveColor(const QColor &color);
    void setHandleColor(const QColor &color);
    void setTextColor(const QColor &color);
    void setRange(float min, float max);
    void setUnitValuePixels(int pixels) { m_unitValuePixels = pixels; }
    float minimum() const { return m_minValue; }
    float maximum() const { return m_maxValue; }
    float median() const { return (m_minValue + m_maxValue) / 2.0f; }

signals:
    void valueChanged(float value);

protected:
    void paintEvent(QPaintEvent *event) override;
    void mousePressEvent(QMouseEvent *event) override;
    void mouseMoveEvent(QMouseEvent *event) override;
    void mouseReleaseEvent(QMouseEvent *event) override;
    void mouseDoubleClickEvent(QMouseEvent* event) override;
    void wheelEvent(QWheelEvent *event) override;
    void keyPressEvent(QKeyEvent *event) override;
    void resizeEvent(QResizeEvent *event) override;
    bool eventFilter(QObject* o, QEvent* e)override;

private:
    void updateHandleRect();
    float valueFromPos(const QPoint &pos) const;
    QPoint posFromValue(float value) const;
    QString getTextFromValue(float value) const;
    float getValueFromText(const QString &text) const;
    bool updateValue(float value, bool emitSignal);
    
    QRectF m_grooveRect;
    QRectF m_handleRect;
    QRectF m_textRect;
    bool m_pressed = false;
    float m_value;
    float m_defaultValue;
    QFont m_font;
    float m_minValue = 0.f;
    float m_maxValue = 200.f;
    float m_handleWidth = 4.0f;
    int m_unitValuePixels = 2;

    QColor m_backgroundColor = QColor(60, 60, 60);
    QColor m_grooveColor = QColor(102, 102, 102);
    QColor m_handleColor = QColor(67, 207, 124);
    QColor m_textColor = QColor(67, 207, 124);
    QPoint m_mousePosRatio  = {0, 0};
    QPoint m_mousePos = {0, 0};

    QLineEdit* m_lineEdit;
};

#endif // HSliderS1M1_H
