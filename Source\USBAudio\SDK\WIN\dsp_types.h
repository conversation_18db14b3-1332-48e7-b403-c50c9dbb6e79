/************************************************************************
 *
 *  Module:       dsp_types.h
 *  Description:  data types and other defs used by DSP and driver code
 *
 *  Runtime Env.: kernel mode
 *
 *  Author(s):
 *    Udo E<PERSON>hardt,  <EMAIL>
 *  Company:
 *    Thesycon GmbH, Germany      http://www.thesycon.de
 *
 ************************************************************************/

#ifndef __dsp_types_h__
#define __dsp_types_h__


// max number of channels supported by the data path routed through the DSP plugin
#define DSP_MAX_CHANNELS    64


//
// generic sample, contains either PCM or FLOAT format
//
typedef int GenSample32b;
// idle value
#define GEN_SAMPLE_32b_IDLE   0


//
// Integer type the driver uses to represent a 32 bit LPCM sample.
// bit 31 = sign bit
//
typedef int LpcmSample32b;
// idle value
#define LPCM_SAMPLE_32b_IDLE   0
// max positive amplitude
#define LPCM_SAMPLE_32b_MAX    ((LpcmSample32b)0x7FFFFFFF)
// max negative amplitude
#define LPCM_SAMPLE_32b_MIN    ((LpcmSample32b)0x80000000)



//
// IEEE 754 float type, 32 bits
//
typedef float FloatSample32b;
// idle value
#define FLOAT_SAMPLE_32b_IDLE   0.0f


//
// 64-bit word that contains 64 DSD samples (bits)
//
typedef unsigned __int64 DsdSampleWord64b;



//
// sample formats reported by the device
//
enum DeviceSampleFormat
{
    SampleFormat_unknown = 0,
    SampleFormat_PCM8 = 8,        // PCM,  8 valid bits
    SampleFormat_PCM16 = 16,      // PCM, 16 valid bits
    SampleFormat_PCM24 = 24,      // PCM, 24 valid bits
    SampleFormat_PCM32 = 32,      // PCM, 32 valid bits
    SampleFormat_IEEE_FLOAT = 754 // IEEE 754 float, 32 bits
};



//
// We define a set of integer types of a known size one both compilers, MS and GNU (Mac)
//

// 64 bit size on both x86 and x64
typedef long long DSP_INT64;
typedef unsigned long long DSP_UINT64;

// 32 bit size on both x86 and x64
typedef int DSP_INT32;
typedef unsigned int DSP_UINT32;

// 16 bit size on both x86 and x64
typedef short DSP_INT16;
typedef unsigned int DSP_UINT16;

// 8 bit size on both x86 and x64
typedef char DSP_INT8;
typedef unsigned char DSP_UINT8;



//
// Q7.24 signed fixed point format
// useful for expressing ratios, mixer weights, filter coefficients, etc.
//
// -128.0 corresponds to 0x80.000000
//   -2.0 corresponds to 0xFE.000000
//   -1.0 corresponds to 0xFF.000000
//   -0.5 corresponds to 0xFF.800000
//    0.0 corresponds to 0x00.000000
//    0.5 corresponds to 0x00.800000
//    1.0 corresponds to 0x01.000000
//    2.0 corresponds to 0x02.000000
//  127.0 corresponds to 0x7f.000000
//
typedef int DSP_Q724;

// constants
#define DSP_Q724_ZERO         0             // 0.0
#define DSP_Q724_ONE          (1<<24)       // 1.0
#define DSP_Q724_MINUS_ONE    (-(1<<24))    // -1.0
#define DSP_Q724_FRACT_BITS   24            // size, in bits, of the fractional part



#endif // __dsp_types_h__

/******************************** EOF ***********************************/
