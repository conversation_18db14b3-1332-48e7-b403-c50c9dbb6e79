#ifndef VSLIDERS1M2_H
#define VSLIDERS1M2_H


#include <QFont>
#include <QEvent>
#include <QSlider>
#include <QObject>
#include <QWidget>
#include <QLineEdit>
#include <QResizeEvent>


class VSliderS1M2 : public QWidget
{
    Q_OBJECT
public:
    explicit VSliderS1M2(QWidget *parent=nullptr);
    ~VSliderS1M2();
    VSliderS1M2& setFont(QFont font);
    VSliderS1M2& setValue(float value);
    VSliderS1M2& setDefault(float value);
    VSliderS1M2& setRange(int valueStart, int valueEnd05, int valueEnd10, int valueEnd20);
    VSliderS1M2& setHeightRatio(int text, int space, int slider);
    float getValue() { return mValue; }
    float getDefault() { return mValueDefault; }
    int getMin() { return mValueEnd20; }
    int getMax() { return mValueStart; }
    VSliderS1M2& showInfinity(bool state=true);
    VSliderS1M2& showInfinitesimal(bool state=true);
protected:
    bool eventFilter(QObject* obj, QEvent* e) override;
    void resizeEvent(QResizeEvent* e) override;
private:
    QLineEdit mLineEdit;
    QSlider mSlider;
    QFont mFont;
    bool mShowInfinity=false;
    bool mShowInfinitesimal=false;
    bool mEmitOpen=false;
    float mValue=0;
    float mValueDefault=0;
    int mValueStart;
    int mValueEnd05;
    int mValueEnd10;
    int mValueEnd20;
    int mHText=8;
    int mHSpace=2;
    int mHSlider=90;
private slots:
    void in_mLineEdit_textChanged(const QString& text);
    void in_mLineEdit_editingFinished();
    void in_mSlider_valueChanged(int value);
signals:
    void valueChanged(float value);
};


#endif // VSLIDERS1M2_H

