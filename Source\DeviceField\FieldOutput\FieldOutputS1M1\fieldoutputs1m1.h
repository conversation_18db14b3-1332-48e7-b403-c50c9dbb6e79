#ifndef FIELDOUTPUTS1M1_H
#define FIELDOUTPUTS1M1_H


#include <QWidget>
#include <QVector>
#include <QString>

#include "workspace.h"
#include "outputbase.h"
#include "appsettings.h"
#include "fieldoutputbase1.h"


class FieldOutputS1M1 : public FieldOutputBase1, public WorkspaceObserver, public AppSettingsObserver
{
    Q_OBJECT
public:
    explicit FieldOutputS1M1(QWidget* parent=nullptr, QString name="");
    ~FieldOutputS1M1();
    FieldOutputS1M1& setName(QString name);
    FieldOutputS1M1& modifyWidgetList(QVector<OutputBase*> list);
    FieldOutputS1M1& setVisibleListDefault(QVector<OutputBase*> list);
protected:
    void loadSettings() override;
    void AppSettingsChanged(QString objectName, QString attribute, QString value) override;
private:
    QVector<OutputBase*> mWidgetList;
    QVariantList mVisibleListDefault;
private slots:
    void in_widgetBase_attributeChanged(QString objectName, QString attribute, QString value);
    void in_widgetList_attributeChanged(QString objectName, QString attribute, QString value);
signals:
    void attributeChanged(QString objectName, QString attribute, QString value);
};


#endif // FIELDOUTPUTS1M1_H

