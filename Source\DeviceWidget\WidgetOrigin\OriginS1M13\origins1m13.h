#ifndef ORIGINS1M13_H
#define ORIGINS1M13_H


#include <QFont>
#include <QEvent>
#include <QTimer>
#include <QObject>
#include <QWidget>
#include <QResizeEvent>

#include "workspace.h"
#include "originbase.h"
#include "appsettings.h"


namespace Ui {
class OriginS1M13;
}


class OriginS1M13 : public OriginBase, public WorkspaceObserver, public AppSettingsObserver
{
    Q_OBJECT
public:
    explicit OriginS1M13(QWidget* parent=nullptr, QString name="");
    ~OriginS1M13();
    OriginS1M13& setName(QString name);
    OriginS1M13& setFont(QFont font);
    OriginS1M13& setVolumeMeterLeft(int value);
    OriginS1M13& setVolumeMeterLeftClear();
    OriginS1M13& setVolumeMeterLeftSlip();
    OriginS1M13& setVolumeMeterRight(int value);
    OriginS1M13& setVolumeMeterRightClear();
    OriginS1M13& setVolumeMeterRightSlip();
    OriginS1M13& setGain(float value);
    OriginS1M13& setGainLock(bool state=true);
    OriginS1M13& setMuteAffectGain(bool state=true);
    OriginS1M13& setGainAffectMute(bool state=true);
    OriginS1M13& setGainRange(int valueStart, int valueEnd05, int valueEnd10, int valueEnd20);
    OriginS1M13& setGainDefault(float value);
    OriginS1M13& setGainWidgetDisable(float value);
    OriginS1M13& setChannelNameEditable(bool state=true);
    OriginS1M13& setValueGAIN(float value);
    OriginS1M13& setValueMUTE(bool state=true);
    OriginS1M13& setOverlay(bool state=true);
    OriginS1M13& addAudioSource(QString audioClass, QVector<QString>& audioSourceList);
protected:
    bool eventFilter(QObject* obj, QEvent* e) override;
    void resizeEvent(QResizeEvent* e) override;
    void updateAttribute() override;
    void loadSettings() override;
    void AppSettingsChanged(QString objectName, QString attribute, QString value) override;
private:
    Ui::OriginS1M13* ui;
    QTimer mTimer;
    QFont mFont;
    int mPreMUTE=-2147483648;
    float mPreGAIN=-2147483648;
    float mDisableGAIN=-88;
    bool mMuteAffectGain=false;
    bool mGainAffectMute=false;
    void save(QAnyStringView key, const QVariant& value);
private slots:
    void in_mTimer_timeout();
    void in_widgetLinkedVSlider_valueChanged(float value);
    void in_widgetPushButtonGroup1_stateChanged(QString button, QString state);
    void on_lineEdit_textChanged(const QString& arg1);
    void on_lineEdit_editingFinished();
    void on_pushButtonClose_clicked();
};


#endif // ORIGINS1M13_H

