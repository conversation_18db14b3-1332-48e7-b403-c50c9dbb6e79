#ifndef PUSHBUTTONGROUPS1M8_H
#define PUSHBUTTONGROUPS1M8_H


#include <QHash>
#include <QWidget>
#include <QResizeEvent>


namespace Ui {
class PushButtonGroupS1M8;
}


class PushButtonGroupS1M8 : public QWidget
{
    Q_OBJECT
public:
    explicit PushButtonGroupS1M8(QWidget* parent=nullptr);
    ~PushButtonGroupS1M8();
    PushButtonGroupS1M8& setFont(QFont font);
    PushButtonGroupS1M8& setLanguage(QString language);
    PushButtonGroupS1M8& setState(QString button, QString state, bool needEmit=true);
    QString getState(QString button);
protected:
    void resizeEvent(QResizeEvent* e) override;
    void showEvent(QShowEvent*) override { resizeEvent(nullptr); }
private:
    Ui::PushButtonGroupS1M8* ui;
    QFont mFont;
    unsigned int mBitmap=0;
    QHash<unsigned int, QString> mStyle;
private slots:
    void on_PushButtonMUTE_clicked(bool checked);
signals:
    void stateChanged(QString button, QString state);
};


#endif // PUSHBUTTONGROUPS1M8_H

