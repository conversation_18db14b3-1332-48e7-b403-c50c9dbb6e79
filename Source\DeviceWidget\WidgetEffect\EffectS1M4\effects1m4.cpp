#include "effects1m4.h"
#include "globalfont.h"
#include "ui_effects1m4.h"


EffectS1M4::EffectS1M4(QWidget* parent, QString name)
    : EffectBase(parent)
    , WorkspaceObserver(name)
    , AppSettingsObserver(name)
    , ui(new Ui::EffectS1M4)
{
    ui->setupUi(this);
    this->setObjectName(name);
    setAttribute(Qt::WA_StyledBackground, true);
    ui->rbWidget->setObjectName("rbWidget");
    ui->lineEditReverb->setDisabled(true);
    ui->dialDryWet->setRange(0, 100);
    ui->dialRoom->setRange(1, 6).showText(false).showCircle(false);
    ui->dialDecay->setRange(0, 18).showText(false).showCircle(false);
    auto setReverbType = [this](QString text){
        ui->pushButtonBypassReverb->setChecked(false);
        if(text != "Bypass"){
            mReverbTypePre = text;
        }
        save("ReverbType", text);
        updateReverbType(text);
        updateAttribute();
        setReverbIcon(mReverbType);
    };
    connect(ui->pushButtonReverbStudio, &QPushButton::clicked, this, [this, setReverbType](bool checked) {
        setReverbType("STUDIO");
    });
    connect(ui->pushButtonReverbLive, &QPushButton::clicked, this, [this, setReverbType](bool checked) {
        setReverbType("LIVE");
    });
    connect(ui->pushButtonReverbHall, &QPushButton::clicked, this, [this, setReverbType](bool checked) {
        setReverbType("HALL");
    });
    connect(ui->pushButtonBypassReverb, &QPushButton::clicked, this, [this, setReverbType](bool checked) {
        setReverbType(checked?"Bypass":mReverbTypePre);
        ui->pushButtonBypassReverb->setChecked(checked);
    });
    connect(ui->dialDryWet, &DialS1M5::valueChanged, this, [this](float value){
        save("DryWet", value);
        updateAttribute();
    });
    connect(ui->dialRoom, &DialS1M1::valueChanged, this, [this](float value){
        WorkspaceObserver::setValue(getValueReverbKey(mReverbType), value);
        QString key = "Room";
        if(isWidgetEmitAction())
        {
            emit attributeChanged(this->objectName(), "Save_" + key, QString::number(value));
        }
        updateAttribute();
    });
    connect(ui->dialDecay, &DialS1M1::valueChanged, this, [this](float value){
        save("Decay", value);
        updateAttribute();
    });
}
EffectS1M4::~EffectS1M4()
{
    delete ui;
}
EffectS1M4& EffectS1M4::setName(QString name)
{
    setObjectName(name);
    WorkspaceObserver::setObserverName(name);
    AppSettingsObserver::setObserverName(name);
    return *this;
}
EffectS1M4& EffectS1M4::setFont(QFont font)
{
    mFont = font;
    return *this;
}

void EffectS1M4::setReverbIcon(const QString& text){
    QString png;
    if(text == "STUDIO"){
        png = ":/Image/PushButtonGroup/PBG8_1.png";
    }else if(text == "LIVE"){
        png = ":/Image/PushButtonGroup/PBG8_5.png";
    }else if(text == "HALL"){
        png = ":/Image/PushButtonGroup/PBG8_8.png";
    }else if(text == "Bypass"){
        png = ":/Image/PushButtonGroup/PBG8_10.png";
    }
    ui->rbWidget->setStyleSheet(QString("QWidget#rbWidget{image:url(%1);}").arg(png));
}

EffectS1M4& EffectS1M4::setReverbType(QString type) {
    if(type!="Bypass"){
        mReverbTypePre=type;
    }
    ui->pushButtonBypassReverb->setChecked(false);
    mReverbType = type;
    if(type == "Bypass") {
        ui->pushButtonBypassReverb->setChecked(true);
    }
    WorkspaceObserver::setValue("ReverbType", mReverbType);
    updateReverbType(mReverbType);
    setReverbIcon(mReverbType);
    return *this;
}

EffectS1M4& EffectS1M4::setValueDryWet(float value)
{
    if(mPreDryWet != value) {
        mPreDryWet = value;
        ui->dialDryWet->setValue(value);
        save("DryWet", value);
    }
    return *this;
}

EffectS1M4& EffectS1M4::setValueRoom(float value)
{
    ui->dialRoom->setValue(value * 100);
    return *this;
}

QString EffectS1M4::getValueReverbKey(QString type)
{
    if(type == "STUDIO") {
        return "ReverbStudio";
    } else if(type == "LIVE") {
        return "ReverbLive";
    } else if(type == "HALL") {
        return "ReverbHall";
    } else if(type == "Bypass") {
        return "ReverbBypass";
    }
    return "";
}

int EffectS1M4::getValueReverb(QString type)
{
    auto key = getValueReverbKey(type);
    return WorkspaceObserver::value(key).toFloat();
}

EffectS1M4& EffectS1M4::updateReverbType(QString type)
{
    ui->dialRoom->setEnabled(true);
    ui->dialDryWet->setEnabled(true);
    ui->dialDecay->setEnabled(true);
    if(type == "Bypass") {
        ui->dialRoom->setEnabled(false);
        ui->dialDryWet->setEnabled(false);
        ui->dialDecay->setEnabled(false);
    }
    ui->dialRoom->setValue(getValueReverb(type));
    return *this;
}

EffectS1M4& EffectS1M4::setValueDecay(float value)
{
    if(mPreDecay != value) {
        mPreDecay = value;
        ui->dialDecay->setValue(static_cast<int>(value * 100));
    }
    return *this;
}

EffectS1M4& EffectS1M4::setReverbTitle(const QString& title) {
    ui->lineEditReverb->setText(title);
    return *this;
}

void EffectS1M4::resizeEvent(QResizeEvent* e) {
    Q_UNUSED(e);
    QRect widget3Rect = rect();
    int w3Width = widget3Rect.width();
    int w3Height = widget3Rect.height();
    ui->lineEditReverb->setGeometry(w3Width * 0, w3Width * 0, w3Width, w3Height * 0.08);
    ui->dialDryWet->setGeometry((w3Width - w3Height * 0.19) / 2 ,w3Height * 0.12, w3Height * 0.19, w3Height * 0.19);
    ui->dialDryWet->setFont(mFont);
    ui->labelDry->setGeometry(w3Width * 0.09, w3Height * 0.23, w3Width * 0.19, w3Height * 0.05);
    ui->labelWet->setGeometry(w3Width * 0.75, w3Height * 0.23, w3Width * 0.19, w3Height * 0.05);

    ui->labelRoomSize->setGeometry(w3Width * 0, w3Height * 0.34, w3Width, w3Height * 0.04);
    ui->dialRoom->setGeometry((w3Width - w3Height * 0.10) / 2 ,w3Height * 0.38, w3Height * 0.10, w3Height * 0.10);
    ui->labelReverbRoomSmall->setGeometry(w3Width * 0, w3Height * 0.44, w3Width * 0.5, w3Height * 0.04);
    ui->labelReverbRoomLarge->setGeometry(w3Width * 0.5, w3Height * 0.44, w3Width * 0.5, w3Height * 0.04);

    ui->labelDecayRate->setGeometry(w3Width * 0, w3Height * 0.508, w3Width, w3Height * 0.04);
    ui->dialDecay->setGeometry((w3Width - w3Height * 0.10) / 2 ,w3Height * 0.55, w3Height * 0.10, w3Height * 0.10);
    ui->labelReverbDecayMin->setGeometry(w3Width * 0, w3Height * 0.61, w3Width * 0.5, w3Height * 0.04);
    ui->labelReverbDecayMax->setGeometry(w3Width * 0.5, w3Height * 0.61, w3Width * 0.5, w3Height * 0.04);

    int h = w3Height * 0.342;
    int w = h/1.5;
    ui->rbWidget->setGeometry((w3Width - w) / 2, w3Height * 0.662, w, h);
    updateStyle();
}

void EffectS1M4::updateStyle(){
    auto setLabelFontSize = [=](QLabel* label, double factor = 1.0) {
        QFont font = mFont;
        font.setPointSize(GLBFHandle.getSuitablePointSize(mFont, label->text(), label->rect())*factor);
        label->setFont(font);
    };

    auto setTitleFontSize = [=](QWidget* widget) {
        mFont.setPointSize(GLBFHandle.getSuitablePointSize(mFont, widget->height()) - 3);
        if(mFont.pointSize() < 8)
        {
            mFont.setPointSize(mFont.pointSize());
        }
        else if(mFont.pointSize() < 12)
        {
            mFont.setPointSize(mFont.pointSize() - 1);
        }
        else if(mFont.pointSize() < 17)
        {
            mFont.setPointSize(mFont.pointSize() - 2);
        }
        else if(mFont.pointSize() < 22)
        {
            mFont.setPointSize(mFont.pointSize() - 3);
        }
        widget->setFont(mFont);
    };       

    setTitleFontSize(ui->lineEditReverb);
    setLabelFontSize(ui->labelDry);
    setLabelFontSize(ui->labelWet);
    setLabelFontSize(ui->labelRoomSize);
    setLabelFontSize(ui->labelReverbRoomSmall, 0.9);
    setLabelFontSize(ui->labelReverbRoomLarge, 0.9);
    setLabelFontSize(ui->labelDecayRate);
    setLabelFontSize(ui->labelReverbDecayMin, 0.9);
    setLabelFontSize(ui->labelReverbDecayMax, 0.9);

    QString style;
    style = QString("QWidget {"
            "   color: rgb(222, 222, 222);"
            "   background-color: #161616;"
            "   border-radius: 8px;"
            "}"
            "QLabel{"
            "   background-color: transparent;"
            "}"
            "QLineEdit {"
            "   color: rgb(161, 161, 161);"
            "   background-color: rgb(46, 46, 46);"
            "   border-top-left-radius: %1px; border-top-right-radius: %1px;border-bottom-left-radius:0px; border-bottom-right-radius:0px;"
            "   selection-color: rgb(0, 121, 107);"
            "   selection-background-color: rgb(224, 247, 250);"
            "}").arg(width() * 0.04);
    style += QString("QPushButton {"
            "   color: rgb(161, 161, 161);"
            "   background-color: transparent;"
            "}"
            "QPushButton:checked {"
            "   color: rgb(229, 229, 229);"
            "}");

    setStyleSheet(style);
}

void EffectS1M4::updateAttribute()
{
    if(isWidgetReady())
    {
        if(mPreDryWet != static_cast<int>(WorkspaceObserver::value("DryWet").toFloat()))
        {
            mPreDryWet = static_cast<int>(WorkspaceObserver::value("DryWet").toFloat());
            emit attributeChanged(this->objectName(), "DryWet", QString::number(mPreDryWet));
        }

        if(mReverbType != WorkspaceObserver::value("ReverbType").toString())
        {
            mReverbType = WorkspaceObserver::value("ReverbType").toString();
            emit attributeChanged(this->objectName(), "ReverbType", mReverbType);
        }

        auto valueRoom = getValueReverb(mReverbType);
        if(mReverbType == "STUDIO" && valueRoom != mReverbStudio) {
            mReverbStudio = valueRoom;
            emit attributeChanged(this->objectName(), "Room", QString::number(mReverbStudio));
        }
        else if(mReverbType == "LIVE" && valueRoom != mReverbLive) {
            mReverbLive = valueRoom;
            emit attributeChanged(this->objectName(), "Room", QString::number(mReverbLive));
        }
        else if(mReverbType == "HALL" && valueRoom != mReverbHall) {
            mReverbHall = valueRoom;
            emit attributeChanged(this->objectName(), "Room", QString::number(mReverbHall));
        }
        else if(mReverbType == "Bypass" && valueRoom != mReverbBypass) {
            mReverbBypass = valueRoom;
            emit attributeChanged(this->objectName(), "Room", QString::number(mReverbBypass));
        }

        if(mPreDecay != static_cast<int>(WorkspaceObserver::value("Decay").toFloat()))
        {
            mPreDecay = static_cast<int>(WorkspaceObserver::value("Decay").toFloat());
            emit attributeChanged(this->objectName(), "Decay", QString::number(mPreDecay));
        }
    }
}

void EffectS1M4::loadSettings()
{
    mReverbType = "";
    mPreDryWet = -2147483648;  
    mReverbStudio = -2147483648;
    mReverbLive = -2147483648;
    mReverbHall = -2147483648;
    mReverbBypass = -2147483648;
    mPreDecay = -2147483648;
    setWidgetReady(false);

    WorkspaceObserver::getSettings()->beginGroup(objectName());
    bool flag = WorkspaceObserver::getSettings()->contains(objectName());
    WorkspaceObserver::getSettings()->endGroup();

    if(!flag)
    {
        WorkspaceObserver::setValue(objectName(), true);
        WorkspaceObserver::setValue("ChannelName", getChannelName());
        WorkspaceObserver::setValue("DryWet", 30);
        WorkspaceObserver::setValue("ReverbStudio", 1);
        WorkspaceObserver::setValue("ReverbLive", 1);
        WorkspaceObserver::setValue("ReverbHall", 1);
        WorkspaceObserver::setValue("ReverbBypass", 1);
        WorkspaceObserver::setValue("Decay", 0);
        WorkspaceObserver::setValue("ReverbType", "Bypass");
    }

    ui->dialDryWet->setValue(WorkspaceObserver::value("DryWet").toFloat());
    ui->dialRoom->setValue(WorkspaceObserver::value("Room").toFloat());
    ui->dialDecay->setValue(WorkspaceObserver::value("Decay").toFloat());

    auto reverbType = WorkspaceObserver::value("ReverbType").toString();

    if(isWidgetEmitAction())
    {
        emit attributeChanged(this->objectName(), "Save_ChannelName", WorkspaceObserver::value("ChannelName").toString());
        emit attributeChanged(this->objectName(), "Save_DryWet", WorkspaceObserver::value("DryWet").toString());
        emit attributeChanged(this->objectName(), "Save_Room", QString::number(getValueReverb(reverbType)));
        emit attributeChanged(this->objectName(), "Save_Decay", WorkspaceObserver::value("Decay").toString());
        emit attributeChanged(this->objectName(), "Save_ReverbType", WorkspaceObserver::value("ReverbType").toString());
    }

    setWidgetReady(true);
    updateAttribute();
    setReverbType(reverbType);
    if(reverbType == "Bypass"){
        reverbType = "STUDIO";
    }
    mReverbTypePre = reverbType;
}

void EffectS1M4::AppSettingsChanged(QString objectName, QString attribute, QString value)
{

}

void EffectS1M4::save(const QString& key, const QVariant& value)
{
    WorkspaceObserver::setValue(key, value);
    if(isWidgetEmitAction())
    {
        emit attributeChanged(this->objectName(), "Save_" + key, value.typeId() == QMetaType::Bool ? (QString::number(value.toBool())) : (value.toString()));
    }
}