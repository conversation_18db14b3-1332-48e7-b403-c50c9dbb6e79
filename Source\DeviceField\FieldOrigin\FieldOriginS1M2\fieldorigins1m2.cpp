#include "fieldorigins1m2.h"


FieldOriginS1M2::FieldOriginS1M2(QWidget* parent, QString name)
    : FieldOriginBase3(parent)
    , WorkspaceObserver(name)
    , AppSettingsObserver(name)
{
    connect(this, &FieldOriginBase3::attributeChanged, this, &FieldOriginS1M2::in_widgetBase_attributeChanged, Qt::UniqueConnection);
}
FieldOriginS1M2::~FieldOriginS1M2()
{

}


// override
void FieldOriginS1M2::loadSettings()
{
    WorkspaceObserver::getSettings()->beginGroup(objectName());
    bool flag=WorkspaceObserver::getSettings()->contains(objectName());
    WorkspaceObserver::getSettings()->endGroup();
    if(!flag)
    {
        WorkspaceObserver::setValue(objectName(), true);
        WorkspaceObserver::setValue("Visible", mVisibleListDefault);
    }
    QVariantList visibleList=WorkspaceObserver::value("Visible").toList();
    QVector<QString> list;
    for(auto element : visibleList)
    {
        list.append(element.toString());
    }
    setVisibleList(list);
}
void FieldOriginS1M2::AppSettingsChanged(QString objectName, QString attribute, QString value)
{
    Q_UNUSED(objectName);
    if(attribute == "Language")
    {
        setFieldTitle(mLanguageContainer.value(value).value("FieldTitle"));
    }
}


// slot
void FieldOriginS1M2::in_widgetBase_attributeChanged(QString objectName, QString attribute, QString value)
{
    if(attribute == "Visible")
    {
        if(value.toInt())
        {
            QVariantList visibleList=WorkspaceObserver::value("Visible").toList();
            visibleList.append(QVariant(objectName));
            WorkspaceObserver::setValue("Visible", visibleList);
        }
        else
        {
            QVariantList visibleList=WorkspaceObserver::value("Visible").toList();
            visibleList.removeOne(QVariant(objectName));
            WorkspaceObserver::setValue("Visible", visibleList);
        }
    }
}
void FieldOriginS1M2::in_widgetList_attributeChanged(QString objectName, QString attribute, QString value)
{
    Q_UNUSED(objectName);
    Q_UNUSED(attribute);
    Q_UNUSED(value);
}


// setter & getter
FieldOriginS1M2& FieldOriginS1M2::setName(QString name)
{
    setObjectName(name);
    WorkspaceObserver::setObserverName(name);
    AppSettingsObserver::setObserverName(name);
    return *this;
}
FieldOriginS1M2& FieldOriginS1M2::registerLanguage(QHash<QString, QHash<QString, QString>>& language)
{
    mLanguageContainer = language;
    return *this;
}
FieldOriginS1M2& FieldOriginS1M2::modifyWidgetList(QVector<OriginBase*> list)
{
    mWidgetList = list;
    for(auto element : list)
    {
        element->setWidgetMovable(false);
        connect(element, SIGNAL(attributeChanged(QString, QString, QString)), this, SLOT(in_widgetList_attributeChanged(QString, QString, QString)), Qt::UniqueConnection);
    }
    FieldOriginBase3::modifyWidgetList(list);
    return *this;
}
FieldOriginS1M2& FieldOriginS1M2::setVisibleListDefault(QVector<OriginBase*> list)
{
    for(auto widget : list)
    {
        mVisibleListDefault.append(QVariant(widget->getChannelName()));
    }
    return *this;
}

