#include "globalfont.h"
#include "messageboxs2m1.h"

MessageBoxS2M1_WidgetS2M1::MessageBoxS2M1_WidgetS2M1(QWidget* parent)
    : QWidget(parent)
{
    mRadioButton1.setParent(this);
    mRadioButton2.setParent(this);
    mRadioButton3.setParent(this);
    mPushButtonYes.setParent(this);
    mPushButtonNo.setParent(this);

    mButtonGroup.addButton(&mRadioButton1, Option1);
    mButtonGroup.addButton(&mRadioButton2, Option2);
    mButtonGroup.addButton(&mRadioButton3, Option3);
    mRadioButton3.setChecked(true);

    setColorTextRadio1(QColor(0, 0, 0));
    setColorTextRadio2(QColor(0, 0, 0));
    setColorTextRadio3(QColor(0, 0, 0));
    setColorTextButtonYes(QColor(0, 0, 0), QColor(166, 166, 166));
    setColorTextButtonNo(QColor(0, 0, 0), QColor(166, 166, 166));

    connect(&mPushButtonYes, SIGNAL(clicked()), this, SLOT(in_mPushButtonAll_clicked()), Qt::UniqueConnection);
    connect(&mPushButtonNo, SIGNAL(clicked()), this, SLOT(in_mPushButtonAll_clicked()), Qt::UniqueConnection);
}

void MessageBoxS2M1_WidgetS2M1::resizeEvent(QResizeEvent* e)
{
    Q_UNUSED(e);
    // W
    float wPixelPerRatio = size().width() / 100.0;
    int wSpace1 = wPixelPerRatio * 5;
    int wSpace2 = wPixelPerRatio * 30;
    int wButton = (size().width() - wSpace1 * 2 - wSpace2) / 2;
    int xButtonYes = wSpace1;
    int xButtonNo = xButtonYes + wButton + wSpace2;

    // H
    float hPixelPerRatio = size().height() / 100.0;
    int hSpace1 = hPixelPerRatio * 10;
    int hSpace2 = hPixelPerRatio * 5;
    int hItem = (size().height() - hSpace1 * 2 - hSpace2 * 3) / 4;
    int radioSize = hItem*0.6;

    mRadioButton1.setGeometry(wSpace1, hSpace1, size().width() - 2 * wSpace1, hItem);
    mRadioButton2.setGeometry(wSpace1, hSpace1 + hItem + hSpace2, size().width() - 2 * wSpace1, hItem);
    mRadioButton3.setGeometry(wSpace1, hSpace1 + hItem * 2 + hSpace2 * 2, size().width() - 2 * wSpace1, hItem);
    
    mPushButtonYes.setGeometry(xButtonYes, hSpace1 + hItem * 3 + hSpace2 * 3, wButton, hItem);
    mPushButtonNo.setGeometry(xButtonNo, hSpace1 + hItem * 3 + hSpace2 * 3, wButton, hItem);

    mFont.setPointSize(GLBFHandle.getSuitablePointSize(mFont, mRadioButton1.text(), QRect(0, 0, mRadioButton1.width() - radioSize-10, hItem)));
    mRadioButton1.setFont(mFont);
    mRadioButton2.setFont(mFont);
    mRadioButton3.setFont(mFont);
    mPushButtonYes.setFont(mFont);
    mPushButtonNo.setFont(mFont);

    QString radioStyle = QString(
        "QRadioButton::indicator {"
        "   width: %1px;"
        "   height: %1px;"
        "}"
        "QRadioButton::indicator::unchecked {"
        "   border-radius: %2px;"
        "   background-color: #fff;"
        "}"
        "QRadioButton::indicator::checked {"
        "   border-radius: %2px;"
        "   background-color: #666;"
        "}")
        .arg(radioSize)
        .arg(radioSize / 2);

    mRadioButton1.setStyleSheet(radioStyle);
    mRadioButton2.setStyleSheet(radioStyle);
    mRadioButton3.setStyleSheet(radioStyle);
}

void MessageBoxS2M1_WidgetS2M1::in_mPushButtonAll_clicked()
{
    QPushButton* button = qobject_cast<QPushButton*>(sender());
    if(button == &mPushButtonYes)
    {
        emit buttonClicked(getSelectedRadioIndex());
    }
    else if(button == &mPushButtonNo)
    {
        emit buttonClicked(None);
    }
}

MessageBoxS2M1_WidgetS2M1::Result MessageBoxS2M1_WidgetS2M1::getSelectedRadioIndex() const
{
    return (Result)mButtonGroup.checkedId();
}

// setter methods
MessageBoxS2M1_WidgetS2M1& MessageBoxS2M1_WidgetS2M1::setFont(QFont font)
{
    mFont = font;
    QResizeEvent e(size(), size());
    resizeEvent(&e);
    update();
    return *this;
}

MessageBoxS2M1_WidgetS2M1& MessageBoxS2M1_WidgetS2M1::setTextRadio1(QString text)
{
    mRadioButton1.setText(text);
    return *this;
}

MessageBoxS2M1_WidgetS2M1& MessageBoxS2M1_WidgetS2M1::setTextRadio2(QString text)
{
    mRadioButton2.setText(text);
    return *this;
}

MessageBoxS2M1_WidgetS2M1& MessageBoxS2M1_WidgetS2M1::setTextRadio3(QString text)
{
    mRadioButton3.setText(text);
    return *this;
}

MessageBoxS2M1_WidgetS2M1& MessageBoxS2M1_WidgetS2M1::setTextButtonYes(QString text)
{
    mPushButtonYes.setText(text);
    return *this;
}

MessageBoxS2M1_WidgetS2M1& MessageBoxS2M1_WidgetS2M1::setTextButtonNo(QString text)
{
    mPushButtonNo.setText(text);
    return *this;
}

MessageBoxS2M1_WidgetS2M1& MessageBoxS2M1_WidgetS2M1::setColorTextRadio1(QColor color)
{
    QString style = QString("QRadioButton { color: %1; }").arg(color.name());
    mRadioButton1.setStyleSheet(style);
    return *this;
}

MessageBoxS2M1_WidgetS2M1& MessageBoxS2M1_WidgetS2M1::setColorTextRadio2(QColor color)
{
    QString style = QString("QRadioButton { color: %1; }").arg(color.name());
    mRadioButton2.setStyleSheet(style);
    return *this;
}

MessageBoxS2M1_WidgetS2M1& MessageBoxS2M1_WidgetS2M1::setColorTextRadio3(QColor color)
{
    QString style = QString("QRadioButton { color: %1; }").arg(color.name());
    mRadioButton3.setStyleSheet(style);
    return *this;
}

MessageBoxS2M1_WidgetS2M1& MessageBoxS2M1_WidgetS2M1::setColorTextButtonYes(QColor color, QColor colorBG)
{
    QString style = QString("QPushButton {"
                          "    color: %1;"
                          "    background-color: %2;"
                          "}"
                          "QPushButton:hover {"
                          "    border: 2px solid gray;"
                          "    border-radius: 3px;"
                          "}").arg(color.name()).arg(colorBG.name());
    mPushButtonYes.setStyleSheet(style);
    return *this;
}

MessageBoxS2M1_WidgetS2M1& MessageBoxS2M1_WidgetS2M1::setColorTextButtonNo(QColor color, QColor colorBG)
{
    QString style = QString("QPushButton {"
                          "    color: %1;"
                          "    background-color: %2;"
                          "}"
                          "QPushButton:hover {"
                          "    border: 2px solid gray;"
                          "    border-radius: 3px;"
                          "}").arg(color.name()).arg(colorBG.name());
    mPushButtonNo.setStyleSheet(style);
    return *this;
}

// MessageBoxS2M1 implementation
MessageBoxS2M1::MessageBoxS2M1(FramelessWindow* parent)
    : FramelessWindow(parent)
{
    mWidget.setParent(this);
    setCentralWidget(&mWidget);
    setHeightRatio(2, 10);
    setColorTitle(QColor(0, 0, 0), QColor(166, 166, 166));
    setColorBody(QColor(204, 204, 204));
    connect(&mWidget, SIGNAL(buttonClicked(int)), this, SLOT(in_mWidget_buttonClicked(int)), Qt::UniqueConnection);
}

void MessageBoxS2M1::in_mWidget_buttonClicked(int index)
{
    done(index);
}

MessageBoxS2M1& MessageBoxS2M1::setFont(QFont font)
{
    mWidget.setFont(font);
    FramelessWindow::setFont(font);
    return *this;
}

MessageBoxS2M1::Result  MessageBoxS2M1::getSelectedRadioIndex() const
{
    return (MessageBoxS2M1::Result)mWidget.getSelectedRadioIndex();
}

// setter methods for MessageBoxS2M1
MessageBoxS2M1& MessageBoxS2M1::setTextRadio1(QString text)
{
    mWidget.setTextRadio1(text);
    return *this;
}

MessageBoxS2M1& MessageBoxS2M1::setTextRadio2(QString text)
{
    mWidget.setTextRadio2(text);
    return *this;
}

MessageBoxS2M1& MessageBoxS2M1::setTextRadio3(QString text)
{
    mWidget.setTextRadio3(text);
    return *this;
}

MessageBoxS2M1& MessageBoxS2M1::setTextButtonYes(QString text)
{
    mWidget.setTextButtonYes(text);
    return *this;
}

MessageBoxS2M1& MessageBoxS2M1::setTextButtonNo(QString text)
{
    mWidget.setTextButtonNo(text);
    return *this;
}

MessageBoxS2M1& MessageBoxS2M1::setColorTitle(QColor color, QColor colorBG)
{
    setTitleColor(color);
    setTitleBackground(colorBG);
    return *this;
}

MessageBoxS2M1& MessageBoxS2M1::setColorBody(QColor color)
{
    QString style = QString("QWidget { background-color: %1; }").arg(color.name());
    setStyleSheet(style);
    return *this;
}

MessageBoxS2M1& MessageBoxS2M1::setColorTextRadio1(QColor color)
{
    mWidget.setColorTextRadio1(color);
    return *this;
}

MessageBoxS2M1& MessageBoxS2M1::setColorTextRadio2(QColor color)
{
    mWidget.setColorTextRadio2(color);
    return *this;
}

MessageBoxS2M1& MessageBoxS2M1::setColorTextRadio3(QColor color)
{
    mWidget.setColorTextRadio3(color);
    return *this;
}

MessageBoxS2M1& MessageBoxS2M1::setColorTextButtonYes(QColor color, QColor colorBG)
{
    mWidget.setColorTextButtonYes(color, colorBG);
    return *this;
}

MessageBoxS2M1& MessageBoxS2M1::setColorTextButtonNo(QColor color, QColor colorBG)
{
    mWidget.setColorTextButtonNo(color, colorBG);
    return *this;
}