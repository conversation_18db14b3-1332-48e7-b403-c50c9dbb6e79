/************************************************************************

    Description:
        project-specific status code definitions

    Author(s):
        <PERSON>
        
    Thesycon Software Solutions GmbH & Co. KG, Germany, www.thesycon.de

************************************************************************/

// This file must be included by tlstatus_codes.h only and cannot be used directly.
#ifndef __in_tlstatus_codes_h__
#error This file is an extension to tlstatus_codes.h and must not be included otherwise.
#endif


///////////////////////////////////////////////////////////////////
//
// Project-specific status codes
//
// A set of project-specific status codes may be defined per project.
// Use TLSTATUS_BASE_EX+x, x ranges from 0x000..0xFFF.
//
///////////////////////////////////////////////////////////////////

#define       TLSTATUS_DFU_STATUS_BASE      TLSTATUS_CODE(TLSTATUS_BASE_EX+0x100)
case_TLSTATUS_(TLSTATUS_DFU_STATUS_BASE, "Base value for a status code generated from the 1-byte DFU bStatus.")

#define       TLSTATUS_DFU_STATUS_ERR_TARGET      (TLSTATUS_DFU_STATUS_BASE + 1)
case_TLSTATUS_(TLSTATUS_DFU_STATUS_ERR_TARGET, "DFU status: errTARGET.")

#define       TLSTATUS_DFU_STATUS_ERR_FILE      (TLSTATUS_DFU_STATUS_BASE + 2)
case_TLSTATUS_(TLSTATUS_DFU_STATUS_ERR_FILE, "DFU status: errFILE.")

#define       TLSTATUS_DFU_STATUS_ERR_WRITE      (TLSTATUS_DFU_STATUS_BASE + 3)
case_TLSTATUS_(TLSTATUS_DFU_STATUS_ERR_WRITE, "DFU status: errWRITE.")

#define       TLSTATUS_DFU_STATUS_ERR_ERASE      (TLSTATUS_DFU_STATUS_BASE + 4)
case_TLSTATUS_(TLSTATUS_DFU_STATUS_ERR_ERASE, "DFU status: errERASE.")

#define       TLSTATUS_DFU_STATUS_ERR_CHECK_ERASED      (TLSTATUS_DFU_STATUS_BASE + 5)
case_TLSTATUS_(TLSTATUS_DFU_STATUS_ERR_CHECK_ERASED, "DFU status: errCHECK_ERASED.")

#define       TLSTATUS_DFU_STATUS_ERR_PROG      (TLSTATUS_DFU_STATUS_BASE + 6)
case_TLSTATUS_(TLSTATUS_DFU_STATUS_ERR_PROG, "DFU status: errPROG.")

#define       TLSTATUS_DFU_STATUS_ERR_VERIFY      (TLSTATUS_DFU_STATUS_BASE + 7)
case_TLSTATUS_(TLSTATUS_DFU_STATUS_ERR_VERIFY, "DFU status: errVERIFY.")

#define       TLSTATUS_DFU_STATUS_ERR_ADDRESS      (TLSTATUS_DFU_STATUS_BASE + 8)
case_TLSTATUS_(TLSTATUS_DFU_STATUS_ERR_ADDRESS, "DFU status: errADDRESS.")

#define       TLSTATUS_DFU_STATUS_ERR_NOTDONE      (TLSTATUS_DFU_STATUS_BASE + 9)
case_TLSTATUS_(TLSTATUS_DFU_STATUS_ERR_NOTDONE, "DFU status: errNOTDONE.")

#define       TLSTATUS_DFU_STATUS_ERR_FIRMWARE      (TLSTATUS_DFU_STATUS_BASE + 10)
case_TLSTATUS_(TLSTATUS_DFU_STATUS_ERR_FIRMWARE, "DFU status: errFIRMWARE.")

#define       TLSTATUS_DFU_STATUS_ERR_VENDOR      (TLSTATUS_DFU_STATUS_BASE + 11)
case_TLSTATUS_(TLSTATUS_DFU_STATUS_ERR_VENDOR, "DFU status: errVENDOR.")

#define       TLSTATUS_DFU_STATUS_ERR_USBR      (TLSTATUS_DFU_STATUS_BASE + 12)
case_TLSTATUS_(TLSTATUS_DFU_STATUS_ERR_USBR, "DFU status: errUSBR.")

#define       TLSTATUS_DFU_STATUS_ERR_POR      (TLSTATUS_DFU_STATUS_BASE + 13)
case_TLSTATUS_(TLSTATUS_DFU_STATUS_ERR_POR, "DFU status: errPOR.")

#define       TLSTATUS_DFU_STATUS_ERR_UNKNOWN      (TLSTATUS_DFU_STATUS_BASE + 14)
case_TLSTATUS_(TLSTATUS_DFU_STATUS_ERR_UNKNOWN, "DFU status: errUNKNOWN.")

#define       TLSTATUS_DFU_STATUS_ERR_STALLEDPKT      (TLSTATUS_DFU_STATUS_BASE + 15)
case_TLSTATUS_(TLSTATUS_DFU_STATUS_ERR_STALLEDPKT, "DFU status: errSTALLEDPKT.")



///////////////////////////////////////////////////////////////////
//
// Application-specific status codes
//
// A set of application-specific status codes may be defined per project or application.
// Use TLSTATUS_BASE_APP+x, x ranges from 0x000..0xFFF.
//
///////////////////////////////////////////////////////////////////

//#define        TLSTATUS_APP_ERROR1          TLSTATUS_CODE(TLSTATUS_BASE_APP+0x001)
//case_TLSTATUS_(TLSTATUS_APP_ERROR1, "Error 1 occurred.")




/*************************** EOF **************************************/
