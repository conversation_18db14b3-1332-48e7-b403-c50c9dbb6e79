#ifndef PUSHBUTTONS1M1_H
#define PUSHBUTTONS1M1_H


#include <QLabel>
#include <QWidget>
#include <QPushButton>
#include <QResizeEvent>


class PushButtonS1M1 : public QWidget
{
    Q_OBJECT
public:
    explicit PushButtonS1M1(QWidget* parent=nullptr);
    ~PushButtonS1M1();
    enum ButtonID
    {
        buttonMic1=0,
        buttonMic35,
        buttonMicHP,
        button48V,
        buttonAUTO,
        buttonDucking
    };
    PushButtonS1M1& setFont(QFont font);
    PushButtonS1M1& setPushButtonStateMic1(bool state);
    PushButtonS1M1& setPushButtonStateMic35(bool state);
    PushButtonS1M1& setPushButtonStateMicHP(bool state);
    PushButtonS1M1& setPushButtonState48V(bool state);
    PushButtonS1M1& setPushButtonStateAUTO(bool state);
    PushButtonS1M1& setPushButtonStateDucking(bool state);
    bool getPushButtonStateMic1();
    bool getPushButtonStateMic35();
    bool getPushButtonStateMicHP();
    bool getPushButtonState48V();
    bool getPushButtonStateAUTO();
    bool getPushButtonStateDucking();
protected:
    void resizeEvent(QResizeEvent* e) override;
private:
    QFont mFont;
    bool mPushButtonStateMic1=false;
    bool mPushButtonStateMic35=false;
    bool mPushButtonStateMicHP=false;
    bool mPushButtonState48V=false;
    bool mPushButtonStateAUTO=false;
    bool mPushButtonStateDucking=false;
    QPushButton mPushButtonMic1;
    QPushButton mPushButtonMic35;
    QPushButton mPushButtonMicHP;
    QPushButton mPushButton48V;
    QPushButton mPushButtonAUTO;
    QPushButton mPushButtonDucking;
    QLabel mLabelMic1;
    QLabel mLabelMic35;
    QLabel mLabelMicHP;
    int mRadius=0;
private slots:
    void in_mPushButtonMic1_clicked();
    void in_mPushButtonMic35_clicked();
    void in_mPushButtonMicHP_clicked();
    void in_mPushButton48V_clicked();
    void in_mPushButtonAUTO_clicked();
    void in_mPushButtonDucking_clicked();
signals:
    void buttonStateChanged(ButtonID button, bool state);
};


#endif // PUSHBUTTONS1M1_H

