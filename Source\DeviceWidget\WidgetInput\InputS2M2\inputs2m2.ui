<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>InputS2M2</class>
 <widget class="QWidget" name="InputS2M2">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>226</width>
    <height>60</height>
   </rect>
  </property>
  <property name="sizePolicy">
   <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
    <horstretch>0</horstretch>
    <verstretch>0</verstretch>
   </sizepolicy>
  </property>
  <property name="minimumSize">
   <size>
    <width>226</width>
    <height>40</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QGridLayout" name="gridLayout">
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <property name="spacing">
    <number>0</number>
   </property>
   <item row="0" column="0">
    <widget class="QFrame" name="frame">
     <property name="styleSheet">
      <string notr="true"/>
     </property>
     <property name="frameShape">
      <enum>QFrame::Shape::StyledPanel</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Shadow::Raised</enum>
     </property>
     <widget class="QLineEdit" name="lineEdit">
      <property name="geometry">
       <rect>
        <x>0</x>
        <y>10</y>
        <width>41</width>
        <height>41</height>
       </rect>
      </property>
      <property name="sizePolicy">
       <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="styleSheet">
       <string notr="true"/>
      </property>
      <property name="text">
       <string>AUX</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignmentFlag::AlignCenter</set>
      </property>
     </widget>
     <widget class="QPushButton" name="buttonMute">
      <property name="geometry">
       <rect>
        <x>40</x>
        <y>20</y>
        <width>41</width>
        <height>21</height>
       </rect>
      </property>
      <property name="text">
       <string>MUTE</string>
      </property>
      <property name="checkable">
       <bool>true</bool>
      </property>
     </widget>
     <widget class="HSliderS2M1" name="slider" native="true">
      <property name="geometry">
       <rect>
        <x>110</x>
        <y>17</y>
        <width>101</width>
        <height>16</height>
       </rect>
      </property>
     </widget>
     <widget class="VolumeMeterS2M1" name="volumeBottom" native="true">
      <property name="geometry">
       <rect>
        <x>110</x>
        <y>37</y>
        <width>101</width>
        <height>16</height>
       </rect>
      </property>
     </widget>
     <widget class="VolumeMeterS2M1" name="volumeTop" native="true">
      <property name="geometry">
       <rect>
        <x>110</x>
        <y>0</y>
        <width>101</width>
        <height>16</height>
       </rect>
      </property>
     </widget>
    </widget>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>HSliderS2M1</class>
   <extends>QWidget</extends>
   <header location="global">hsliders2m1.h</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>VolumeMeterS2M1</class>
   <extends>QWidget</extends>
   <header location="global">volumemeters2m1.h</header>
   <container>1</container>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
