#ifndef DEVICECONNECTORVIEWS1M1_H
#define DEVICECONNECTORVIEWS1M1_H


#include <QFont>
#include <QWidget>
#include <QResizeEvent>

#include "deviceconnectorviewbase.h"


namespace Ui {
class DeviceConnectorViewS1M1;
}


class DeviceConnectorViewS1M1 : public DeviceConnectorViewBase
{
    Q_OBJECT
public:
    explicit DeviceConnectorViewS1M1(QWidget* parent=nullptr);
    ~DeviceConnectorViewS1M1();
    void setFont(QFont font) override;
    void setLanguage(QString language) override;
    void modifyVersion(QString current, QString updatable) override;
    void modifyUpdateMessage(QString text) override;
    void modifyUpdateProgress(QString text) override;
    void modifyUpdateProgress(int progress) override;
    int currentPage() override;
    void showPage(int page) override;
protected:
    void resizeEvent(QResizeEvent* e) override;
private:
    Ui::DeviceConnectorViewS1M1 *ui;
    QFont mFont;
    QString mLanguage="";
private slots:
    void on_Page6PushButton1_clicked();
    void on_Page6PushButton2_clicked();
    void on_Page6CheckBox_checkStateChanged(const Qt::CheckState &arg1);
    void on_Page7PushButton1_clicked();
    void on_Page7PushButton2_clicked();
    void on_Page7CheckBox_checkStateChanged(const Qt::CheckState &arg1);
    void on_Page9PushButton1_clicked();
    void on_Page9PushButton2_clicked();
    void on_Page9CheckBox_checkStateChanged(const Qt::CheckState &arg1);
    void on_PageAPushButton1_clicked();
    void on_PageAPushButton2_clicked();
    void on_PageBPushButton_clicked();
    void on_PageCPushButton_clicked();
};


#endif // DEVICECONNECTORVIEWS1M1_H

