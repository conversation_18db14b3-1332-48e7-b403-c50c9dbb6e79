/************************************************************************
 *
 *  Module:       tusbaudio_defs.h
 *
 *  Description:
 *    Types and definitions for the programming interface provided by the
 *    device driver.
 *    This file conforms to C standard. It can be used by C and C++ code.
 *
 *  Runtime Env.:
 *    Windows Kernel, Win32
 *
 *  Company:
 *    Thesycon GmbH, Germany      http://www.thesycon.de
 *
 ************************************************************************/

#ifndef __tusbaudio_defs_h__
#define __tusbaudio_defs_h__

// struct alignment = 1 byte
#include <pshpack1.h>


// max length, in chars, of strings reported by the device, including terminating null
#define TUSBAUDIO_MAX_STRDESC_STRLEN              128

// maximum length of display name strings, including terminating null
#define TUSBAUDIO_MAX_NAME_STRING_LENGTH          TUSBAUDIO_MAX_STRDESC_STRLEN

// maximum length of sound device profile names, including terminating null
#define TUSBAUDIO_MAX_SD_PROFILE_NAME_STRING_LENGTH         64

// max length, in bytes, of the data field that may be attached to a Notification Event
#define TUSBAUDIO_NOTIFY_EVENT_MAX_DATA_BYTES     64


// Unique identifier for an object.
// To get fixed struct sizes at the API level, the integer is always 64 bit.
typedef unsigned __int64 TUsbAudioObjectId;


// ID type used by for ASIO instances
typedef int TUsbAudioASIOInstanceID;

#define TUSBAUDIO_INVALID_ASIO_INSTANCE_ID (-1)


//
// Notification Event categories
//
// note: values can be combined bitwise OR
//
#define TUSBAUDIO_NOTIFY_CATEGORY_NONE                      0x00000000U
#define TUSBAUDIO_NOTIFY_CATEGORY_ALL                       0xFFFFFFFFU
#define TUSBAUDIO_NOTIFY_CATEGORY_SAMPLE_RATE_CHANGE        (1u<<0)
#define TUSBAUDIO_NOTIFY_CATEGORY_STREAM_CHANGE             (1u<<1)
#define TUSBAUDIO_NOTIFY_CATEGORY_VOLUME_CHANGE             (1u<<2)
#define TUSBAUDIO_NOTIFY_CATEGORY_STREAMING_STATE_CHANGE    (1u<<3)
#define TUSBAUDIO_NOTIFY_CATEGORY_CLIENT_CHANGE             (1u<<4)
#define TUSBAUDIO_NOTIFY_CATEGORY_ASIO_BUFFER_SIZE_CHANGE   (1u<<5)
#define TUSBAUDIO_NOTIFY_CATEGORY_CLOCK_SOURCE              (1u<<6)
#define TUSBAUDIO_NOTIFY_CATEGORY_AC_NODE_INTERRUPT         (1u<<16)
#define TUSBAUDIO_NOTIFY_CATEGORY_CUSTOM_EVENT              (1u<<20)  // custom events


//
// Notification Events
//
typedef enum tagTUsbAudioNotifyEvent
{
    NotifyEvent_SampleRateChanged = 1,    // category = TUSBAUDIO_NOTIFY_CATEGORY_SAMPLE_RATE_CHANGE

    NotifyEvent_StreamFormatChanged,      // category = TUSBAUDIO_NOTIFY_CATEGORY_STREAM_CHANGE

    NotifyEvent_VolumeChanged,            // category = TUSBAUDIO_NOTIFY_CATEGORY_VOLUME_CHANGE
    NotifyEvent_MuteChanged,              // category = TUSBAUDIO_NOTIFY_CATEGORY_VOLUME_CHANGE

    NotifyEvent_StreamingStateChanged,    // category = TUSBAUDIO_NOTIFY_CATEGORY_STREAMING_STATE_CHANGE

    NotifyEvent_ClientChanged,            // category = TUSBAUDIO_NOTIFY_CATEGORY_CLIENT_CHANGE

    NotifyEvent_AsioBufferSizeChanged,    // category = TUSBAUDIO_NOTIFY_CATEGORY_ASIO_BUFFER_SIZE_CHANGE

    NotifyEvent_ClockSourceStatusChanged,  // category = TUSBAUDIO_NOTIFY_CATEGORY_CLOCK_SOURCE
    NotifyEvent_CurrentClockSourceChanged, // category = TUSBAUDIO_NOTIFY_CATEGORY_CLOCK_SOURCE

    NotifyEvent_AcNodeInterrupt = 100,     // category = TUSBAUDIO_NOTIFY_CATEGORY_AC_NODE_INTERRUPT
    NotifyEvent_AcNodeInterruptUAC1,       // category = TUSBAUDIO_NOTIFY_CATEGORY_AC_NODE_INTERRUPT

    NotifyEvent_Custom = 200            // category = TUSBAUDIO_NOTIFY_CATEGORY_CUSTOM_EVENT

} TUsbAudioNotifyEvent;


//
// custom event data
//
typedef struct tagTUsbAudioCustomEventData
{
    unsigned int eventCode;

} TUsbAudioCustomEventData;


//
// clock source event data
//
typedef struct tagTUsbAudioClockSourceEventData
{
    unsigned int clockSourceId;

} TUsbAudioClockSourceEventData;



//
// data field for NotifyEvent_VolumeChanged and NotifyEvent_MuteChanged events
//
// NOTE: This struct layout is part of the API. If it is changed, the API version needs to be changed.
//
typedef struct tagTUsbAudioNotifyVolumeChange
{
    // unit ID of the feature unit that reported a volume or mute change
    unsigned char featureUnitId;
    // channel number, 0 for master, 1..N for logical channels
    unsigned char featureUnitLogicalChannel;
    // new value for volume or mute
    short newValue;

    // reserved for future use
    unsigned int reserved[8];

} TUsbAudioNotifyVolumeChange;


//
// driver version info
//
// NOTE: This struct layout is part of the API. If it is changed, the API version needs to be changed.
//
typedef struct tagTUsbAudioDriverInfo
{
    // current version of programming interface (API) exposed by DLL
    unsigned int apiVersionMajor;
    unsigned int apiVersionMinor;

    // current version of device driver implementation
    unsigned int driverVersionMajor;
    unsigned int driverVersionMinor;
    unsigned int driverVersionSub;

    // additional information, encoded as bit flags
    unsigned int flags;
// the device driver is a debug build
#define TUSBAUDIO_DRIVER_INFOFLAG_CHECKED_BUILD    0x00000001
// the device driver is an evaluation version
#define TUSBAUDIO_DRIVER_INFOFLAG_EVAL_VERSION     0x00000002

} TUsbAudioDriverInfo;


//
// device properties
//
// NOTE: This struct layout is part of the API. If it is changed, the API version needs to be changed.
//
typedef struct tagTUsbAudioDeviceProperties
{
    // values from the USB device descriptor
    unsigned int usbVendorId;
    unsigned int usbProductId;
    unsigned int usbRevisionId;

    // serial number as reported by the device (string descriptor)
    // set to an empty string if the device does not support a serial number
    WCHAR serialNumberString[TUSBAUDIO_MAX_STRDESC_STRLEN];

    // manufacturer and product name as reported by the device (string descriptor)
    // always contains a null-terminated string,
    // set to an empty string if the device does not support it
    WCHAR manufacturerString[TUSBAUDIO_MAX_STRDESC_STRLEN];
    WCHAR productString[TUSBAUDIO_MAX_STRDESC_STRLEN];

    // various flags bit-encoded
    unsigned int flags;
#define TUSBAUDIO_DEVPROP_FLAG_HIGH_SPEED_SUPPORTED         0x00000001  // device supports high-speed mode
#define TUSBAUDIO_DEVPROP_FLAG_HIGH_SPEED                   0x00000002  // device currently works in high-speed mode
#define TUSBAUDIO_DEVPROP_FLAG_AUDIO_STREAMING_SUPPORTED    0x00000004  // device supports at last one audio class compliant stream
#define TUSBAUDIO_DEVPROP_FLAG_MIDI_STREAMING_SUPPORTED     0x00000008  // device supports at last one MIDI class compliant endpoint
#define TUSBAUDIO_DEVPROP_FLAG_DFU_SUPPORTED                0x00000010  // device supports USB DFU class
#define TUSBAUDIO_DEVPROP_FLAG_DSP_PLUGIN_PRESENT           0x00000020  // DSP plugin is loaded
#define TUSBAUDIO_DEVPROP_FLAG_DFU_XMOS_PROTOCOL            0x00000040  // device uses XMOS specific DFU requests
#define TUSBAUDIO_DEVPROP_FLAG_PEAK_METERS_SUPPORTED        0x00000080  // device supports the peak meters feature
#define TUSBAUDIO_DEVPROP_FLAG_AUDIOCLASS10_SUPPORTED       0x00000100  // device supports USB Audio Class 1.0
#define TUSBAUDIO_DEVPROP_FLAG_AUDIOCLASS20_SUPPORTED       0x00000200  // device supports USB Audio Class 2.0
#define TUSBAUDIO_DEVPROP_FLAG_CURRENT_ASIO_DEVICE          0x00001000  // current device for ASIO instance specified by TUSBAUDIO_GetASIORelation
#define TUSBAUDIO_DEVPROP_FLAG_PREFERRED_ASIO_DEVICE        0x00002000  // preferred device for ASIO instance specified by TUSBAUDIO_GetASIORelation (stored persistently in registry)

    // interface descriptor number of the audio class control interface, -1 if invalid
    int audioControlInterfaceNumber;

    // reserved for future use
    unsigned int reserved[63];

} TUsbAudioDeviceProperties;


typedef struct tagTUsbAudioASIOInstanceDetails
{
    // ASIO driver GUID (CLSID), null terminated
    // format: {A356729F-38B6-42AF-84E9-17117198DA45}
    WCHAR asioDriverGuid[64];

    // ASIO instance number
    TUsbAudioASIOInstanceID asioInstanceId;

    // ASIO driver name, null terminated
    // used as registry name and as description (display name)
    WCHAR asioDriverName[100];

#define TUSBAUDIO_ASIO_INSTANCE_FLAG_HIDE 1u
    unsigned int flags;

} TUsbAudioASIOInstanceDetails;



#define TUSBAUDIO_ASIO_BUFFER_SIZE_LIST_LEN     32


typedef struct tagTUsbAudioASIOInstanceInfo
{
    // current sample rate the ASIO instance is running at
    // includes DSD correction
    unsigned int currentSampleRate;

    // reference value that must be passed to TUSBAUDIO_SetASIOBufferPreferredSize
    unsigned int referenceSampleRate;

    // number of ASIO clients currently active
    unsigned int activeAsioClients;

    // various flags bit-encoded
    unsigned int flags;
#define TUSBAUDIO_ASIO_FLAG_DSD_MODE            0x00000001  // ASIO currently runs in DSD mode

    // options bit-encoded
    unsigned int options;
#define TUSBAUDIO_ASIO_OPT_SAFE_MODE   0x00010000  // safe mode

    // Latency of the input/output data path, in terms of samples
    unsigned int inputLatency;
    unsigned int outputLatency;

    // preferred ASIO buffer size, in samples, reported by the ASIO DLL
    unsigned int preferredSize;

    // list of supported ASIO buffer sizes, in samples
    // The array contains supportedSizesCount valid entries.
    unsigned int supportedSizesCount;
    unsigned int supportedSizes[TUSBAUDIO_ASIO_BUFFER_SIZE_LIST_LEN];

    // reserved for future use
    unsigned int reserved[8];

} TUsbAudioASIOInstanceInfo;


typedef struct tagTUsbAudioCplOptions
{
    // various flags bit-encoded
    unsigned int flags;
#define TUSBAUDIO_CPL_SHOW_SAMPLE_RATE_CONTROL      0x00000001
#define TUSBAUDIO_CPL_SHOW_SOUND_DEVICE_PROFILE     0x00000002

    // reserved for future use
    unsigned int reserved[31];

} TUsbAudioCplOptions;


//
// device streaming mode
//
typedef enum tagTUsbAudioDeviceStreamingMode
{
    DeviceStreamingMode_reserved = 0, // for internal use only, not a real value

    DeviceStreamingMode_PowerSaver,
    DeviceStreamingMode_AlwaysOn,

    DeviceStreamingMode_last  // for internal use only, not a real value

} TUsbAudioDeviceStreamingMode;

// flags
#define TUSBAUDIO_STREAMING_MODE_PERSISTENT   0x00000001



//
// Stream Format selection mode
//
typedef enum tagTUsbAudioStreamFormatSelectionMode
{
    StreamFormatSelectionMode_reserved = 0, // for internal use only, not a real value

    StreamFormatSelectionMode_Manual,  // manual selection per control panel
    StreamFormatSelectionMode_Auto_MaxChannelCount, // prefer channel count
    StreamFormatSelectionMode_Auto_MaxSampleSize,   // prefer sample size (in bits)

    StreamFormatSelectionMode_last  // for internal use only, not a real value

} TUsbAudioStreamFormatSelectionMode;



//
// Clock Source Type
//
typedef enum tagTUsbAudioClockSourceType
{
    ClockSourceType_External = 1,

    ClockSourceType_InternalFixed,
    ClockSourceType_InternalVariable,
    ClockSourceType_InternalProgrammable

} TUsbAudioClockSourceType;

//
// clock source info
//
// NOTE: This struct layout is part of the API. If it is changed, the API version needs to be changed.
//
typedef struct tagTUsbAudioClockSource
{
    // unique ID
    // This ID unambiguously identifies the clock source at the driver API.
    // Values are driver-defined. Client code must not interpret the value.
    unsigned int clockSourceId;

    // ID of the clock source unit (within the audio function) that implements this source.
    unsigned int clockSourceUnitId;

    // clock selector input pin this source is connected to
    unsigned int clockSelectorPinNumber;

    // flag: 1 if clock is valid/stable, 0 otherwise
    unsigned int clockIsValid;

    // current sample rate, in samples per second
    // only valid if clockIsValid==1, set to zero otherwise
    unsigned int sampleRate;

    // name of the clock source
    WCHAR clockNameString[TUSBAUDIO_MAX_STRDESC_STRLEN];

    // type of the clock source
    TUsbAudioClockSourceType clockSourceType;

    // reserved for future use, set to zero
    unsigned int externalSampleRate;

    // various flags, bit-encoded
    unsigned int flags;
#define TUSBAUDIO_CLOCK_SYNCHED_SOF             0x00000001
#define TUSBAUDIO_CLOCK_SUPPORTS_SET            0x00000002
#define TUSBAUDIO_CLOCK_SUPPORTS_VALIDITY       0x00000004
#define TUSBAUDIO_CLOCK_SUPPORTS_NOTIFICATIONS  0x00000008

    // reserved for future use
    unsigned int reserved[13];

} TUsbAudioClockSource;




//
// stream format info
//
// NOTE: This struct layout is part of the API. If it is changed, the API version needs to be changed.
//
typedef struct tagTUsbAudioStreamFormat
{
    // unique ID
    // This ID unambiguously identifies the format at the driver API.
    // Values are driver-defined. Client code should not interpret the value.
    unsigned int formatId;

    // number of valid bits per sample (16 or 24)
    unsigned int bitsPerSample;

    // number of audio channels
    unsigned int numberOfChannels;

    // name of the format as reported by the device
    WCHAR formatNameString[TUSBAUDIO_MAX_STRDESC_STRLEN];

    // reserved for future use
    unsigned int reserved[16];

} TUsbAudioStreamFormat;



//
// volume control
//
typedef struct tagTUsbAudioVolumeMuteInfo
{
    // various flags, bit-encoded
    unsigned int flags;
#define TUSBAUDIO_FLAG_VOLUME_CONTROL_PRESENT     (1u<<0)
#define TUSBAUDIO_FLAG_MUTE_CONTROL_PRESENT       (1u<<1)

    // supported range for volume control, values as defined by the USB Audio Class specification
    // valid only if TUSBAUDIO_FLAG_VOLUME_CONTROL_PRESENT is set
    short volumeMin;
    short volumeMax;
    short volumeStep;

    // filler
    short reserved1;

    // ID of the feature unit (FU) that implements the above controls
    unsigned char featureUnitId;

    // filler
    unsigned char reserved2[3];

} TUsbAudioVolumeMuteInfo;


//
// channel info
//
typedef struct tagTUsbAudioChannelInfo
{
    // various flags, bit-encoded
    unsigned int flags;

    // channel name as reported by the device
    WCHAR channelNameString[TUSBAUDIO_MAX_STRDESC_STRLEN];

    // reserved for future use
    unsigned int reserved[16];

} TUsbAudioChannelInfo;


//
// channel property
//
// NOTE: This struct layout is part of the API. If it is changed, the API version needs to be changed.
//
typedef struct tagTUsbAudioChannelProperty
{
    // zero-based index within the stream
    unsigned int channelIndex;

    // 1 for input (record), 0 for output (playback)
    unsigned int isInput;

    // various flags, bit-encoded
    unsigned int flags;
#define TUSBAUDIO_CHANPROP_FLAG_VOLUME_MAPPED       0x00000001  // KS volume control is mapped to the below FU
#define TUSBAUDIO_CHANPROP_FLAG_MUTE_MAPPED         0x00000002  // KS mute control is mapped to the below FU

    // ID of the feature unit (FU) that is used by KS to control volume and/or mute
    // valid only if one of TUSBAUDIO_CHANPROP_FLAG_VOLUME_MAPPED or TUSBAUDIO_CHANPROP_FLAG_MUTE_MAPPED is set
    unsigned char featureUnitId;
    // logical channel in the corresponding feature unit
    unsigned char featureUnitLogicalChannel;

    // supported range for volume control, values as defined by the USB Audio Class specification
    // valid only if TUSBAUDIO_CHANPROP_FLAG_VOLUME_MAPPED is set
    short volumeRangeMin;
    short volumeRangeMax;
    // supported resolution for volume values, values as defined by the USB Audio Class specification
    // valid only if TUSBAUDIO_CHANPROP_FLAG_VOLUME_MAPPED is set
    unsigned short volumeRangeStep;

    // channel name as reported by the device
    WCHAR channelNameString[TUSBAUDIO_MAX_STRDESC_STRLEN];

    // reserved for future use
    unsigned int reserved[16];

} TUsbAudioChannelProperty;



typedef enum tagTUsbAudioControlRequestRecipient
{
    ControlRequestRecipient_Device = 0,
    ControlRequestRecipient_Interface,
    ControlRequestRecipient_Endpoint,
    ControlRequestRecipient_Other
} TUsbAudioControlRequestRecipient;



typedef enum tagTUsbAudioExtendedInfoId
{
    ExtendedInfoId_GetWdmSoundDeviceCount = 0,          // input: no / output: TUsbAudioWdmSoundDeviceCount
    ExtendedInfoId_GetWdmInputSoundDevices,             // input: no / output: array of TUsbAudioWdmSoundDevice
    ExtendedInfoId_GetWdmOutputSoundDevices,            // input: no / output: array of TUsbAudioWdmSoundDevice
    ExtendedInfoId_GetWdmChannelInfo,                   // input: TUsbAudioObjectId / output: array of TUsbAudioWdmChannelInfo
    ExtendedInfoId_GetHardwareChannelCount,             // input: no / output: TUsbAudioHardwareChannelCount
    ExtendedInfoId_GetVirtualChannelCount,              // input: no / output: TUsbAudioVirtualChannelCount
    ExtendedInfoId_GetHardwareInputChannelInfo,         // input: no / output: array of TUsbAudioHardwareChannelInfo
    ExtendedInfoId_GetHardwareOutputChannelInfo,        // input: no / output: array of TUsbAudioHardwareChannelInfo
    ExtendedInfoId_GetHardwareChannelName,              // input: TUsbAudioObjectId / output: TUsbAudioHardwareChannelName

    ExtendedInfoId_GetValidSoundDeviceProfiles,         // input: unsigned int streamFormatId / output: array of TUsbAudioSoundDeviceProfileInfo
    ExtendedInfoId_GetCurrentSoundDeviceProfile,        // input: unsigned int streamFormatId / output: TUsbAudioSoundDeviceProfileInfo

    ExtendedInfoId_GetHardwareChannelHiddenFlags,       // input: unsigned int streamFormatId / output: TUsbAudioHardwareChannelHiddenFlags

    ExtendedInfoId_GetValidVirtualSoundDeviceProfiles,  // input: unsigned int inputStream (1 for input, 0 for output) / output: array of TUsbAudioSoundDeviceProfileInfo
    ExtendedInfoId_GetCurrentVirtualSoundDeviceProfile, // input: unsigned int inputStream (1 for input, 0 for output) / output: TUsbAudioSoundDeviceProfileInfo

    ExtendedInfoId_max
} TUsbAudioExtendedInfoId;


typedef struct tagTUsbAudioWdmSoundDeviceCount
{
    unsigned int inputWdmSoundDeviceCount;
    unsigned int outputWdmSoundDeviceCount;

} TUsbAudioWdmSoundDeviceCount;


typedef struct tagTUsbAudioWdmSoundDevice
{
    TUsbAudioObjectId id;

    unsigned int channelCount;

    WCHAR displayName[TUSBAUDIO_MAX_STRDESC_STRLEN];

    GUID pinCategoryGuid;
    GUID pinNameGuid;

} TUsbAudioWdmSoundDevice;


typedef struct tagTUsbAudioWdmChannelInfo
{
    unsigned int channelIndex;

    unsigned int isVirtual;

    unsigned int channelAddress;

} TUsbAudioWdmChannelInfo;


typedef struct tagTUsbAudioHardwareChannelCount
{
    unsigned int inputHardwareChannelCount;
    unsigned int outputHardwareChannelCount;

} TUsbAudioHardwareChannelCount;


typedef struct tagTUsbAudioVirtualChannelCount
{
    unsigned int inputVirtualChannelCount;
    unsigned int outputVirtualChannelCount;

} TUsbAudioVirtualChannelCount;


typedef struct tagTUsbAudioHardwareChannelInfo
{
    TUsbAudioObjectId id;
    unsigned int channelIndex;

} TUsbAudioHardwareChannelInfo;


typedef struct tagTUsbAudioHardwareChannelName
{
    WCHAR channelName[TUSBAUDIO_MAX_STRDESC_STRLEN];

} TUsbAudioHardwareChannelName;


typedef struct tagTUsbAudioSoundDeviceProfileInfo
{
    WCHAR profileName[TUSBAUDIO_MAX_SD_PROFILE_NAME_STRING_LENGTH];
    WCHAR profileDisplayName[TUSBAUDIO_MAX_NAME_STRING_LENGTH];

} TUsbAudioSoundDeviceProfileInfo;


typedef struct tagTUsbAudioSetSoundDeviceProfile
{
    unsigned int streamFormatId;
    WCHAR profileName[TUSBAUDIO_MAX_SD_PROFILE_NAME_STRING_LENGTH];

} TUsbAudioSetSoundDeviceProfile;


typedef struct tagTUsbAudioSetVirtualSoundDeviceProfile
{
    unsigned int input;
    WCHAR profileName[TUSBAUDIO_MAX_SD_PROFILE_NAME_STRING_LENGTH];

} TUsbAudioSetVirtualSoundDeviceProfile;



typedef struct tagTUsbAudioHardwareChannelHiddenFlags
{
    unsigned char hiddenFlagsBitmask[32]; // max 256 channels (limited by the UAC 2.0 descriptors)

} TUsbAudioHardwareChannelHiddenFlags;



//
// Notification Events
//
typedef enum tagTUsbAudioCustomStringScope
{
    CustomStringScope_Driver = 0,
    CustomStringScope_Device

} TUsbAudioCustomStringScope;



//
// channel types in the driver
//
typedef enum tagTUsbAudioStreamChannelType
{
    ChannelType_UsbIn = 0,
    ChannelType_UsbOut,

    ChannelType_ApplicationIn,
    ChannelType_ApplicationOut,

    ChannelType_VirtualIn,
    ChannelType_VirtualOut,


    // dummy value, defined to force sizeof(enum)==4
    ChannelType_Force32bitEnum = 2000000000

} TUsbAudioStreamChannelType;


//
// Identifies a channel
//
typedef struct tagTUsbAudioStreamChannelId
{
    TUsbAudioObjectId channelId;
    TUsbAudioStreamChannelType channelType;
    int reserved;

} TUsbAudioStreamChannelId;



typedef struct tagTUsbAudioPeakMeterId
{
    TUsbAudioStreamChannelId channelId;

    unsigned int flags;
// reset max value after capture
#define TUSBAUDIO_PEAK_METER_RESET_MAX    0x00000001u

} TUsbAudioPeakMeterId;


typedef struct tagTUsbAudioPeakMeterData
{
    float currentPeakValue;
    float maxPeakValue;

} TUsbAudioPeakMeterData;



// restore previous alignment
#include <poppack.h>


#endif  // __tusbaudio_defs_h__

/*************************** EOF **************************************/
