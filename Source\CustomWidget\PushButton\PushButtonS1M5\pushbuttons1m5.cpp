#include "globalfont.h"
#include "pushbuttons1m5.h"


PushButtonS1M5::PushButtonS1M5(QWidget* parent)
    : QWidget(parent)
{
    mPushButtonNC.setParent(this);
    mPushButtonIN1.setParent(this);
    mPushButtonIN2.setParent(this);
    mPushButtonIN12.setParent(this);
    mPushButtonOFF.setParent(this);
    mPushButtonSTU.setParent(this);
    mPushButtonLIVE.setParent(this);
    mPushButtonHALL.setParent(this);
    mLabelNC.setParent(this);
    mLabelIN1.setParent(this);
    mLabelIN2.setParent(this);
    mLabelIN12.setParent(this);
    QString style;
    style = "QLabel {"
            "   background-color: transparent;"
            "   image: url(:/Icon/VerticalRoundedRectBlack.png);"
            "}";
    mLabelNC.setStyleSheet(style);
    mLabelIN1.setStyleSheet(style);
    mLabelIN2.setStyleSheet(style);
    mLabelIN12.setStyleSheet(style);
    style = "QPushButton {"
            "   text-align: left;"
            "   color: rgb(161, 161, 161);"
            "   background-color: transparent;"
            "}"
            "QPushButton:hover {"
            "   color: rgb(255, 255, 255);"
            "}";
    mPushButtonNC.setStyleSheet(style);
    mPushButtonIN1.setStyleSheet(style);
    mPushButtonIN2.setStyleSheet(style);
    mPushButtonIN12.setStyleSheet(style);
    style = "QPushButton {"
            "   border-radius: 3px;"
            "   color: rgb(161, 161, 161);"
            "   background-color: rgb(60, 60, 60);"
            "}"
            "QPushButton:hover {"
            "   border: 2px solid gray;"
            "   border-radius: 3px;"
            "}";
    mPushButtonOFF.setStyleSheet(style);
    mPushButtonSTU.setStyleSheet(style);
    mPushButtonLIVE.setStyleSheet(style);
    mPushButtonHALL.setStyleSheet(style);
    mPushButtonNC.setText("NC");
    mPushButtonIN1.setText("IN 1");
    mPushButtonIN2.setText("IN 2");
    mPushButtonIN12.setText("IN 1+2");
    mPushButtonOFF.setText("OFF");
    mPushButtonSTU.setText("STU");
    mPushButtonLIVE.setText("LIVE");
    mPushButtonHALL.setText("HALL");
    connect(&mPushButtonNC, SIGNAL(clicked()), this, SLOT(in_mPushButtonNC_clicked()), Qt::UniqueConnection);
    connect(&mPushButtonIN1, SIGNAL(clicked()), this, SLOT(in_mPushButtonIN1_clicked()), Qt::UniqueConnection);
    connect(&mPushButtonIN2, SIGNAL(clicked()), this, SLOT(in_mPushButtonIN2_clicked()), Qt::UniqueConnection);
    connect(&mPushButtonIN12, SIGNAL(clicked()), this, SLOT(in_mPushButtonIN12_clicked()), Qt::UniqueConnection);
    connect(&mPushButtonOFF, SIGNAL(clicked()), this, SLOT(in_mPushButtonOFF_clicked()), Qt::UniqueConnection);
    connect(&mPushButtonSTU, SIGNAL(clicked()), this, SLOT(in_mPushButtonSTU_clicked()), Qt::UniqueConnection);
    connect(&mPushButtonLIVE, SIGNAL(clicked()), this, SLOT(in_mPushButtonLIVE_clicked()), Qt::UniqueConnection);
    connect(&mPushButtonHALL, SIGNAL(clicked()), this, SLOT(in_mPushButtonHALL_clicked()), Qt::UniqueConnection);
}
PushButtonS1M5::~PushButtonS1M5()
{

}


// override
void PushButtonS1M5::resizeEvent(QResizeEvent* e)
{
    Q_UNUSED(e);
    // W
    float wPixelPerRatio=size().width() / 100.0;
    int wLabel=wPixelPerRatio * 10;
    int wSpace1=wPixelPerRatio * 10;
    int wPushButton=size().width() - wLabel - wSpace1;
    int xLabel=0;
    int xPushButton=xLabel + wLabel + wSpace1;
    // H
    float hPixelPerRatio=size().height() / 100.0;
    int hSpace1=hPixelPerRatio * 12;
    int hSpace2=hPixelPerRatio * 2;
    int hSpace3=hPixelPerRatio * 18;
    int hSpace4=hPixelPerRatio * 3;
    int hPushButton=(size().height() - hSpace1 - hSpace2 * 6 - hSpace3 - hSpace4) / 7;
    mLabelNC.setGeometry(xLabel, hSpace1, wLabel, hPushButton);
    mPushButtonNC.setGeometry(xPushButton, hSpace1, wPushButton, hPushButton);
    mLabelIN1.setGeometry(xLabel, hSpace1 + hSpace2 + hPushButton, wLabel, hPushButton);
    mPushButtonIN1.setGeometry(xPushButton, hSpace1 + hPushButton + hSpace2, wPushButton, hPushButton);
    mLabelIN2.setGeometry(xLabel, hSpace1 + hSpace2 * 2 + hPushButton * 2, wLabel, hPushButton);
    mPushButtonIN2.setGeometry(xPushButton, hSpace1 + hSpace2 * 2 + hPushButton * 2, wPushButton, hPushButton);
    mLabelIN12.setGeometry(xLabel, hSpace1 + hSpace2 * 3 + hPushButton * 3, wLabel, hPushButton);
    mPushButtonIN12.setGeometry(xPushButton, hSpace1 + hSpace2 * 3 + hPushButton * 3, wPushButton, hPushButton);
    mPushButtonOFF.setGeometry(xLabel + (wLabel + wSpace1) / 3, hSpace1 + hSpace2 * 2 + hSpace3 + hPushButton * 3, wPushButton * 1.1, hPushButton);
    mPushButtonSTU.setGeometry(xLabel + (wLabel + wSpace1) / 3, hSpace1 + hSpace2 * 3 + hSpace3 + hPushButton * 4, wPushButton * 1.1, hPushButton);
    mPushButtonLIVE.setGeometry(xLabel + (wLabel + wSpace1) / 3, hSpace1 + hSpace2 * 4 + hSpace3 + hPushButton * 5, wPushButton * 1.1, hPushButton);
    mPushButtonHALL.setGeometry(xLabel + (wLabel + wSpace1) / 3, hSpace1 + hSpace2 * 5 + hSpace3 + hPushButton * 6, wPushButton * 1.1, hPushButton);
    mFont.setPointSize(GLBFHandle.getSuitablePointSize(mFont, mPushButtonIN2.height()));
    mPushButtonNC.setFont(mFont);
    mPushButtonIN1.setFont(mFont);
    mPushButtonIN2.setFont(mFont);
    mPushButtonIN12.setFont(mFont);
    mFont.setPointSize(GLBFHandle.getSuitablePointSize(mFont, mPushButtonSTU.text(), mPushButtonSTU.rect()));
    mPushButtonOFF.setFont(mFont);
    mPushButtonSTU.setFont(mFont);
    mPushButtonLIVE.setFont(mFont);
    mPushButtonHALL.setFont(mFont);
}


// slot
void PushButtonS1M5::in_mPushButtonNC_clicked()
{
    QString style;
    if(!mPushButtonStateNC)
    {
        mPushButtonStateNC = true;
        mPushButtonStateIN1 = false;
        mPushButtonStateIN2 = false;
        mPushButtonStateIN12 = false;
        style = "QLabel {"
                "   background-color: transparent;"
                "   image: url(:/Icon/VerticalRoundedRectGreen.png);"
                "}";
        mLabelNC.setStyleSheet(style);
        style = "QLabel {"
                "   background-color: transparent;"
                "   image: url(:/Icon/VerticalRoundedRectBlack.png);"
                "}";
        mLabelIN1.setStyleSheet(style);
        mLabelIN2.setStyleSheet(style);
        mLabelIN12.setStyleSheet(style);
        style = "QPushButton {"
                "   text-align: left;"
                "   color: rgb(255, 255, 255);"
                "   background-color: transparent;"
                "}"
                "QPushButton:hover {"
                "   color: rgb(255, 255, 255);"
                "}";
        mPushButtonNC.setStyleSheet(style);
        style = "QPushButton {"
                "   text-align: left;"
                "   color: rgb(161, 161, 161);"
                "   background-color: transparent;"
                "}"
                "QPushButton:hover {"
                "   color: rgb(255, 255, 255);"
                "}";
        mPushButtonIN1.setStyleSheet(style);
        mPushButtonIN2.setStyleSheet(style);
        mPushButtonIN12.setStyleSheet(style);
        emit buttonStateChanged(buttonNC, true);
    }
}
void PushButtonS1M5::in_mPushButtonIN1_clicked()
{
    QString style;
    if(!mPushButtonStateIN1)
    {
        mPushButtonStateIN1 = true;
        mPushButtonStateNC = false;
        mPushButtonStateIN2 = false;
        mPushButtonStateIN12 = false;
        style = "QLabel {"
                "   background-color: transparent;"
                "   image: url(:/Icon/VerticalRoundedRectGreen.png);"
                "}";
        mLabelIN1.setStyleSheet(style);
        style = "QLabel {"
                "   background-color: transparent;"
                "   image: url(:/Icon/VerticalRoundedRectBlack.png);"
                "}";
        mLabelNC.setStyleSheet(style);
        mLabelIN2.setStyleSheet(style);
        mLabelIN12.setStyleSheet(style);
        style = "QPushButton {"
                "   text-align: left;"
                "   color: rgb(255, 255, 255);"
                "   background-color: transparent;"
                "}"
                "QPushButton:hover {"
                "   color: rgb(255, 255, 255);"
                "}";
        mPushButtonIN1.setStyleSheet(style);
        style = "QPushButton {"
                "   text-align: left;"
                "   color: rgb(161, 161, 161);"
                "   background-color: transparent;"
                "}"
                "QPushButton:hover {"
                "   color: rgb(255, 255, 255);"
                "}";
        mPushButtonNC.setStyleSheet(style);
        mPushButtonIN2.setStyleSheet(style);
        mPushButtonIN12.setStyleSheet(style);
        emit buttonStateChanged(buttonIN1, true);
    }
}
void PushButtonS1M5::in_mPushButtonIN2_clicked()
{
    QString style;
    if(!mPushButtonStateIN2)
    {
        mPushButtonStateIN2 = true;
        mPushButtonStateNC = false;
        mPushButtonStateIN1 = false;
        mPushButtonStateIN12 = false;
        style = "QLabel {"
                "   background-color: transparent;"
                "   image: url(:/Icon/VerticalRoundedRectGreen.png);"
                "}";
        mLabelIN2.setStyleSheet(style);
        style = "QLabel {"
                "   background-color: transparent;"
                "   image: url(:/Icon/VerticalRoundedRectBlack.png);"
                "}";
        mLabelNC.setStyleSheet(style);
        mLabelIN1.setStyleSheet(style);
        mLabelIN12.setStyleSheet(style);
        style = "QPushButton {"
                "   text-align: left;"
                "   color: rgb(255, 255, 255);"
                "   background-color: transparent;"
                "}"
                "QPushButton:hover {"
                "   color: rgb(255, 255, 255);"
                "}";
        mPushButtonIN2.setStyleSheet(style);
        style = "QPushButton {"
                "   text-align: left;"
                "   color: rgb(161, 161, 161);"
                "   background-color: transparent;"
                "}"
                "QPushButton:hover {"
                "   color: rgb(255, 255, 255);"
                "}";
        mPushButtonNC.setStyleSheet(style);
        mPushButtonIN1.setStyleSheet(style);
        mPushButtonIN12.setStyleSheet(style);
        emit buttonStateChanged(buttonIN2, true);
    }
}
void PushButtonS1M5::in_mPushButtonIN12_clicked()
{
    QString style;
    if(!mPushButtonStateIN12)
    {
        mPushButtonStateIN12 = true;
        mPushButtonStateNC = false;
        mPushButtonStateIN1 = false;
        mPushButtonStateIN2 = false;
        style = "QLabel {"
                "   background-color: transparent;"
                "   image: url(:/Icon/VerticalRoundedRectGreen.png);"
                "}";
        mLabelIN12.setStyleSheet(style);
        style = "QLabel {"
                "   background-color: transparent;"
                "   image: url(:/Icon/VerticalRoundedRectBlack.png);"
                "}";
        mLabelNC.setStyleSheet(style);
        mLabelIN1.setStyleSheet(style);
        mLabelIN2.setStyleSheet(style);
        style = "QPushButton {"
                "   text-align: left;"
                "   color: rgb(255, 255, 255);"
                "   background-color: transparent;"
                "}"
                "QPushButton:hover {"
                "   color: rgb(255, 255, 255);"
                "}";
        mPushButtonIN12.setStyleSheet(style);
        style = "QPushButton {"
                "   text-align: left;"
                "   color: rgb(161, 161, 161);"
                "   background-color: transparent;"
                "}"
                "QPushButton:hover {"
                "   color: rgb(255, 255, 255);"
                "}";
        mPushButtonNC.setStyleSheet(style);
        mPushButtonIN1.setStyleSheet(style);
        mPushButtonIN2.setStyleSheet(style);
        emit buttonStateChanged(buttonIN12, true);
    }
}
void PushButtonS1M5::in_mPushButtonOFF_clicked()
{
    QString style;
    if(!mPushButtonStateOFF)
    {
        mPushButtonStateOFF = true;
        mPushButtonStateSTU = false;
        mPushButtonStateLIVE = false;
        mPushButtonStateHALL = false;
        style = "QPushButton {"
                "   border-radius: 3px;"
                "   color: rgb(229, 229, 229);"
                "   background-color: rgb(0, 126, 168);"
                "}"
                "QPushButton:hover {"
                "   border: 2px solid gray;"
                "   border-radius: 3px;"
                "}";
        mPushButtonOFF.setStyleSheet(style);
        style = "QPushButton {"
                "   border-radius: 3px;"
                "   color: rgb(161, 161, 161);"
                "   background-color: rgb(60, 60, 60);"
                "}"
                "QPushButton:hover {"
                "   border: 2px solid gray;"
                "   border-radius: 3px;"
                "}";
        mPushButtonSTU.setStyleSheet(style);
        mPushButtonLIVE.setStyleSheet(style);
        mPushButtonHALL.setStyleSheet(style);
        emit buttonStateChanged(buttonOFF, true);
    }
}
void PushButtonS1M5::in_mPushButtonSTU_clicked()
{
    QString style;
    if(!mPushButtonStateSTU)
    {
        mPushButtonStateSTU = true;
        mPushButtonStateOFF = false;
        mPushButtonStateLIVE = false;
        mPushButtonStateHALL = false;
        style = "QPushButton {"
                "   border-radius: 3px;"
                "   color: rgb(229, 229, 229);"
                "   background-color: rgb(0, 126, 168);"
                "}"
                "QPushButton:hover {"
                "   border: 2px solid gray;"
                "   border-radius: 3px;"
                "}";
        mPushButtonSTU.setStyleSheet(style);
        style = "QPushButton {"
                "   border-radius: 3px;"
                "   color: rgb(161, 161, 161);"
                "   background-color: rgb(60, 60, 60);"
                "}"
                "QPushButton:hover {"
                "   border: 2px solid gray;"
                "   border-radius: 3px;"
                "}";
        mPushButtonOFF.setStyleSheet(style);
        mPushButtonLIVE.setStyleSheet(style);
        mPushButtonHALL.setStyleSheet(style);
        emit buttonStateChanged(buttonSTU, true);
    }
}
void PushButtonS1M5::in_mPushButtonLIVE_clicked()
{
    QString style;
    if(!mPushButtonStateLIVE)
    {
        mPushButtonStateLIVE = true;
        mPushButtonStateOFF = false;
        mPushButtonStateSTU = false;
        mPushButtonStateHALL = false;
        style = "QPushButton {"
                "   border-radius: 3px;"
                "   color: rgb(229, 229, 229);"
                "   background-color: rgb(0, 126, 168);"
                "}"
                "QPushButton:hover {"
                "   border: 2px solid gray;"
                "   border-radius: 3px;"
                "}";
        mPushButtonLIVE.setStyleSheet(style);
        style = "QPushButton {"
                "   border-radius: 3px;"
                "   color: rgb(161, 161, 161);"
                "   background-color: rgb(60, 60, 60);"
                "}"
                "QPushButton:hover {"
                "   border: 2px solid gray;"
                "   border-radius: 3px;"
                "}";
        mPushButtonOFF.setStyleSheet(style);
        mPushButtonSTU.setStyleSheet(style);
        mPushButtonHALL.setStyleSheet(style);
        emit buttonStateChanged(buttonLIVE, true);
    }
}
void PushButtonS1M5::in_mPushButtonHALL_clicked()
{
    QString style;
    if(!mPushButtonStateHALL)
    {
        mPushButtonStateHALL = true;
        mPushButtonStateOFF = false;
        mPushButtonStateSTU = false;
        mPushButtonStateLIVE = false;
        style = "QPushButton {"
                "   border-radius: 3px;"
                "   color: rgb(229, 229, 229);"
                "   background-color: rgb(0, 126, 168);"
                "}"
                "QPushButton:hover {"
                "   border: 2px solid gray;"
                "   border-radius: 3px;"
                "}";
        mPushButtonHALL.setStyleSheet(style);
        style = "QPushButton {"
                "   border-radius: 3px;"
                "   color: rgb(161, 161, 161);"
                "   background-color: rgb(60, 60, 60);"
                "}"
                "QPushButton:hover {"
                "   border: 2px solid gray;"
                "   border-radius: 3px;"
                "}";
        mPushButtonOFF.setStyleSheet(style);
        mPushButtonSTU.setStyleSheet(style);
        mPushButtonLIVE.setStyleSheet(style);
        emit buttonStateChanged(buttonHALL, true);
    }
}


// setter & getter
PushButtonS1M5& PushButtonS1M5::setFont(QFont font)
{
    mFont = font;
    QResizeEvent e(size(), size());
    resizeEvent(&e);
    update();
    return *this;
}
PushButtonS1M5& PushButtonS1M5::setPushButtonStateNC(bool state)
{
    Q_UNUSED(state);
    QString style;
    mPushButtonStateNC = true;
    mPushButtonStateIN1 = false;
    mPushButtonStateIN2 = false;
    mPushButtonStateIN12 = false;
    style = "QLabel {"
            "   background-color: transparent;"
            "   image: url(:/Icon/VerticalRoundedRectGreen.png);"
            "}";
    mLabelNC.setStyleSheet(style);
    style = "QLabel {"
            "   background-color: transparent;"
            "   image: url(:/Icon/VerticalRoundedRectBlack.png);"
            "}";
    mLabelIN1.setStyleSheet(style);
    mLabelIN2.setStyleSheet(style);
    mLabelIN12.setStyleSheet(style);
    style = "QPushButton {"
            "   text-align: left;"
            "   color: rgb(255, 255, 255);"
            "   background-color: transparent;"
            "}"
            "QPushButton:hover {"
            "   color: rgb(255, 255, 255);"
            "}";
    mPushButtonNC.setStyleSheet(style);
    style = "QPushButton {"
            "   text-align: left;"
            "   color: rgb(161, 161, 161);"
            "   background-color: transparent;"
            "}"
            "QPushButton:hover {"
            "   color: rgb(255, 255, 255);"
            "}";
    mPushButtonIN1.setStyleSheet(style);
    mPushButtonIN2.setStyleSheet(style);
    mPushButtonIN12.setStyleSheet(style);
    return *this;
}
PushButtonS1M5& PushButtonS1M5::setPushButtonStateIN1(bool state)
{
    Q_UNUSED(state);
    QString style;
    mPushButtonStateIN1 = true;
    mPushButtonStateNC = false;
    mPushButtonStateIN2 = false;
    mPushButtonStateIN12 = false;
    style = "QLabel {"
            "   background-color: transparent;"
            "   image: url(:/Icon/VerticalRoundedRectGreen.png);"
            "}";
    mLabelIN1.setStyleSheet(style);
    style = "QLabel {"
            "   background-color: transparent;"
            "   image: url(:/Icon/VerticalRoundedRectBlack.png);"
            "}";
    mLabelNC.setStyleSheet(style);
    mLabelIN2.setStyleSheet(style);
    mLabelIN12.setStyleSheet(style);
    style = "QPushButton {"
            "   text-align: left;"
            "   color: rgb(255, 255, 255);"
            "   background-color: transparent;"
            "}"
            "QPushButton:hover {"
            "   color: rgb(255, 255, 255);"
            "}";
    mPushButtonIN1.setStyleSheet(style);
    style = "QPushButton {"
            "   text-align: left;"
            "   color: rgb(161, 161, 161);"
            "   background-color: transparent;"
            "}"
            "QPushButton:hover {"
            "   color: rgb(255, 255, 255);"
            "}";
    mPushButtonNC.setStyleSheet(style);
    mPushButtonIN2.setStyleSheet(style);
    mPushButtonIN12.setStyleSheet(style);
    return *this;
}
PushButtonS1M5& PushButtonS1M5::setPushButtonStateIN2(bool state)
{
    Q_UNUSED(state);
    QString style;
    mPushButtonStateIN2 = true;
    mPushButtonStateNC = false;
    mPushButtonStateIN1 = false;
    mPushButtonStateIN12 = false;
    style = "QLabel {"
            "   background-color: transparent;"
            "   image: url(:/Icon/VerticalRoundedRectGreen.png);"
            "}";
    mLabelIN2.setStyleSheet(style);
    style = "QLabel {"
            "   background-color: transparent;"
            "   image: url(:/Icon/VerticalRoundedRectBlack.png);"
            "}";
    mLabelNC.setStyleSheet(style);
    mLabelIN1.setStyleSheet(style);
    mLabelIN12.setStyleSheet(style);
    style = "QPushButton {"
            "   text-align: left;"
            "   color: rgb(255, 255, 255);"
            "   background-color: transparent;"
            "}"
            "QPushButton:hover {"
            "   color: rgb(255, 255, 255);"
            "}";
    mPushButtonIN2.setStyleSheet(style);
    style = "QPushButton {"
            "   text-align: left;"
            "   color: rgb(161, 161, 161);"
            "   background-color: transparent;"
            "}"
            "QPushButton:hover {"
            "   color: rgb(255, 255, 255);"
            "}";
    mPushButtonNC.setStyleSheet(style);
    mPushButtonIN1.setStyleSheet(style);
    mPushButtonIN12.setStyleSheet(style);
    return *this;
}
PushButtonS1M5& PushButtonS1M5::setPushButtonStateIN12(bool state)
{
    Q_UNUSED(state);
    QString style;
    mPushButtonStateIN12 = true;
    mPushButtonStateNC = false;
    mPushButtonStateIN1 = false;
    mPushButtonStateIN2 = false;
    style = "QLabel {"
            "   background-color: transparent;"
            "   image: url(:/Icon/VerticalRoundedRectGreen.png);"
            "}";
    mLabelIN12.setStyleSheet(style);
    style = "QLabel {"
            "   background-color: transparent;"
            "   image: url(:/Icon/VerticalRoundedRectBlack.png);"
            "}";
    mLabelNC.setStyleSheet(style);
    mLabelIN1.setStyleSheet(style);
    mLabelIN2.setStyleSheet(style);
    style = "QPushButton {"
            "   text-align: left;"
            "   color: rgb(255, 255, 255);"
            "   background-color: transparent;"
            "}"
            "QPushButton:hover {"
            "   color: rgb(255, 255, 255);"
            "}";
    mPushButtonIN12.setStyleSheet(style);
    style = "QPushButton {"
            "   text-align: left;"
            "   color: rgb(161, 161, 161);"
            "   background-color: transparent;"
            "}"
            "QPushButton:hover {"
            "   color: rgb(255, 255, 255);"
            "}";
    mPushButtonNC.setStyleSheet(style);
    mPushButtonIN1.setStyleSheet(style);
    mPushButtonIN2.setStyleSheet(style);
    return *this;
}
PushButtonS1M5& PushButtonS1M5::setPushButtonStateOFF(bool state)
{
    Q_UNUSED(state);
    QString style;
    mPushButtonStateOFF = true;
    mPushButtonStateSTU = false;
    mPushButtonStateLIVE = false;
    mPushButtonStateHALL = false;
    style = "QPushButton {"
            "   border-radius: 3px;"
            "   color: rgb(229, 229, 229);"
            "   background-color: rgb(0, 126, 168);"
            "}"
            "QPushButton:hover {"
            "   border: 2px solid gray;"
            "   border-radius: 3px;"
            "}";
    mPushButtonOFF.setStyleSheet(style);
    style = "QPushButton {"
            "   border-radius: 3px;"
            "   color: rgb(161, 161, 161);"
            "   background-color: rgb(60, 60, 60);"
            "}"
            "QPushButton:hover {"
            "   border: 2px solid gray;"
            "   border-radius: 3px;"
            "}";
    mPushButtonSTU.setStyleSheet(style);
    mPushButtonLIVE.setStyleSheet(style);
    mPushButtonHALL.setStyleSheet(style);
    return *this;
}
PushButtonS1M5& PushButtonS1M5::setPushButtonStateSTU(bool state)
{
    Q_UNUSED(state);
    QString style;
    mPushButtonStateSTU = true;
    mPushButtonStateOFF = false;
    mPushButtonStateLIVE = false;
    mPushButtonStateHALL = false;
    style = "QPushButton {"
            "   border-radius: 3px;"
            "   color: rgb(229, 229, 229);"
            "   background-color: rgb(0, 126, 168);"
            "}"
            "QPushButton:hover {"
            "   border: 2px solid gray;"
            "   border-radius: 3px;"
            "}";
    mPushButtonSTU.setStyleSheet(style);
    style = "QPushButton {"
            "   border-radius: 3px;"
            "   color: rgb(161, 161, 161);"
            "   background-color: rgb(60, 60, 60);"
            "}"
            "QPushButton:hover {"
            "   border: 2px solid gray;"
            "   border-radius: 3px;"
            "}";
    mPushButtonOFF.setStyleSheet(style);
    mPushButtonLIVE.setStyleSheet(style);
    mPushButtonHALL.setStyleSheet(style);
    return *this;
}
PushButtonS1M5& PushButtonS1M5::setPushButtonStateLIVE(bool state)
{
    Q_UNUSED(state);
    QString style;
    mPushButtonStateLIVE = true;
    mPushButtonStateOFF = false;
    mPushButtonStateSTU = false;
    mPushButtonStateHALL = false;
    style = "QPushButton {"
            "   border-radius: 3px;"
            "   color: rgb(229, 229, 229);"
            "   background-color: rgb(0, 126, 168);"
            "}"
            "QPushButton:hover {"
            "   border: 2px solid gray;"
            "   border-radius: 3px;"
            "}";
    mPushButtonLIVE.setStyleSheet(style);
    style = "QPushButton {"
            "   border-radius: 3px;"
            "   color: rgb(161, 161, 161);"
            "   background-color: rgb(60, 60, 60);"
            "}"
            "QPushButton:hover {"
            "   border: 2px solid gray;"
            "   border-radius: 3px;"
            "}";
    mPushButtonOFF.setStyleSheet(style);
    mPushButtonSTU.setStyleSheet(style);
    mPushButtonHALL.setStyleSheet(style);
    return *this;
}
PushButtonS1M5& PushButtonS1M5::setPushButtonStateHALL(bool state)
{
    Q_UNUSED(state);
    QString style;
    mPushButtonStateHALL = true;
    mPushButtonStateOFF = false;
    mPushButtonStateSTU = false;
    mPushButtonStateLIVE = false;
    style = "QPushButton {"
            "   border-radius: 3px;"
            "   color: rgb(229, 229, 229);"
            "   background-color: rgb(0, 126, 168);"
            "}"
            "QPushButton:hover {"
            "   border: 2px solid gray;"
            "   border-radius: 3px;"
            "}";
    mPushButtonHALL.setStyleSheet(style);
    style = "QPushButton {"
            "   border-radius: 3px;"
            "   color: rgb(161, 161, 161);"
            "   background-color: rgb(60, 60, 60);"
            "}"
            "QPushButton:hover {"
            "   border: 2px solid gray;"
            "   border-radius: 3px;"
            "}";
    mPushButtonOFF.setStyleSheet(style);
    mPushButtonSTU.setStyleSheet(style);
    mPushButtonLIVE.setStyleSheet(style);
    return *this;
}
bool PushButtonS1M5::getPushButtonStateNC()
{
    return mPushButtonStateNC;
}
bool PushButtonS1M5::getPushButtonStateIN1()
{
    return mPushButtonStateIN1;
}
bool PushButtonS1M5::getPushButtonStateIN2()
{
    return mPushButtonStateIN2;
}
bool PushButtonS1M5::getPushButtonStateIN12()
{
    return mPushButtonStateIN12;
}
bool PushButtonS1M5::getPushButtonStateOFF()
{
    return mPushButtonStateOFF;
}
bool PushButtonS1M5::getPushButtonStateSTU()
{
    return mPushButtonStateSTU;
}
bool PushButtonS1M5::getPushButtonStateLIVE()
{
    return mPushButtonStateLIVE;
}
bool PushButtonS1M5::getPushButtonStateHALL()
{
    return mPushButtonStateHALL;
}

