#ifndef PUSHBUTTONGROUPS1M2_H
#define PUSHBUTTONGROUPS1M2_H


#include <QHash>
#include <QWidget>
#include <QResizeEvent>


namespace Ui {
class PushButtonGroupS1M2;
}


class PushButtonGroupS1M2 : public QWidget
{
    Q_OBJECT
public:
    explicit PushButtonGroupS1M2(QWidget* parent=nullptr);
    ~PushButtonGroupS1M2();
    PushButtonGroupS1M2& setFont(QFont font);
    PushButtonGroupS1M2& setLanguage(QString language);
    PushButtonGroupS1M2& setState(QString button, QString state, bool needEmit=true);
    QString getState(QString button);
protected:
    void resizeEvent(QResizeEvent* e) override;
    void showEvent(QShowEvent*) override { resizeEvent(nullptr); }
private:
    Ui::PushButtonGroupS1M2* ui;
    QFont mFont;
    unsigned int mBitmap=0;
    QHash<unsigned int, QString> mStyle;
private slots:
    void on_PushButtonSOLO_clicked(bool checked);
    void on_PushButtonMUTE_clicked(bool checked);
signals:
    void stateChanged(QString button, QString state);
};


#endif // PUSHBUTTONGROUPS1M2_H

