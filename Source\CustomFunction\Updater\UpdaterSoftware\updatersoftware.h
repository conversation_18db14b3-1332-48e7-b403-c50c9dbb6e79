#ifndef UPDATERSOFTWARE_H
#define UPDATERSOFTWARE_H

#include "updaterbase.h"

class UpdaterSoftware : public UpdaterBase
{
    Q_OBJECT
    friend class UpdaterFactory;
    
protected:
    void doUpdate(const QString& filePath) override;
    
private:
    explicit UpdaterSoftware(QObject* parent = nullptr);
    ~UpdaterSoftware();
    UpdaterSoftware(const UpdaterSoftware&) = delete;
    UpdaterSoftware& operator=(const UpdaterSoftware&) = delete;
    static UpdaterBase* instance();

#ifdef Q_OS_WIN
    bool installOnWindows(const QString& file);
#endif

#ifdef Q_OS_MACOS
    bool installOnMacOS(const QString& file);
#endif
};

#endif // UPDATERSOFTWARE_H

