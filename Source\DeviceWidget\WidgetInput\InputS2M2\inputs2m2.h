#ifndef InputS2M2_H
#define InputS2M2_H


#include <QFont>
#include <QEvent>
#include <QTimer>
#include <QObject>
#include <QWidget>
#include <QResizeEvent>
#include <QButtonGroup>

#include "originbase.h"
#include "workspace.h"
#include "appsettings.h"
#include "pushbuttons1m2.h"


namespace Ui {
class InputS2M2;
}


class InputS2M2 : public OriginBase, public WorkspaceObserver, public AppSettingsObserver
{
    Q_OBJECT
public:
    explicit InputS2M2(QWidget* parent=nullptr, QString name="");
    ~InputS2M2();
    InputS2M2& setName(QString name);
    InputS2M2& setFont(QFont font);
    void setTopVolumeMeter(int value);
    void setBottomVolumeMeter(int value);
    void setMute(bool state);
    void setGain(float value);

protected:
    void resizeEvent(QResizeEvent* e) override;
    void updateAttribute() override;
    void loadSettings() override;
    void AppSettingsChanged(QString objectName, QString attribute, QString value) override;
private:
    Ui::InputS2M2* ui;
    QFont mFont;
    int mPreMUTE=-2147483648;
    float mPreGAIN=-2147483648;
    void save(QAnyStringView key, const QVariant& value);
private slots:
    void in_widgetPushButtonGroup2_buttonStateChanged(PushButtonS1M2::ButtonID button, bool state);
    void on_lineEdit_textChanged(const QString& arg1);
    void on_lineEdit_editingFinished();
    void on_buttonMute_clicked(bool checked);
    void on_slider_valueChanged(int value);
};


#endif // InputS2M2_H

