#include <QRegularExpressionValidator>

#include "globalfont.h"
#include "hsliders2m1.h"


HSliderS2M1::HSliderS2M1(QWidget *parent)
    : QWidget(parent)
{    mLineEdit.setParent(this);
    mLineEdit.setAlignment(Qt::AlignCenter);
    mSlider.setParent(this);
    mSlider.setOrientation(Qt::Horizontal);
    mSlider.setPageStep(5);
    mSlider.installEventFilter(this);
    connect(&mLineEdit, SIGNAL(editingFinished()), this, SLOT(in_mLineEdit_editingFinished()));
    connect(&mSlider, SIGNAL(valueChanged(int)), this, SLOT(in_mSlider_valueChanged(int)));
    setRange(-50, 50).setDefault(0).setValue(18);
}
HSliderS2M1::~HSliderS2M1()
{

}


// override
bool HSliderS2M1::eventFilter(QObject* obj, QEvent* e)
{
    if(obj == &mSlider && mSlider.isEnabled())
    {
        if(e->type() == QEvent::MouseButtonDblClick)
        {
            mSlider.setValue(mValueDefault);
            return true;
        }
        else if(e->type() == QEvent::Wheel)
        {
            QWheelEvent *wheelEvent=static_cast<QWheelEvent *>(e);
            int numSteps=wheelEvent->angleDelta().y() / 120;
            mSlider.setValue(mSlider.value() + numSteps);
            return true;
        }
        else if(e->type() == QEvent::KeyPress)
        {
            QKeyEvent* keyEvent=static_cast<QKeyEvent*>(e);
            if(keyEvent->key() == Qt::Key_PageUp || keyEvent->key() == Qt::Key_PageDown || keyEvent->key() == Qt::Key_Home || keyEvent->key() == Qt::Key_End)
            {
                return true;
            }
        }
    }
    return QWidget::eventFilter(obj, e);
}
void HSliderS2M1::resizeEvent(QResizeEvent* e)
{
    Q_UNUSED(e);
    float wPixelPerRatio = width() / 100.0;
    int sliderWidth = wPixelPerRatio * 90;
    int lineEditWidth = width() - sliderWidth - wPixelPerRatio*4;
    
    mSlider.setGeometry(2*wPixelPerRatio, 0, sliderWidth, height());
    mLineEdit.setGeometry(mSlider.geometry().right()+2*wPixelPerRatio, (height() - height()*0.8)/2, lineEditWidth, height()*0.8);
    mFont.setPointSize(GLBFHandle.getSuitablePointSize(mFont, mLineEdit.text(), mLineEdit.rect())*0.8);
    mLineEdit.setFont(mFont);
    QString style;
    style = QString("QLineEdit {"
                    "   color: rgb(161, 161, 161);"
                    // "   background-color: rgb(46, 46, 46);"
                    "   background-color: transparent;"
                    "   border-radius: %1px;"
                    "   selection-color: rgb(0, 121, 107);"
                    "   selection-background-color: rgb(224, 247, 250);"
                    "}").arg(width() / 5);
    mLineEdit.setStyleSheet(style);
    style = QString("QSlider {"
                    "   background-color: transparent;"
                    "}"
                    "QSlider::groove:horizontal {"
                    "   background: #333333;"
                    "   border-radius: %1px;"
                    "   margin-top: %2px;"
                    "   margin-bottom: %2px;"
                    "}"
                    "QSlider::sub-page:horizontal {"
                    "   background: #CCCCCC;"
                    "   border-radius: %1px;"
                    "   margin-top: %2px;"
                    "   margin-bottom: %2px;"
                    "}"
                    "QSlider::handle:horizontal {"
                    "   border-image: url(:/Icon/SliderHandle.png);"
                    "   width: %3px;"
                    "   margin: -%2px -0px -%2px -1px;"
                    "}").arg(mSlider.height() / 8).arg(mSlider.height() / 10.0 * 3.7).arg(mSlider.height() / 10.0 * 6);
    mSlider.setStyleSheet(style);
}


// slot
void HSliderS2M1::in_mLineEdit_editingFinished()
{
    QString text=mLineEdit.text();
    text.remove("+");
    text.remove("-");
    text.remove("∞");
    if(text.isEmpty())
    {
        if(mLineEdit.text() == "-∞")
        {
            if(mSlider.value() != mSlider.minimum())
            {
                mSlider.setValue(mSlider.minimum());
            }
            else
            {
                in_mSlider_valueChanged(mSlider.minimum());
            }
        }
        else if(mLineEdit.text() == "+∞")
        {
            if(mSlider.value() != mSlider.maximum())
            {
                mSlider.setValue(mSlider.maximum());
            }
            else
            {
                in_mSlider_valueChanged(mSlider.maximum());
            }
        }
        else
        {
            in_mSlider_valueChanged(mSlider.value());
        }
    }
    else
    {
        if(mSlider.value() != mLineEdit.text().toInt())
        {
            mSlider.setValue(mLineEdit.text().toInt());
        }
        else
        {
            in_mSlider_valueChanged(mLineEdit.text().toInt());
        }
    }
    mLineEdit.clearFocus();
}
void HSliderS2M1::in_mSlider_valueChanged(int value)
{
    if(value == 0)
    {
        mLineEdit.setText("-" + QString::number(value));
    }
    else if(value > 0)
    {
        mLineEdit.setText("+" + QString::number(value));
    }
    else
    {
        mLineEdit.setText(QString::number(value));
    }
    if(value == mSlider.minimum() && mShowInfinitesimal)
    {
        mLineEdit.setText("-∞");
    }
    else if(value == mSlider.maximum() && mShowInfinity)
    {
        mLineEdit.setText("+∞");
    }
    if(mValue != value)
    {
        mValue = value;
        if(mEmitOpen)
        {
            emit valueChanged(mValue);
        }
    }
}


// setter & getter
HSliderS2M1& HSliderS2M1::setFont(QFont font)
{
    mFont = font;
    update();
    return *this;
}
HSliderS2M1& HSliderS2M1::setValue(int value)
{
    mEmitOpen = false;
    mSlider.setValue(value);
    mEmitOpen = true;
    return *this;
}
HSliderS2M1& HSliderS2M1::setStep(int value)
{
    mSlider.setSingleStep(value);
    return *this;
}
HSliderS2M1& HSliderS2M1::setDefault(int value)
{
    mValueDefault = value;
    return *this;
}
HSliderS2M1& HSliderS2M1::setRange(int minValue, int maxValue)
{
    QString regexStr("^(^$)|(\\-∞)|(\\+∞)|(\\-)|(\\+)|(");
    for(int i=minValue;i<=maxValue;i++)
    {
        if(i < 0)
        {
            regexStr += "\\";
            regexStr += QString::number(i);
            regexStr += "|";
        }
        else if(i == 0)
        {
            regexStr += "\\-";
            regexStr += QString::number(i);
            regexStr += "|";
            regexStr += QString::number(i);
            regexStr += "|";
            regexStr += "\\+";
            regexStr += QString::number(i);
            regexStr += "|";
        }
        else
        {
            regexStr += QString::number(i);
            regexStr += "|";
            regexStr += "\\+";
            regexStr += QString::number(i);
            regexStr += "|";
        }
    }
    regexStr.chop(1);
    regexStr += ")$";
    QRegularExpression reg(regexStr);
    mLineEdit.setValidator(new QRegularExpressionValidator(reg, this));
    mEmitOpen = false;
    mSlider.setRange(minValue, maxValue);
    mEmitOpen = true;
    return *this;
}
HSliderS2M1& HSliderS2M1::setHeightRatio(int text, int space, int Slider)
{
    mHText = text;
    mHSpace = space;
    mHSlider = Slider;
    return *this;
}
HSliderS2M1& HSliderS2M1::showInfinity(bool state)
{
    mShowInfinity = state;
    return *this;
}
HSliderS2M1& HSliderS2M1::showInfinitesimal(bool state)
{
    mShowInfinitesimal = state;
    return *this;
}

