#ifndef EqualizerS1M2_H
#define EqualizerS1M2_H

#include <qfont.h>
#include "equalizerbase.h"

namespace Ui {
class EqualizerS1M2;
}

class EqualizerS1M2 : public EqualizerBase
{
public:
    explicit EqualizerS1M2(QWidget *parent = nullptr);
    ~EqualizerS1M2();
    EqualizerS1M2& setName(const QString& name);
    EqualizerS1M2& setFont(const QFont& font);
    void setEqualizerBands(int count);
    void setEqualizerData(const QString& index, const QString& type, const QString& data);
    void setScaleFactor(double sizeFactor);

    void setStateTarget(bool state);
    void setStateSource(bool state);
    void setStateEachFilter(bool state);
    void setStateCombined(bool state);
    void setStateFiltered(bool state);
    void setFilteredType(int type);
    void setTargetFile(QString path);
    void setSourceFile(QString path);

    void setSwitchStatus(bool is, bool isSendSig = true);
    void setGainInputLeft(float value);
    void setGainInputRight(float value);
    void setGainOutputLeft(float value);
    void setGainOutputRight(float value);
    void setVolumeMeterInputLeft(int value);
    void setVolumeMeterInputRight(int value);
    void setVolumeMeterOutputLeft(int value);
    void setVolumeMeterOutputRight(int value);

protected:
    void setSizeFactor(double sizeFactor);
    void showEvent(QShowEvent* e);
    void paintEvent(QPaintEvent* e);

private:
    Ui::EqualizerS1M2 *ui;
    float mScaleRatio=1.0;
    QWidget* mWidget=nullptr;
    QFont mFont;
    QHash<QObject*,QHash<QString,QString>> mHashLanguages;;
};

#endif // EqualizerS1M2_H
