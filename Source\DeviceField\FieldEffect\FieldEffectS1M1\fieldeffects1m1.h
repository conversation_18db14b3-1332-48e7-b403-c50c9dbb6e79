#ifndef FIELDEFFECTS1M1_H
#define FIELDEFFECTS1M1_H


#include <QWidget>
#include <QVector>
#include <QString>

#include "workspace.h"
#include "appsettings.h"
#include "effectbase.h"
#include "fieldeffectbase1.h"


class FieldEffectS1M1 : public FieldEffectBase1, public WorkspaceObserver, public AppSettingsObserver
{
    Q_OBJECT
public:
    explicit FieldEffectS1M1(QWidget* parent=nullptr, QString name="");
    ~FieldEffectS1M1();
    FieldEffectS1M1& setName(QString name);
    FieldEffectS1M1& modifyWidgetList(QVector<EffectBase*> list);
    FieldEffectS1M1& setVisibleListDefault(QVector<EffectBase*> list);
protected:
    void loadSettings() override;
    void AppSettingsChanged(QString objectName, QString attribute, QString value) override;
private:
    QVector<EffectBase*> mWidgetList;
    QVariantList mVisibleListDefault;
private slots:
    void in_widgetBase_attributeChanged(QString objectName, QString attribute, QString value);
    void in_widgetList_attributeChanged(QString objectName, QString attribute, QString value);
signals:
    void attributeChanged(QString objectName, QString attribute, QString value);
};


#endif // FIELDEFFECTS1M1_H

