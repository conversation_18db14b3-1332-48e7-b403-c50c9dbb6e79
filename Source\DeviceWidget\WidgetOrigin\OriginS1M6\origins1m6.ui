<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>OriginS1M6</class>
 <widget class="QWidget" name="OriginS1M6">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>140</width>
    <height>280</height>
   </rect>
  </property>
  <property name="sizePolicy">
   <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
    <horstretch>0</horstretch>
    <verstretch>0</verstretch>
   </sizepolicy>
  </property>
  <property name="minimumSize">
   <size>
    <width>60</width>
    <height>220</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QGridLayout" name="gridLayout">
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <property name="spacing">
    <number>0</number>
   </property>
   <item row="0" column="0">
    <widget class="QFrame" name="frame">
     <property name="styleSheet">
      <string notr="true"/>
     </property>
     <property name="frameShape">
      <enum>QFrame::Shape::StyledPanel</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Shadow::Raised</enum>
     </property>
     <widget class="QPushButton" name="pushButtonClose">
      <property name="geometry">
       <rect>
        <x>110</x>
        <y>10</y>
        <width>21</width>
        <height>21</height>
       </rect>
      </property>
      <property name="text">
       <string/>
      </property>
     </widget>
     <widget class="QLineEdit" name="lineEdit">
      <property name="geometry">
       <rect>
        <x>10</x>
        <y>10</y>
        <width>91</width>
        <height>21</height>
       </rect>
      </property>
      <property name="sizePolicy">
       <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="styleSheet">
       <string notr="true"/>
      </property>
      <property name="alignment">
       <set>Qt::AlignmentFlag::AlignCenter</set>
      </property>
     </widget>
     <widget class="PushButtonS1M7" name="widgetPushButtonGroup1" native="true">
      <property name="geometry">
       <rect>
        <x>10</x>
        <y>230</y>
        <width>91</width>
        <height>40</height>
       </rect>
      </property>
      <property name="sizePolicy">
       <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="minimumSize">
       <size>
        <width>0</width>
        <height>0</height>
       </size>
      </property>
     </widget>
     <widget class="VolumeMeterS1M4" name="widgetLinkedMeterLeft" native="true">
      <property name="geometry">
       <rect>
        <x>10</x>
        <y>110</y>
        <width>21</width>
        <height>111</height>
       </rect>
      </property>
      <property name="sizePolicy">
       <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
     </widget>
     <widget class="VolumeMeterS1M5" name="widgetLinkedMeterRight" native="true">
      <property name="geometry">
       <rect>
        <x>80</x>
        <y>110</y>
        <width>21</width>
        <height>111</height>
       </rect>
      </property>
      <property name="sizePolicy">
       <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
     </widget>
     <widget class="VSliderS1M2" name="widgetLinkedVSlider" native="true">
      <property name="geometry">
       <rect>
        <x>40</x>
        <y>110</y>
        <width>31</width>
        <height>111</height>
       </rect>
      </property>
     </widget>
    </widget>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>VolumeMeterS1M4</class>
   <extends>QWidget</extends>
   <header location="global">volumemeters1m4.h</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>VolumeMeterS1M5</class>
   <extends>QWidget</extends>
   <header location="global">volumemeters1m5.h</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>PushButtonS1M7</class>
   <extends>QWidget</extends>
   <header location="global">pushbuttons1m7.h</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>VSliderS1M2</class>
   <extends>QWidget</extends>
   <header location="global">vsliders1m2.h</header>
   <container>1</container>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
