#include <QDebug>

#include "devicem62.h"
#include "usbaudioapi.h"


static int getAudioSourceCode(QString source)
{
    int code=-1;
    source == "Mix A"           ? (code = 1) : (0);
    source == "Mix B"           ? (code = 2) : (0);
    source == "Mix C"           ? (code = 3) : (0);
    source == "Mix D"           ? (code = 4) : (0);
    source == "Mix E"           ? (code = 5) : (0);
    source == "IN 1"            ? (code = 6) : (0);
    source == "IN 2"            ? (code = 7) : (0);
    source == "IN 1+2"          ? (code = 8) : (0);
    source == "AUX"             ? (code = 9) : (0);
    source == "BT"              ? (code = 10) : (0);
    source == "OTG IN"          ? (code = 11) : (0);
    source == "Playback 1/2"    ? (code = 12) : (0);
    source == "Playback 3/4"    ? (code = 13) : (0);
    source == "Playback 5/6"    ? (code = 14) : (0);
    source == "Playback 7/8"    ? (code = 15) : (0);
    source == "Playback 9/10"   ? (code = 16) : (0);
    source == "FX"              ? (code = 17) : (0);
    return code;
}
static DeviceM62::cmd getMixerChannelCode(QString channel)
{
    DeviceM62::cmd code=DeviceM62::M_none;
    channel == "Mixer_IN12L"    ? (code = DeviceM62::Ma_ch_9) : (0);
    channel == "Mixer_IN12R"    ? (code = DeviceM62::Ma_ch_10) : (0);
    channel == "Mixer_AUXL"     ? (code = DeviceM62::Ma_ch_11) : (0);
    channel == "Mixer_AUXR"     ? (code = DeviceM62::Ma_ch_12) : (0);
    channel == "Mixer_BTL"      ? (code = DeviceM62::Ma_ch_15) : (0);
    channel == "Mixer_BTR"      ? (code = DeviceM62::Ma_ch_16) : (0);
    channel == "Mixer_OTGINL"   ? (code = DeviceM62::Ma_ch_13) : (0);
    channel == "Mixer_OTGINR"   ? (code = DeviceM62::Ma_ch_14) : (0);
    channel == "Mixer_FXL"      ? (code = DeviceM62::Ma_ch_19) : (0);
    channel == "Mixer_FXR"      ? (code = DeviceM62::Ma_ch_20) : (0);
    channel == "Mixer_PB910L"   ? (code = DeviceM62::Ma_ch_17) : (0);
    channel == "Mixer_PB910R"   ? (code = DeviceM62::Ma_ch_18) : (0);
    channel == "Mixer_PB12L"    ? (code = DeviceM62::Ma_ch_1) : (0);
    channel == "Mixer_PB12R"    ? (code = DeviceM62::Ma_ch_2) : (0);
    channel == "Mixer_PB34L"    ? (code = DeviceM62::Ma_ch_3) : (0);
    channel == "Mixer_PB34R"    ? (code = DeviceM62::Ma_ch_4) : (0);
    channel == "Mixer_PB56L"    ? (code = DeviceM62::Ma_ch_5) : (0);
    channel == "Mixer_PB56R"    ? (code = DeviceM62::Ma_ch_6) : (0);
    channel == "Mixer_PB78L"    ? (code = DeviceM62::Ma_ch_7) : (0);
    channel == "Mixer_PB78R"    ? (code = DeviceM62::Ma_ch_8) : (0);
    return code;
}
static DeviceM62::cmd getMixerChannelAttributeCode(QString attribute)
{
    DeviceM62::cmd code=DeviceM62::P_none;
    attribute == "MixA_Link"                ? (code = DeviceM62::Pa_mixer_a_link) : (0);
    attribute == "MixA_BalanceLinkedLeft"   ? (code = DeviceM62::Pa_mixer_a_BalanceLinkedLeft) : (0);
    attribute == "MixA_BalanceLinkedRight"  ? (code = DeviceM62::Pa_mixer_a_BalanceLinkedRight) : (0);
    attribute == "MixA_BalanceUnlinkLeft"   ? (code = DeviceM62::Pa_mixer_a_BalanceUnlinkLeft) : (0);
    attribute == "MixA_BalanceUnlinkRight"  ? (code = DeviceM62::Pa_mixer_a_BalanceUnlinkRight) : (0);
    attribute == "MixA_GAIN"                ? (code = DeviceM62::Pa_mixer_a_GAIN) : (0);
    attribute == "MixA_GAINLeft"            ? (code = DeviceM62::Pa_mixer_a_GAINLeft) : (0);
    attribute == "MixA_GAINRight"           ? (code = DeviceM62::Pa_mixer_a_GAINRight) : (0);
    attribute == "MixA_SOLO"                ? (code = DeviceM62::Pa_mixer_a_SOLO) : (0);
    attribute == "MixA_SOLOLeft"            ? (code = DeviceM62::Pa_mixer_a_SOLOLeft) : (0);
    attribute == "MixA_SOLORight"           ? (code = DeviceM62::Pa_mixer_a_SOLORight) : (0);
    attribute == "MixA_MUTE"                ? (code = DeviceM62::Pa_mixer_a_MUTE) : (0);
    attribute == "MixA_MUTELeft"            ? (code = DeviceM62::Pa_mixer_a_MUTELeft) : (0);
    attribute == "MixA_MUTERight"           ? (code = DeviceM62::Pa_mixer_a_MUTERight) : (0);
    attribute == "MixA_Enable"              ? (code = DeviceM62::Pa_mixer_a_Enable) : (0);

    attribute == "MixB_Link"                ? (code = DeviceM62::Pa_mixer_b_link) : (0);
    attribute == "MixB_BalanceLinkedLeft"   ? (code = DeviceM62::Pa_mixer_b_BalanceLinkedLeft) : (0);
    attribute == "MixB_BalanceLinkedRight"  ? (code = DeviceM62::Pa_mixer_b_BalanceLinkedRight) : (0);
    attribute == "MixB_BalanceUnlinkLeft"   ? (code = DeviceM62::Pa_mixer_b_BalanceUnlinkLeft) : (0);
    attribute == "MixB_BalanceUnlinkRight"  ? (code = DeviceM62::Pa_mixer_b_BalanceUnlinkRight) : (0);
    attribute == "MixB_GAIN"                ? (code = DeviceM62::Pa_mixer_b_GAIN) : (0);
    attribute == "MixB_GAINLeft"            ? (code = DeviceM62::Pa_mixer_b_GAINLeft) : (0);
    attribute == "MixB_GAINRight"           ? (code = DeviceM62::Pa_mixer_b_GAINRight) : (0);
    attribute == "MixB_SOLO"                ? (code = DeviceM62::Pa_mixer_b_SOLO) : (0);
    attribute == "MixB_SOLOLeft"            ? (code = DeviceM62::Pa_mixer_b_SOLOLeft) : (0);
    attribute == "MixB_SOLORight"           ? (code = DeviceM62::Pa_mixer_b_SOLORight) : (0);
    attribute == "MixB_MUTE"                ? (code = DeviceM62::Pa_mixer_b_MUTE) : (0);
    attribute == "MixB_MUTELeft"            ? (code = DeviceM62::Pa_mixer_b_MUTELeft) : (0);
    attribute == "MixB_MUTERight"           ? (code = DeviceM62::Pa_mixer_b_MUTERight) : (0);
    attribute == "MixB_Enable"              ? (code = DeviceM62::Pa_mixer_b_Enable) : (0);

    attribute == "MixC_Link"                ? (code = DeviceM62::Pa_mixer_c_link) : (0);
    attribute == "MixC_BalanceLinkedLeft"   ? (code = DeviceM62::Pa_mixer_c_BalanceLinkedLeft) : (0);
    attribute == "MixC_BalanceLinkedRight"  ? (code = DeviceM62::Pa_mixer_c_BalanceLinkedRight) : (0);
    attribute == "MixC_BalanceUnlinkLeft"   ? (code = DeviceM62::Pa_mixer_c_BalanceUnlinkLeft) : (0);
    attribute == "MixC_BalanceUnlinkRight"  ? (code = DeviceM62::Pa_mixer_c_BalanceUnlinkRight) : (0);
    attribute == "MixC_GAIN"                ? (code = DeviceM62::Pa_mixer_c_GAIN) : (0);
    attribute == "MixC_GAINLeft"            ? (code = DeviceM62::Pa_mixer_c_GAINLeft) : (0);
    attribute == "MixC_GAINRight"           ? (code = DeviceM62::Pa_mixer_c_GAINRight) : (0);
    attribute == "MixC_SOLO"                ? (code = DeviceM62::Pa_mixer_c_SOLO) : (0);
    attribute == "MixC_SOLOLeft"            ? (code = DeviceM62::Pa_mixer_c_SOLOLeft) : (0);
    attribute == "MixC_SOLORight"           ? (code = DeviceM62::Pa_mixer_c_SOLORight) : (0);
    attribute == "MixC_MUTE"                ? (code = DeviceM62::Pa_mixer_c_MUTE) : (0);
    attribute == "MixC_MUTELeft"            ? (code = DeviceM62::Pa_mixer_c_MUTELeft) : (0);
    attribute == "MixC_MUTERight"           ? (code = DeviceM62::Pa_mixer_c_MUTERight) : (0);
    attribute == "MixC_Enable"              ? (code = DeviceM62::Pa_mixer_c_Enable) : (0);

    attribute == "MixD_Link"                ? (code = DeviceM62::Pa_mixer_d_link) : (0);
    attribute == "MixD_BalanceLinkedLeft"   ? (code = DeviceM62::Pa_mixer_d_BalanceLinkedLeft) : (0);
    attribute == "MixD_BalanceLinkedRight"  ? (code = DeviceM62::Pa_mixer_d_BalanceLinkedRight) : (0);
    attribute == "MixD_BalanceUnlinkLeft"   ? (code = DeviceM62::Pa_mixer_d_BalanceUnlinkLeft) : (0);
    attribute == "MixD_BalanceUnlinkRight"  ? (code = DeviceM62::Pa_mixer_d_BalanceUnlinkRight) : (0);
    attribute == "MixD_GAIN"                ? (code = DeviceM62::Pa_mixer_d_GAIN) : (0);
    attribute == "MixD_GAINLeft"            ? (code = DeviceM62::Pa_mixer_d_GAINLeft) : (0);
    attribute == "MixD_GAINRight"           ? (code = DeviceM62::Pa_mixer_d_GAINRight) : (0);
    attribute == "MixD_SOLO"                ? (code = DeviceM62::Pa_mixer_d_SOLO) : (0);
    attribute == "MixD_SOLOLeft"            ? (code = DeviceM62::Pa_mixer_d_SOLOLeft) : (0);
    attribute == "MixD_SOLORight"           ? (code = DeviceM62::Pa_mixer_d_SOLORight) : (0);
    attribute == "MixD_MUTE"                ? (code = DeviceM62::Pa_mixer_d_MUTE) : (0);
    attribute == "MixD_MUTELeft"            ? (code = DeviceM62::Pa_mixer_d_MUTELeft) : (0);
    attribute == "MixD_MUTERight"           ? (code = DeviceM62::Pa_mixer_d_MUTERight) : (0);
    attribute == "MixD_Enable"              ? (code = DeviceM62::Pa_mixer_d_Enable) : (0);
    return code;
}
static DeviceM62::cmd getEqualizerBandCode(QString band)
{
    DeviceM62::cmd code=DeviceM62::M_none;
    band == "1"     ? (code = DeviceM62::Ec_segment_1) : (0);
    band == "2"     ? (code = DeviceM62::Ec_segment_2) : (0);
    band == "3"     ? (code = DeviceM62::Ec_segment_3) : (0);
    band == "4"     ? (code = DeviceM62::Ec_segment_4) : (0);
    band == "5"     ? (code = DeviceM62::Ec_segment_5) : (0);
    band == "6"     ? (code = DeviceM62::Ec_segment_6) : (0);
    band == "7"     ? (code = DeviceM62::Ec_segment_7) : (0);
    band == "8"     ? (code = DeviceM62::Ec_segment_8) : (0);
    band == "9"     ? (code = DeviceM62::Ec_segment_9) : (0);
    band == "10"    ? (code = DeviceM62::Ec_segment_10) : (0);
    return code;
}
static DeviceM62::cmd getEqualizerAttributeCode(QString attribute)
{
    DeviceM62::cmd code=DeviceM62::M_none;
    attribute == "LType"    ? (code = DeviceM62::Ea_FilterType_l) : (0);
    attribute == "LGain"    ? (code = DeviceM62::Ea_gain_l) : (0);
    attribute == "LFreq"    ? (code = DeviceM62::Ea_Frequency_l) : (0);
    attribute == "LQ"       ? (code = DeviceM62::Ea_QualityValue_l) : (0);
    attribute == "LSwitch"  ? (code = DeviceM62::Ea_EnableState_l) : (0);
    attribute == "RType"    ? (code = DeviceM62::Ea_FilterType_r) : (0);
    attribute == "RGain"    ? (code = DeviceM62::Ea_gain_r) : (0);
    attribute == "RFreq"    ? (code = DeviceM62::Ea_Frequency_r) : (0);
    attribute == "RQ"       ? (code = DeviceM62::Ea_QualityValue_r) : (0);
    attribute == "RSwitch"  ? (code = DeviceM62::Ea_EnableState_r) : (0);
    return code;
}
int DeviceM62::GainToVolume_AUX(float Gain)
{
    if(Gain >= -10)
    {
        return 99 - (9 - Gain) / 0.5;
    }
    else if(Gain >= -52)
    {
        return 61 - (-10 - Gain) / 1;
    }
    else if(Gain >= -88)
    {
        return 19 - (-52 - Gain) / 2;
    }
    else
    {
        return 0;
    }
}
int DeviceM62::GainToVolume_BT(float Gain)
{
    if(Gain >= -10)
    {
        return 99 - (0 - Gain) / 0.5;
    }
    else if(Gain >= -88)
    {
        return 79 - (-10 - Gain) / 1;
    }
    else
    {
        return 0;
    }
}
int DeviceM62::GainToVolume_OTGIN(float Gain)
{
    if(Gain >= -10)
    {
        return 99 - (0 - Gain) / 0.5;
    }
    else if(Gain >= -88)
    {
        return 79 - (-10 - Gain) / 1;
    }
    else
    {
        return 0;
    }
}
int DeviceM62::GainToVolume_HP(float Gain)
{
    if(Gain >= -10)
    {
        return 99 - (9 - Gain) / 0.5;
    }
    else if(Gain >= -52)
    {
        return 61 - (-10 - Gain) / 1;
    }
    else if(Gain >= -88)
    {
        return 19 - (-52 - Gain) / 2;
    }
    else
    {
        return 0;
    }
}
int DeviceM62::GainToVolume_OTGOUT(float Gain)
{
    if(Gain >= -10)
    {
        return 99 - (0 - Gain) / 0.5;
    }
    else if(Gain >= -88)
    {
        return 79 - (-10 - Gain) / 1;
    }
    else
    {
        return 0;
    }
}
float DeviceM62::VolumeToGain_AUX(int Volume)
{
    if(Volume >= 62)
    {
        return -9.5 + (Volume - 62) * 0.5;
    }
    else if(Volume >= 20)
    {
        return -51 + (Volume - 20) * 1;
    }
    else if(Volume >= 1)
    {
        return -88 + (Volume - 1) * 2;
    }
    else
    {
        return -90;
    }
}
float DeviceM62::VolumeToGain_BT(int Volume)
{
    if(Volume >= 80)
    {
        return -9.5 + (Volume - 80) * 0.5;
    }
    else if(Volume >= 1)
    {
        return -88 + (Volume - 1) * 1;
    }
    else
    {
        return -89;
    }
}
float DeviceM62::VolumeToGain_OTGIN(int Volume)
{
    if(Volume >= 80)
    {
        return -9.5 + (Volume - 80) * 0.5;
    }
    else if(Volume >= 1)
    {
        return -88 + (Volume - 1) * 1;
    }
    else
    {
        return -89;
    }
}
float DeviceM62::VolumeToGain_HP(int Volume)
{
    if(Volume >= 62)
    {
        return -9.5 + (Volume - 62) * 0.5;
    }
    else if(Volume >= 20)
    {
        return -51 + (Volume - 20) * 1;
    }
    else if(Volume >= 1)
    {
        return -88 + (Volume - 1) * 2;
    }
    else
    {
        return -90;
    }
}
float DeviceM62::VolumeToGain_OTGOUT(int Volume)
{
    if(Volume >= 80)
    {
        return -9.5 + (Volume - 80) * 0.5;
    }
    else if(Volume >= 1)
    {
        return -88 + (Volume - 1) * 1;
    }
    else
    {
        return -89;
    }
}
void DeviceM62::in_fieldSystem_attributeChanged(QString objectName, QString attribute, QString value)
{
    DeviceType1::FrameInfo frame;
    frame.protocol = DeviceType1::write_nack;
    frame.current = 1;
    frame.total = 1;
    if(objectName == "MainWindow")
    {
        if(attribute == "Auth")
        {
            frame.cmd = makeCMD(cmdS, Sc_running, Sa_connect_state);
            frame.data = value.toInt();
            sendFrame(frame);
        }
    }
}
void DeviceM62::in_fieldHead_attributeChanged(QString objectName, QString attribute, QString value)
{
    if(attribute.startsWith("Save_"))
    {
        // doActionSaveFieldHead(objectName, attribute, value);
        return;
    }
    DeviceType1::FrameInfo frame;
    frame.protocol = DeviceType1::write_nack;
    frame.current = 1;
    frame.total = 1;
    if(objectName == "WorkspaceDownload")
    {
        if(attribute == "Clicked")
        {
            frame.cmd = makeCMD(cmdS, Sc_running, Sa_download);
            frame.data = value.toInt();
            sendFrame(frame);
        }
    }
}
void DeviceM62::in_fieldInput_attributeChanged(QString objectName, QString attribute, QString value)
{
    if(attribute.startsWith("Save_"))
    {
        // doActionSaveFieldInput(objectName, attribute, value);
        return;
    }
    DeviceType1::FrameInfo frame;
    frame.protocol = DeviceType1::write_nack;
    frame.current = 1;
    frame.total = 1;
    if(objectName == "Input_IN1")
    {
        if(attribute == "MIC")
        {
            frame.cmd = makeCMD(cmdI, Ic_ch_1, Ia_mic);
            value == "Mic1" ? (frame.data = 1) : (0);
            value == "Mic35" ? (frame.data = 2) : (0);
            value == "MicHP" ? (frame.data = 3) : (0);
            sendFrame(frame);
        }
        else if(attribute == "48V")
        {
            frame.cmd = makeCMD(cmdI, Ic_ch_1, Ia_48v);
            frame.data = value.toInt();
            sendFrame(frame);
        }
        else if(attribute == "GAIN")
        {
            frame.cmd = makeCMD(cmdI, Ic_ch_1, Ia_gain);
            frame.data = value.toInt();
            sendFrame(frame);
        }
        else if(attribute == "MUTE")
        {
            frame.cmd = makeCMD(cmdI, Ic_ch_1, Ia_mute);
            frame.data = value.toInt();
            sendFrame(frame);
        }
        else if(attribute == "ANTI")
        {
            frame.cmd = makeCMD(cmdI, Ic_ch_1, Ia_anti);
            frame.data = value.toInt();
            sendFrame(frame);
        }
    }
    else if(objectName == "Input_IN2")
    {
        if(attribute == "48V")
        {
            frame.cmd = makeCMD(cmdI, Ic_ch_2, Ia_48v);
            frame.data = value.toInt();
            sendFrame(frame);
        }
        else if(attribute == "GAIN")
        {
            frame.cmd = makeCMD(cmdI, Ic_ch_2, Ia_gain);
            frame.data = value.toInt();
            sendFrame(frame);
        }
        else if(attribute == "MUTE")
        {
            frame.cmd = makeCMD(cmdI, Ic_ch_2, Ia_mute);
            frame.data = value.toInt();
            sendFrame(frame);
        }
        else if(attribute == "ANTI")
        {
            frame.cmd = makeCMD(cmdI, Ic_ch_2, Ia_anti);
            frame.data = value.toInt();
            sendFrame(frame);
        }
    }
    else if(objectName == "Input_AUX")
    {
        if(attribute == "GAIN")
        {
            frame.cmd = makeCMD(cmdI, Ic_ch_3, Ia_gain);
            frame.data = GainToVolume_AUX(value.toFloat());
            sendFrame(frame);
        }
        else if(attribute == "MUTE")
        {
            frame.cmd = makeCMD(cmdI, Ic_ch_3, Ia_mute);
            frame.data = value.toInt();
            sendFrame(frame);
        }
        else if(attribute == "ANTI")
        {
            frame.cmd = makeCMD(cmdI, Ic_ch_3, Ia_anti);
            frame.data = value.toInt();
            sendFrame(frame);
        }
    }
    else if(objectName == "Input_BT")
    {
        if(attribute == "GAIN")
        {
            frame.cmd = makeCMD(cmdI, Ic_ch_5, Ia_gain);
            frame.data = GainToVolume_BT(value.toFloat());
            sendFrame(frame);
        }
        else if(attribute == "MUTE")
        {
            frame.cmd = makeCMD(cmdI, Ic_ch_5, Ia_mute);
            frame.data = value.toInt();
            sendFrame(frame);
        }
        else if(attribute == "ANTI")
        {
            frame.cmd = makeCMD(cmdI, Ic_ch_5, Ia_anti);
            frame.data = value.toInt();
            sendFrame(frame);
        }
    }
    else if(objectName == "Input_OTGIN")
    {
        if(attribute == "GAIN")
        {
            frame.cmd = makeCMD(cmdI, Ic_ch_7, Ia_gain);
            frame.data = GainToVolume_OTGIN(value.toFloat());
            sendFrame(frame);
        }
        else if(attribute == "MUTE")
        {
            frame.cmd = makeCMD(cmdI, Ic_ch_7, Ia_mute);
            frame.data = value.toInt();
            sendFrame(frame);
        }
        else if(attribute == "ANTI")
        {
            frame.cmd = makeCMD(cmdI, Ic_ch_7, Ia_anti);
            frame.data = value.toInt();
            sendFrame(frame);
        }
    }
}
void DeviceM62::in_fieldMixer_attributeChanged(QString objectName, QString attribute, QString value)
{
    if(attribute.startsWith("Save_"))
    {
        // doActionSaveFieldMixer(objectName, attribute, value);
        return;
    }
    DeviceType1::FrameInfo frame;
    frame.protocol = DeviceType1::write_nack;
    frame.current = 1;
    frame.total = 1;
    if(attribute.contains("MixA"))
    {
        if(attribute.contains("GainMLCL"))
        {
            frame.cmd = makeCMD(cmdM, Mc_mixer_al, getMixerChannelCode(objectName + "L"));
            frame.data = value.toInt();
            sendFrame(frame);
        }
        else if(attribute.contains("GainMLCR"))
        {
            frame.cmd = makeCMD(cmdM, Mc_mixer_al, getMixerChannelCode(objectName + "R"));
            frame.data = value.toInt();
            sendFrame(frame);
        }
        else if(attribute.contains("GainMRCL"))
        {
            frame.cmd = makeCMD(cmdM, Mc_mixer_ar, getMixerChannelCode(objectName + "L"));
            frame.data = value.toInt();
            sendFrame(frame);
        }
        else if(attribute.contains("GainMRCR"))
        {
            frame.cmd = makeCMD(cmdM, Mc_mixer_ar, getMixerChannelCode(objectName + "R"));
            frame.data = value.toInt();
            sendFrame(frame);
        }
    }
    else if(attribute.contains("MixB"))
    {
        if(attribute.contains("GainMLCL"))
        {
            frame.cmd = makeCMD(cmdM, Mc_mixer_bl, getMixerChannelCode(objectName + "L"));
            frame.data = value.toInt();
            sendFrame(frame);
        }
        else if(attribute.contains("GainMLCR"))
        {
            frame.cmd = makeCMD(cmdM, Mc_mixer_bl, getMixerChannelCode(objectName + "R"));
            frame.data = value.toInt();
            sendFrame(frame);
        }
        else if(attribute.contains("GainMRCL"))
        {
            frame.cmd = makeCMD(cmdM, Mc_mixer_br, getMixerChannelCode(objectName + "L"));
            frame.data = value.toInt();
            sendFrame(frame);
        }
        else if(attribute.contains("GainMRCR"))
        {
            frame.cmd = makeCMD(cmdM, Mc_mixer_br, getMixerChannelCode(objectName + "R"));
            frame.data = value.toInt();
            sendFrame(frame);
        }
    }
    else if(attribute.contains("MixC"))
    {
        if(attribute.contains("GainMLCL"))
        {
            frame.cmd = makeCMD(cmdM, Mc_mixer_cl, getMixerChannelCode(objectName + "L"));
            frame.data = value.toInt();
            sendFrame(frame);
        }
        else if(attribute.contains("GainMLCR"))
        {
            frame.cmd = makeCMD(cmdM, Mc_mixer_cl, getMixerChannelCode(objectName + "R"));
            frame.data = value.toInt();
            sendFrame(frame);
        }
        else if(attribute.contains("GainMRCL"))
        {
            frame.cmd = makeCMD(cmdM, Mc_mixer_cr, getMixerChannelCode(objectName + "L"));
            frame.data = value.toInt();
            sendFrame(frame);
        }
        else if(attribute.contains("GainMRCR"))
        {
            frame.cmd = makeCMD(cmdM, Mc_mixer_cr, getMixerChannelCode(objectName + "R"));
            frame.data = value.toInt();
            sendFrame(frame);
        }
    }
    else if(attribute.contains("MixD"))
    {
        if(attribute.contains("GainMLCL"))
        {
            frame.cmd = makeCMD(cmdM, Mc_mixer_dl, getMixerChannelCode(objectName + "L"));
            frame.data = value.toInt();
            sendFrame(frame);
        }
        else if(attribute.contains("GainMLCR"))
        {
            frame.cmd = makeCMD(cmdM, Mc_mixer_dl, getMixerChannelCode(objectName + "R"));
            frame.data = value.toInt();
            sendFrame(frame);
        }
        else if(attribute.contains("GainMRCL"))
        {
            frame.cmd = makeCMD(cmdM, Mc_mixer_dr, getMixerChannelCode(objectName + "L"));
            frame.data = value.toInt();
            sendFrame(frame);
        }
        else if(attribute.contains("GainMRCR"))
        {
            frame.cmd = makeCMD(cmdM, Mc_mixer_dr, getMixerChannelCode(objectName + "R"));
            frame.data = value.toInt();
            sendFrame(frame);
        }
    }
    else if(attribute.contains("MixE"))
    {
        if(attribute.contains("GainMLCL"))
        {
            frame.cmd = makeCMD(cmdM, Mc_mixer_el, getMixerChannelCode(objectName + "L"));
            frame.data = value.toInt();
            sendFrame(frame);
        }
        else if(attribute.contains("GainMLCR"))
        {
            frame.cmd = makeCMD(cmdM, Mc_mixer_el, getMixerChannelCode(objectName + "R"));
            frame.data = value.toInt();
            sendFrame(frame);
        }
        else if(attribute.contains("GainMRCL"))
        {
            frame.cmd = makeCMD(cmdM, Mc_mixer_er, getMixerChannelCode(objectName + "L"));
            frame.data = value.toInt();
            sendFrame(frame);
        }
        else if(attribute.contains("GainMRCR"))
        {
            frame.cmd = makeCMD(cmdM, Mc_mixer_er, getMixerChannelCode(objectName + "R"));
            frame.data = value.toInt();
            sendFrame(frame);
        }
    }
}
void DeviceM62::in_fieldEffect_attributeChanged(QString objectName, QString attribute, QString value)
{
    if(attribute.startsWith("Save_"))
    {
        // doActionSaveFieldEffect(objectName, attribute, value);
        return;
    }
    DeviceType1::FrameInfo frame;
    frame.protocol = DeviceType1::write_nack;
    frame.current = 1;
    frame.total = 1;
    if(objectName == "Effect_Integration")
    {
        if(attribute == "Input1Level")
        {
            frame.cmd = makeCMD(cmdE, Ec_integration, Ea_integration_gain_in1);
            frame.data = USBAHandle.LogToGain(value.toDouble(), -90);
            sendFrame(frame);
        }
        else if(attribute == "Input2Level")
        {
            frame.cmd = makeCMD(cmdE, Ec_integration, Ea_integration_gain_in2);
            frame.data = USBAHandle.LogToGain(value.toDouble(), -90);
            sendFrame(frame);
        }
        else if(attribute == "AuxLevel")
        {
            frame.cmd = makeCMD(cmdE, Ec_integration, Ea_integration_gain_aux);
            frame.data = USBAHandle.LogToGain(value.toDouble(), -90);
            sendFrame(frame);
        }
        else if(attribute == "BluetoothLevel")
        {
            frame.cmd = makeCMD(cmdE, Ec_integration, Ea_integration_gain_bt);
            frame.data = USBAHandle.LogToGain(value.toDouble(), -90);
            sendFrame(frame);
        }
        else if(attribute == "OtgLevel")
        {
            frame.cmd = makeCMD(cmdE, Ec_integration, Ea_integration_gain_otg);
            frame.data = USBAHandle.LogToGain(value.toDouble(), -90);
            sendFrame(frame);
        }
        else if(attribute == "NoiseReduction")
        {
            frame.cmd = makeCMD(cmdE, Ec_integration, Ea_integration_nc_level);
            frame.data = value.toInt();
            sendFrame(frame);
        }
        else if(attribute == "NoiseReductionType")
        {
            frame.cmd = makeCMD(cmdE, Ec_integration, Ea_integration_nc_type);
            value == "Bypass" ? (frame.data = 0) : (0);
            value == "NC1" ? (frame.data = 15) : (0);
            value == "NC2" ? (frame.data = 30) : (0);
            value == "NC3" ? (frame.data = 45) : (0);
            sendFrame(frame);
        }
        else if(attribute == "DryWet")
        {
            frame.cmd = makeCMD(cmdE, Ec_integration, Ea_integration_reverb_drywet);
            frame.data = value.toInt();
            sendFrame(frame);
        }
        else if(attribute == "Room")
        {
            frame.cmd = makeCMD(cmdE, Ec_integration, Ea_integration_reverb_room);
            frame.data = value.toInt();
            sendFrame(frame);
        }
        else if(attribute == "Decay")
        {
            frame.cmd = makeCMD(cmdE, Ec_integration, Ea_integration_reverb_decay);
            frame.data = value.toInt();
            sendFrame(frame);
        }
        else if(attribute == "ReverbType")
        {
            frame.cmd = makeCMD(cmdE, Ec_integration, Ea_integration_reverb_type);
            value == "Bypass" ? (frame.data = 1) : (0);
            value == "STUDIO" ? (frame.data = 2) : (0);
            value == "LIVE" ? (frame.data = 3) : (0);
            value == "HALL" ? (frame.data = 4) : (0);
            sendFrame(frame);
        }
        else if(attribute == "MuteFX")
        {
            frame.cmd = makeCMD(cmdE, Ec_integration, Ea_integration_mute);
            frame.data = value.toInt();
            sendFrame(frame);
        }
    }
    else if(objectName == "Effect_NC")
    {
        if(attribute == "NoiseReduction")
        {
            frame.cmd = makeCMD(cmdE, Ec_integration, Ea_integration_nc_level);
            frame.data = value.toInt();
            sendFrame(frame);
        }
        else if(attribute == "NoiseReductionType")
        {
            frame.cmd = makeCMD(cmdE, Ec_integration, Ea_integration_nc_type);
            value == "Bypass" ? (frame.data = 0) : (0);
            value == "NC1" ? (frame.data = 15) : (0);
            value == "NC2" ? (frame.data = 30) : (0);
            value == "NC3" ? (frame.data = 45) : (0);
            sendFrame(frame);
        }
    }
    else if(objectName == "Effect_Reverb")
    {
        if(attribute == "DryWet")
        {
            frame.cmd = makeCMD(cmdE, Ec_integration, Ea_integration_reverb_drywet);
            frame.data = value.toInt();
            sendFrame(frame);
        }
        else if(attribute == "Room")
        {
            frame.cmd = makeCMD(cmdE, Ec_integration, Ea_integration_reverb_room);
            frame.data = value.toInt();
            sendFrame(frame);
        }
        else if(attribute == "Decay")
        {
            frame.cmd = makeCMD(cmdE, Ec_integration, Ea_integration_reverb_decay);
            frame.data = value.toInt();
            sendFrame(frame);
        }
        else if(attribute == "ReverbType")
        {
            frame.cmd = makeCMD(cmdE, Ec_integration, Ea_integration_reverb_type);
            value == "Bypass" ? (frame.data = 1) : (0);
            value == "STUDIO" ? (frame.data = 2) : (0);
            value == "LIVE" ? (frame.data = 3) : (0);
            value == "HALL" ? (frame.data = 4) : (0);
            sendFrame(frame);
        }
    }
}
void DeviceM62::in_fieldLoopback_attributeChanged(QString objectName, QString attribute, QString value)
{
    if(attribute.startsWith("Save_"))
    {
        // doActionSaveFieldLoopback(objectName, attribute, value);
        return;
    }
    DeviceType1::FrameInfo frame;
    frame.protocol = DeviceType1::write_nack;
    frame.current = 1;
    frame.total = 1;
    if(objectName == "Loopback_LB12")
    {
        if(attribute == "AudioSource")
        {
            frame.cmd = makeCMD(cmdL, Lc_ch_1, La_audio_source);
            frame.data = getAudioSourceCode(value);
            sendFrame(frame);
        }
        else if(attribute == "GAINLeft")
        {
            frame.cmd = makeCMD(cmdL, Lc_ch_1, La_gain);
            frame.data = USBAHandle.LogToGain(value.toDouble(), -90);
            sendFrame(frame);
        }
        else if(attribute == "GAINRight")
        {
            frame.cmd = makeCMD(cmdL, Lc_ch_2, La_gain);
            frame.data = USBAHandle.LogToGain(value.toDouble(), -90);
            sendFrame(frame);
        }
    }
    else if(objectName == "Loopback_LB34")
    {
        if(attribute == "AudioSource")
        {
            frame.cmd = makeCMD(cmdL, Lc_ch_3, La_audio_source);
            frame.data = getAudioSourceCode(value);
            sendFrame(frame);
        }
        else if(attribute == "GAINLeft")
        {
            frame.cmd = makeCMD(cmdL, Lc_ch_3, La_gain);
            frame.data = USBAHandle.LogToGain(value.toDouble(), -90);
            sendFrame(frame);
        }
        else if(attribute == "GAINRight")
        {
            frame.cmd = makeCMD(cmdL, Lc_ch_4, La_gain);
            frame.data = USBAHandle.LogToGain(value.toDouble(), -90);
            sendFrame(frame);
        }
    }
    else if(objectName == "Loopback_LB56")
    {
        if(attribute == "AudioSource")
        {
            frame.cmd = makeCMD(cmdL, Lc_ch_5, La_audio_source);
            frame.data = getAudioSourceCode(value);
            sendFrame(frame);
        }
        else if(attribute == "GAINLeft")
        {
            frame.cmd = makeCMD(cmdL, Lc_ch_5, La_gain);
            frame.data = USBAHandle.LogToGain(value.toDouble(), -90);
            sendFrame(frame);
        }
        else if(attribute == "GAINRight")
        {
            frame.cmd = makeCMD(cmdL, Lc_ch_6, La_gain);
            frame.data = USBAHandle.LogToGain(value.toDouble(), -90);
            sendFrame(frame);
        }
    }
    else if(objectName == "Loopback_LB78")
    {
        if(attribute == "AudioSource")
        {
            frame.cmd = makeCMD(cmdL, Lc_ch_7, La_audio_source);
            frame.data = getAudioSourceCode(value);
            sendFrame(frame);
        }
        else if(attribute == "GAINLeft")
        {
            frame.cmd = makeCMD(cmdL, Lc_ch_7, La_gain);
            frame.data = USBAHandle.LogToGain(value.toDouble(), -90);
            sendFrame(frame);
        }
        else if(attribute == "GAINRight")
        {
            frame.cmd = makeCMD(cmdL, Lc_ch_8, La_gain);
            frame.data = USBAHandle.LogToGain(value.toDouble(), -90);
            sendFrame(frame);
        }
    }
}
void DeviceM62::in_fieldOutput_attributeChanged(QString objectName, QString attribute, QString value)
{
    if(attribute.startsWith("Save_"))
    {
        // doActionSaveFieldOutput(objectName, attribute, value);
        return;
    }
    DeviceType1::FrameInfo frame;
    frame.protocol = DeviceType1::write_nack;
    frame.current = 1;
    frame.total = 1;
    if(objectName == "Output_HP")
    {
        if(attribute == "AudioSource")
        {
            frame.cmd = makeCMD(cmdO, Oc_ch_4, Oa_audio_source);
            frame.data = getAudioSourceCode(value);
            sendFrame(frame);
        }
        else if(attribute == "GAINLeft")
        {
            frame.cmd = makeCMD(cmdO, Oc_ch_3, Oa_gain);
            frame.data = GainToVolume_HP(value.toFloat());
            sendFrame(frame);
        }
        else if(attribute == "GAINRight")
        {
            frame.cmd = makeCMD(cmdO, Oc_ch_4, Oa_gain);
            frame.data = GainToVolume_HP(value.toFloat());
            sendFrame(frame);
        }
        else if(attribute == "MUTE")
        {
            frame.cmd = makeCMD(cmdO, Oc_ch_3, Oa_mute);
            frame.data = value.toInt();
            sendFrame(frame);
            frame.cmd = makeCMD(cmdO, Oc_ch_4, Oa_mute);
            frame.data = value.toInt();
            sendFrame(frame);
        }
        else if(attribute == "MUTELeft")
        {
            frame.cmd = makeCMD(cmdO, Oc_ch_3, Oa_mute);
            frame.data = value.toInt();
            sendFrame(frame);
        }
        else if(attribute == "MUTERight")
        {
            frame.cmd = makeCMD(cmdO, Oc_ch_4, Oa_mute);
            frame.data = value.toInt();
            sendFrame(frame);
        }
    }
    else if(objectName == "Output_OTGOUT")
    {
        if(attribute == "AudioSource")
        {
            frame.cmd = makeCMD(cmdO, Oc_ch_2, Oa_audio_source);
            frame.data = getAudioSourceCode(value);
            sendFrame(frame);
        }
        else if(attribute == "GAINLeft")
        {
            frame.cmd = makeCMD(cmdO, Oc_ch_1, Oa_gain);
            frame.data = GainToVolume_OTGOUT(value.toFloat());
            sendFrame(frame);
        }
        else if(attribute == "GAINRight")
        {
            frame.cmd = makeCMD(cmdO, Oc_ch_2, Oa_gain);
            frame.data = GainToVolume_OTGOUT(value.toFloat());
            sendFrame(frame);
        }
        else if(attribute == "MUTE")
        {
            frame.cmd = makeCMD(cmdO, Oc_ch_1, Oa_mute);
            frame.data = value.toInt();
            sendFrame(frame);
            frame.cmd = makeCMD(cmdO, Oc_ch_2, Oa_mute);
            frame.data = value.toInt();
            sendFrame(frame);
        }
        else if(attribute == "MUTELeft")
        {
            frame.cmd = makeCMD(cmdO, Oc_ch_1, Oa_mute);
            frame.data = value.toInt();
            sendFrame(frame);
        }
        else if(attribute == "MUTERight")
        {
            frame.cmd = makeCMD(cmdO, Oc_ch_2, Oa_mute);
            frame.data = value.toInt();
            sendFrame(frame);
        }
    }
}
void DeviceM62::in_Others_attributeChanged(QString objectName, QString attribute, QString value)
{
    if(attribute.startsWith("Save_"))
    {
        // doActionSaveOthers(objectName, attribute, value);
        return;
    }
    DeviceType1::FrameInfo frame;
    frame.protocol = DeviceType1::write_nack;
    frame.current = 1;
    frame.total = 1;
    if(objectName == "ConfigMenuMble")
    {
        if(attribute == "Chat")
        {
            frame.cmd = makeCMD(cmdE, Ec_scene, Ea_Chat);
            frame.data = 1;
            sendFrame(frame);
        }
        else if(attribute == "Vocal")
        {
            frame.cmd = makeCMD(cmdE, Ec_scene, Ea_Vocal);
            frame.data = 1;
            sendFrame(frame);
        }
        else if(attribute == "Load preset1")
        {
            frame.cmd = makeCMD(cmdS, Sc_running, Sa_load_user1_live);
            frame.data = 1;
            sendFrame(frame);
        }
        else if(attribute == "Load preset2")
        {
            frame.cmd = makeCMD(cmdS, Sc_running, Sa_load_user2_live);
            frame.data = 1;
            sendFrame(frame);
        }
        else if(attribute == "Save preset1")
        {
            frame.cmd = makeCMD(cmdS, Sc_running, Sa_save_user1_live);
            frame.data = 1;
            sendFrame(frame);
        }
        else if(attribute == "Save preset2")
        {
            frame.cmd = makeCMD(cmdS, Sc_running, Sa_save_user2_live);
            frame.data = 1;
            sendFrame(frame);
        }
    }
    else if(objectName == "ConfigMenuLive")
    {
        if(attribute == "Chat")
        {
            frame.cmd = makeCMD(cmdE, Ec_scene, Ea_Chat);
            frame.data = 1;
            sendFrame(frame);
        }
        else if(attribute == "Vocal")
        {
            frame.cmd = makeCMD(cmdE, Ec_scene, Ea_Vocal);
            frame.data = 1;
            sendFrame(frame);
        }
        else if(attribute == "Load preset1")
        {
            frame.cmd = makeCMD(cmdS, Sc_running, Sa_load_user1_live);
            frame.data = 1;
            sendFrame(frame);
        }
        else if(attribute == "Load preset2")
        {
            frame.cmd = makeCMD(cmdS, Sc_running, Sa_load_user2_live);
            frame.data = 1;
            sendFrame(frame);
        }
        else if(attribute == "Save preset1")
        {
            frame.cmd = makeCMD(cmdS, Sc_running, Sa_save_user1_live);
            frame.data = 1;
            sendFrame(frame);
        }
        else if(attribute == "Save preset2")
        {
            frame.cmd = makeCMD(cmdS, Sc_running, Sa_save_user2_live);
            frame.data = 1;
            sendFrame(frame);
        }
    }
    else if(objectName == "ConfigMenuTyro")
    {
        if(attribute == "DuckingSwitch")
        {
            frame.cmd = makeCMD(cmdE, Ec_ducking, Ea_ducking_DuckingSwitch);
            frame.data = value.toInt();
            sendFrame(frame);
        }
        else if(attribute == "Load preset1")
        {
            frame.cmd = makeCMD(cmdS, Sc_running, Sa_load_user1_live);
            frame.data = 1;
            sendFrame(frame);
        }
        else if(attribute == "Load preset2")
        {
            frame.cmd = makeCMD(cmdS, Sc_running, Sa_load_user2_live);
            frame.data = 1;
            sendFrame(frame);
        }
        else if(attribute == "Save preset1")
        {
            frame.cmd = makeCMD(cmdS, Sc_running, Sa_save_user1_live);
            frame.data = 1;
            sendFrame(frame);
        }
        else if(attribute == "Save preset2")
        {
            frame.cmd = makeCMD(cmdS, Sc_running, Sa_save_user2_live);
            frame.data = 1;
            sendFrame(frame);
        }
    }
    else if(objectName == "ConfigMenuProf")
    {
        if(attribute == "Load preset1")
        {
            frame.cmd = makeCMD(cmdS, Sc_running, Sa_load_user1_live);
            frame.data = 1;
            sendFrame(frame);
        }
        else if(attribute == "Load preset2")
        {
            frame.cmd = makeCMD(cmdS, Sc_running, Sa_load_user2_live);
            frame.data = 1;
            sendFrame(frame);
        }
        else if(attribute == "Save preset1")
        {
            frame.cmd = makeCMD(cmdS, Sc_running, Sa_save_user1_live);
            frame.data = 1;
            sendFrame(frame);
        }
        else if(attribute == "Save preset2")
        {
            frame.cmd = makeCMD(cmdS, Sc_running, Sa_save_user2_live);
            frame.data = 1;
            sendFrame(frame);
        }
    }
    else if(objectName == "Ducking")
    {
        if(attribute == "Threshold")
        {
            frame.cmd = makeCMD(cmdE, Ec_ducking, Ea_ducking_threshold);
            frame.data = value.toInt();
            sendFrame(frame);
        }
        else if(attribute == "Attack")
        {
            frame.cmd = makeCMD(cmdE, Ec_ducking, Ea_ducking_attack);
            frame.data = value.toInt();
            sendFrame(frame);
        }
        else if(attribute == "Reduction")
        {
            frame.cmd = makeCMD(cmdE, Ec_ducking, Ea_ducking_reduction);
            frame.data = value.toInt();
            sendFrame(frame);
        }
        else if(attribute == "Release")
        {
            frame.cmd = makeCMD(cmdE, Ec_ducking, Ea_ducking_release);
            frame.data = value.toInt();
            sendFrame(frame);
        }
        else if(attribute == "MapIN1")
        {
            frame.cmd = makeCMD(cmdE, Ec_ducking, Ea_ducking_map_in1);
            frame.data = value.toInt();
            sendFrame(frame);
        }
        else if(attribute == "MapIN2")
        {
            frame.cmd = makeCMD(cmdE, Ec_ducking, Ea_ducking_map_in2);
            frame.data = value.toInt();
            sendFrame(frame);
        }
        else if(attribute == "DuckingSwitch")
        {
            frame.cmd = makeCMD(cmdE, Ec_ducking, Ea_ducking_DuckingSwitch);
            frame.data = value.toInt();
            sendFrame(frame);
        }
    }
    else if(objectName == "SettingsDevice")
    {
        if(attribute == "Bluetooth")
        {
            frame.cmd = makeCMD(cmdS, Sc_running, Sa_BT);
            frame.data = value.toInt();
            sendFrame(frame);
        }
        else if(attribute == "Brightness")
        {
            frame.cmd = makeCMD(cmdS, Sc_running, Sa_brightness);
            value == "Dim" ? (frame.data = 0) : (0);
            value == "Normal" ? (frame.data = 1) : (0);
            value == "Bright" ? (frame.data = 2) : (0);
            sendFrame(frame);
        }
        else if(attribute == "OtgDirection")
        {
            frame.cmd = makeCMD(cmdS, Sc_running, Sa_OTGCharge);
            frame.data = value.toInt();
            sendFrame(frame);
        }
        else if(attribute == "UsbCCharging")
        {
            frame.cmd = makeCMD(cmdS, Sc_running, Sa_USBCCharge);
            value == "Charge" ? (frame.data = 1) : (0);
            value == "Discharge" ? (frame.data = 2) : (0);
            value == "Disabled" ? (frame.data = 3) : (0);
            sendFrame(frame);
        }
        else if(attribute == "FirmwareSelected")
        {
            frame.cmd = makeCMD(cmdS, Sc_running, Sa_Firmware);
            value == "Mobile mode" ? (frame.data = 1) : (0);
            value == "Live streaming mode" ? (frame.data = 2) : (0);
            value == "Pro audio mode" ? (frame.data = 3) : (0);
            sendFrame(frame);
        }
        else if(attribute == "AutoPoweroff")
        {
            frame.cmd = makeCMD(cmdS, Sc_running, Sa_Stdandby);
            frame.data = value.toInt();
            sendFrame(frame);
        }
        else if(attribute == "RestoreDefault")
        {
            frame.cmd = makeCMD(cmdS, Sc_running, Sa_Reset_Default);
            frame.data = value.toInt();
            sendFrame(frame);
        }
        else if(attribute == "MainButtonSingle")
        {
            frame.cmd = makeCMD(cmdS, Sc_running, Sa_MainButton_Single);
            frame.data = value.toInt();
            sendFrame(frame);
        }
        else if(attribute == "MainButtonDouble")
        {
            frame.cmd = makeCMD(cmdS, Sc_running, Sa_MainButton_Double);
            frame.data = value.toInt();
            sendFrame(frame);
        }
    }
    else if(objectName == "SettingsAbout")
    {
        if(attribute == "UpdateFirmware")
        {
            frame.cmd = makeCMD(cmdS, Sc_running, Sa_DFU_Start);
            frame.data = value.toInt();
            sendFrame(frame);
        }
    }
    else if(objectName == "Workspace")
    {
        if(attribute == "LoadSettings")
        {
            frame.cmd = makeCMD(cmdS, Sc_running, Sa_Connect_SE_Signal);
            frame.data = value.toInt();
            sendFrame(frame);
        }
    }
}
void DeviceM62::in_Equalizer_attributeChanged(QString objectName, QString attribute, QString value)
{
    DeviceType1::FrameInfo frame;
    frame.protocol = DeviceType1::write_nack;
    frame.current = 1;
    frame.total = 1;
    QStringList propertyList=objectName.split("@");
    if(propertyList.count() == 1)
    {
        if(propertyList.value(0) == "EQ_HP")
        {
            if(attribute == "Switch")
            {
                frame.cmd = makeCMD(cmdOEQ, Ec_parameter, Ea_Master_switch_1);
                frame.data = value.toInt();
                sendFrame(frame);
            }
            else if(attribute == "Gain")
            {
                frame.cmd = makeCMD(cmdO, Oc_ch_3, Oa_gain);
                frame.data = GainToVolume_HP(value.toFloat());
                sendFrame(frame);
                frame.cmd = makeCMD(cmdO, Oc_ch_4, Oa_gain);
                sendFrame(frame);
            }
        }
        else if(propertyList.value(0) == "EQ_IN1")
        {
            if(attribute == "Switch")
            {
                frame.cmd = makeCMD(cmdIEQ, Ec_parameter, Ea_Master_switch_1);
                frame.data = value.toInt();
                sendFrame(frame);
            }
            else if(attribute == "Gain")
            {
                frame.cmd = makeCMD(cmdIEQ, Ec_parameter, Ea_Output_gain_1);
                frame.data = USBAHandle.LogToGain(value.toDouble(), -90);
                sendFrame(frame);
            }
        }
        else if(propertyList.value(0) == "EQ_IN2")
        {
            if(attribute == "Switch")
            {
                frame.cmd = makeCMD(cmdIEQ, Ec_parameter, Ea_Master_switch_2);
                frame.data = value.toInt();
                sendFrame(frame);
            }
            else if(attribute == "Gain")
            {
                frame.cmd = makeCMD(cmdIEQ, Ec_parameter, Ea_Output_gain_2);
                frame.data = USBAHandle.LogToGain(value.toDouble(), -90);
                sendFrame(frame);
            }
        }
        return;
    }
    if(propertyList.value(0) == "EQ_HP")
    {
        frame.cmd = makeCMD(cmdOEQ, getEqualizerBandCode(propertyList.value(1)), getEqualizerAttributeCode("L" + attribute));
        if(attribute == "Type") frame.data = value.toInt() + 1;
        else if(attribute == "Gain") frame.data = value.toFloat() * 10;
        else if(attribute == "Q") frame.data = value.toFloat() * 10000;
        else frame.data = value.toInt();
        sendFrame(frame);
        frame.cmd = makeCMD(cmdOEQ, getEqualizerBandCode(propertyList.value(1)), getEqualizerAttributeCode("R" + attribute));
        sendFrame(frame);
    }
    else if(propertyList.value(0) == "EQ_IN1")
    {
        frame.cmd = makeCMD(cmdIEQ, getEqualizerBandCode(propertyList.value(1)), getEqualizerAttributeCode("L" + attribute));
        if(attribute == "Type") frame.data = value.toInt() + 1;
        else if(attribute == "Gain") frame.data = value.toFloat() * 10;
        else if(attribute == "Q") frame.data = value.toFloat() * 10000;
        else frame.data = value.toInt();
        sendFrame(frame);
    }
    else if(propertyList.value(0) == "EQ_IN2")
    {
        frame.cmd = makeCMD(cmdIEQ, getEqualizerBandCode(propertyList.value(1)), getEqualizerAttributeCode("R" + attribute));
        if(attribute == "Type") frame.data = value.toInt() + 1;
        else if(attribute == "Gain") frame.data = value.toFloat() * 10;
        else if(attribute == "Q") frame.data = value.toFloat() * 10000;
        else frame.data = value.toInt();
        sendFrame(frame);
    }
}


// do
void DeviceM62::doActionSaveFieldHead(QString& objectName, QString& attribute, QString& value)
{
    attribute.remove(0, QString("Save_").length());
    DeviceType1::FrameInfo frame;
    frame.protocol = DeviceType1::read_nack;
    frame.current = 1;
    frame.total = 1;
}
void DeviceM62::doActionSaveFieldInput(QString& objectName, QString& attribute, QString& value)
{
    attribute.remove(0, QString("Save_").length());
    DeviceType1::FrameInfo frame;
    frame.protocol = DeviceType1::read_nack;
    frame.current = 1;
    frame.total = 1;
    if(objectName == "Input_All")
    {
        if(attribute == "SoloState")
        {
            frame.cmd = makeCMD(cmdI, Ic_ch_all, Ia_solo);
            frame.data = value.toInt();
            sendFrame(frame);
        }
    }
    else if(objectName == "Input_IN1")
    {
        if(attribute == "Enable")
        {
            frame.cmd = makeCMD(cmdI, Ic_ch_1, Ia_Enable);
            frame.data = value.toInt();
            sendFrame(frame);
        }
        else if(attribute == "MIC")
        {
            frame.cmd = makeCMD(cmdI, Ic_ch_1, Ia_mic);
            value == "Mic1" ? (frame.data = 1) : (0);
            value == "Mic35" ? (frame.data = 2) : (0);
            value == "MicHP" ? (frame.data = 3) : (0);
            sendFrame(frame);
        }
        else if(attribute == "48V")
        {
            frame.cmd = makeCMD(cmdI, Ic_ch_1, Ia_48v);
            frame.data = value.toInt();
            sendFrame(frame);
        }
        else if(attribute == "GAIN")
        {
            frame.cmd = makeCMD(cmdI, Ic_ch_1, Ia_gain);
            frame.data = value.toInt();
            sendFrame(frame);
        }
        else if(attribute == "MUTE")
        {
            frame.cmd = makeCMD(cmdI, Ic_ch_1, Ia_mute);
            frame.data = value.toInt();
            sendFrame(frame);
        }
        else if(attribute == "SOLO")
        {
            frame.cmd = makeCMD(cmdI, Ic_ch_1, Ia_solo);
            frame.data = value.toInt();
            sendFrame(frame);
        }
    }
    else if(objectName == "Input_IN2")
    {
        if(attribute == "Enable")
        {
            frame.cmd = makeCMD(cmdI, Ic_ch_2, Ia_Enable);
            frame.data = value.toInt();
            sendFrame(frame);
        }
        else if(attribute == "48V")
        {
            frame.cmd = makeCMD(cmdI, Ic_ch_2, Ia_48v);
            frame.data = value.toInt();
            sendFrame(frame);
        }
        else if(attribute == "GAIN")
        {
            frame.cmd = makeCMD(cmdI, Ic_ch_2, Ia_gain);
            frame.data = value.toInt();
            sendFrame(frame);
        }
        else if(attribute == "MUTE")
        {
            frame.cmd = makeCMD(cmdI, Ic_ch_2, Ia_mute);
            frame.data = value.toInt();
            sendFrame(frame);
        }
        else if(attribute == "SOLO")
        {
            frame.cmd = makeCMD(cmdI, Ic_ch_2, Ia_solo);
            frame.data = value.toInt();
            sendFrame(frame);
        }
    }
    else if(objectName == "Input_AUX")
    {
        if(attribute == "Enable")
        {
            frame.cmd = makeCMD(cmdI, Ic_ch_3, Ia_Enable);
            frame.data = value.toInt();
            sendFrame(frame);
        }
        else if(attribute == "GAIN")
        {
            frame.cmd = makeCMD(cmdI, Ic_ch_3, Ia_gain);
            frame.data = GainToVolume_AUX(value.toFloat());
            sendFrame(frame);
        }
        else if(attribute == "MUTE")
        {
            frame.cmd = makeCMD(cmdI, Ic_ch_3, Ia_mute);
            frame.data = value.toInt();
            sendFrame(frame);
        }
        else if(attribute == "SOLO")
        {
            frame.cmd = makeCMD(cmdI, Ic_ch_3, Ia_solo);
            frame.data = value.toInt();
            sendFrame(frame);
        }
    }
    else if(objectName == "Input_BT")
    {
        if(attribute == "Enable")
        {
            frame.cmd = makeCMD(cmdI, Ic_ch_5, Ia_Enable);
            frame.data = value.toInt();
            sendFrame(frame);
        }
        else if(attribute == "GAIN")
        {
            frame.cmd = makeCMD(cmdI, Ic_ch_5, Ia_gain);
            frame.data = GainToVolume_BT(value.toFloat());
            sendFrame(frame);
        }
        else if(attribute == "MUTE")
        {
            frame.cmd = makeCMD(cmdI, Ic_ch_5, Ia_mute);
            frame.data = value.toInt();
            sendFrame(frame);
        }
        else if(attribute == "SOLO")
        {
            frame.cmd = makeCMD(cmdI, Ic_ch_5, Ia_solo);
            frame.data = value.toInt();
            sendFrame(frame);
        }
    }
    else if(objectName == "Input_OTGIN")
    {
        if(attribute == "Enable")
        {
            frame.cmd = makeCMD(cmdI, Ic_ch_7, Ia_Enable);
            frame.data = value.toInt();
            sendFrame(frame);
        }
        else if(attribute == "GAIN")
        {
            frame.cmd = makeCMD(cmdI, Ic_ch_7, Ia_gain);
            frame.data = GainToVolume_OTGIN(value.toFloat());
            sendFrame(frame);
        }
        else if(attribute == "MUTE")
        {
            frame.cmd = makeCMD(cmdI, Ic_ch_7, Ia_mute);
            frame.data = value.toInt();
            sendFrame(frame);
        }
        else if(attribute == "SOLO")
        {
            frame.cmd = makeCMD(cmdI, Ic_ch_7, Ia_solo);
            frame.data = value.toInt();
            sendFrame(frame);
        }
    }
}
void DeviceM62::doActionSaveFieldMixer(QString& objectName, QString& attribute, QString& value)
{
    attribute.remove(0, QString("Save_").length());
    DeviceType1::FrameInfo frame;
    frame.protocol = DeviceType1::read_nack;
    frame.current = 1;
    frame.total = 1;
    if(objectName == "Mixer_All")
    {
        if(attribute == "Mixer")
        {
            frame.cmd = makeCMD(cmdM, Mc_mixer, Ma_ch_all);
            value == "MixA" ? (frame.data = 1) : (0);
            value == "MixB" ? (frame.data = 2) : (0);
            value == "MixC" ? (frame.data = 3) : (0);
            value == "MixD" ? (frame.data = 4) : (0);
            sendFrame(frame);
        }
        else if(attribute == "MixA_SoloState")
        {
            frame.cmd = makeCMD(cmdM, Mc_mixer_al, Ma_SoloState);
            frame.data = value.toInt();
            sendFrame(frame);
        }
        else if(attribute == "MixB_SoloState")
        {
            frame.cmd = makeCMD(cmdM, Mc_mixer_bl, Ma_SoloState);
            frame.data = value.toInt();
            sendFrame(frame);
        }
        else if(attribute == "MixC_SoloState")
        {
            frame.cmd = makeCMD(cmdM, Mc_mixer_cl, Ma_SoloState);
            frame.data = value.toInt();
            sendFrame(frame);
        }
        else if(attribute == "MixD_SoloState")
        {
            frame.cmd = makeCMD(cmdM, Mc_mixer_dl, Ma_SoloState);
            frame.data = value.toInt();
            sendFrame(frame);
        }
    }
    else if(objectName == "Mixer_IN12")
    {
        frame.cmd = makeCMD(cmdP, Pc_ch_1, getMixerChannelAttributeCode(attribute));
        frame.data = value.toInt();
        sendFrame(frame);
    }
    else if(objectName == "Mixer_AUX")
    {
        frame.cmd = makeCMD(cmdP, Pc_ch_2, getMixerChannelAttributeCode(attribute));
        frame.data = value.toInt();
        sendFrame(frame);
    }
    else if(objectName == "Mixer_BT")
    {
        frame.cmd = makeCMD(cmdP, Pc_ch_3, getMixerChannelAttributeCode(attribute));
        frame.data = value.toInt();
        sendFrame(frame);
    }
    else if(objectName == "Mixer_OTGIN")
    {
        frame.cmd = makeCMD(cmdP, Pc_ch_4, getMixerChannelAttributeCode(attribute));
        frame.data = value.toInt();
        sendFrame(frame);
    }
    else if(objectName == "Mixer_FX")
    {
        frame.cmd = makeCMD(cmdP, Pc_ch_5, getMixerChannelAttributeCode(attribute));
        frame.data = value.toInt();
        sendFrame(frame);
    }
    else if(objectName == "Mixer_Reverb")
    {
        frame.cmd = makeCMD(cmdP, Pc_ch_6, getMixerChannelAttributeCode(attribute));
        frame.data = value.toInt();
        sendFrame(frame);
    }
    else if(objectName == "Mixer_PB12")
    {
        frame.cmd = makeCMD(cmdP, Pc_ch_7, getMixerChannelAttributeCode(attribute));
        frame.data = value.toInt();
        sendFrame(frame);
    }
    else if(objectName == "Mixer_PB34")
    {
        frame.cmd = makeCMD(cmdP, Pc_ch_8, getMixerChannelAttributeCode(attribute));
        frame.data = value.toInt();
        sendFrame(frame);
    }
    else if(objectName == "Mixer_PB56")
    {
        frame.cmd = makeCMD(cmdP, Pc_ch_9, getMixerChannelAttributeCode(attribute));
        frame.data = value.toInt();
        sendFrame(frame);
    }
    else if(objectName == "Mixer_PB78")
    {
        frame.cmd = makeCMD(cmdP, Pc_ch_10, getMixerChannelAttributeCode(attribute));
        frame.data = value.toInt();
        sendFrame(frame);
    }
}
void DeviceM62::doActionSaveFieldEffect(QString& objectName, QString& attribute, QString& value)
{
    attribute.remove(0, QString("Save_").length());
    DeviceType1::FrameInfo frame;
    frame.protocol = DeviceType1::read_nack;
    frame.current = 1;
    frame.total = 1;
    if(objectName == "Effect_NC")
    {
        if(attribute == "Enable")
        {
            frame.cmd = makeCMD(cmdE, Ec_nc, Ea_nc_Enable);
            frame.data = value.toInt();
            sendFrame(frame);
        }
        else if(attribute == "NCChannel")
        {
            frame.cmd = makeCMD(cmdE, Ec_nc, Ea_nc_channel);
            value == "IN1" ? (frame.data = 0) : (0);
            value == "IN2" ? (frame.data = 1) : (0);
            value == "IN12" ? (frame.data = 2) : (0);
            sendFrame(frame);
        }
        else if(attribute == "NCType")
        {
            frame.cmd = makeCMD(cmdE, Ec_nc, Ea_nc_level);
            value == "OFF" ? (frame.data = 0) : (0);
            value == "NC1" ? (frame.data = 15) : (0);
            value == "NC2" ? (frame.data = 30) : (0);
            value == "NC3" ? (frame.data = 45) : (0);
            sendFrame(frame);
        }
        else if(attribute == "GAIN")
        {
            frame.cmd = makeCMD(cmdE, Ec_nc, Ea_nc_gain);
            frame.data = value.toInt();
            sendFrame(frame);
        }
        else if(attribute == "MUTE")
        {
            frame.cmd = makeCMD(cmdE, Ec_nc, Ea_nc_mute);
            frame.data = value.toInt();
            sendFrame(frame);
        }
    }
    else if(objectName == "Effect_Reverb")
    {
        if(attribute == "Enable")
        {
            frame.cmd = makeCMD(cmdE, Ec_reverb, Ea_reverb_Enable);
            frame.data = value.toInt();
            sendFrame(frame);
        }
        else if(attribute == "ReverbChannel")
        {
            frame.cmd = makeCMD(cmdE, Ec_reverb, Ea_reverb_channel);
            value == "NC" ? (frame.data = 0) : (0);
            value == "IN1" ? (frame.data = 1) : (0);
            value == "IN2" ? (frame.data = 2) : (0);
            value == "IN12" ? (frame.data = 3) : (0);
            sendFrame(frame);
        }
        else if(attribute == "ReverbType")
        {
            frame.cmd = makeCMD(cmdE, Ec_reverb, Ea_reverb_type);
            value == "OFF" ? (frame.data = 1) : (0);
            value == "STU" ? (frame.data = 2) : (0);
            value == "LIVE" ? (frame.data = 3) : (0);
            value == "HALL" ? (frame.data = 4) : (0);
            sendFrame(frame);
        }
        else if(attribute == "DryWet")
        {
            frame.cmd = makeCMD(cmdE, Ec_reverb, Ea_reverb_drywet);
            frame.data = value.toInt();
            sendFrame(frame);
        }
        else if(attribute == "MUTE")
        {
            frame.cmd = makeCMD(cmdE, Ec_reverb, Ea_reverb_mute);
            frame.data = value.toInt();
            sendFrame(frame);
        }
        else if(attribute == "Room")
        {
            frame.cmd = makeCMD(cmdE, Ec_reverb, Ea_reverb_room);
            frame.data = value.toInt();
            sendFrame(frame);
        }
        else if(attribute == "Decay")
        {
            frame.cmd = makeCMD(cmdE, Ec_reverb, Ea_reverb_decay);
            frame.data = value.toInt();
            sendFrame(frame);
        }
        else if(attribute == "NavigationDisplayMode")
        {
            frame.cmd = makeCMD(cmdE, Ec_reverb, Ea_reverb_DisplayMode);
            frame.data = value.toInt();
            sendFrame(frame);
        }
    }
}
void DeviceM62::doActionSaveFieldLoopback(QString& objectName, QString& attribute, QString& value)
{
    attribute.remove(0, QString("Save_").length());
    DeviceType1::FrameInfo frame;
    frame.protocol = DeviceType1::read_nack;
    frame.current = 1;
    frame.total = 1;
    if(objectName == "Loopback_LB12")
    {
        if(attribute == "Enable")
        {
            frame.cmd = makeCMD(cmdL, Lc_ch_1, La_Enable);
            frame.data = value.toInt();
            sendFrame(frame);
        }
        else if(attribute == "AudioSource")
        {
            frame.cmd = makeCMD(cmdL, Lc_ch_1, La_audio_source);
            frame.data = getAudioSourceCode(value);
            sendFrame(frame);
        }
        else if(attribute == "GAIN")
        {
            frame.cmd = makeCMD(cmdL, Lc_ch_1, La_gain);
            frame.data = value.toInt();
            sendFrame(frame);
        }
        else if(attribute == "MUTE")
        {
            frame.cmd = makeCMD(cmdL, Lc_ch_1, La_mute);
            frame.data = value.toInt();
            sendFrame(frame);
        }
    }
    else if(objectName == "Loopback_LB34")
    {
        if(attribute == "Enable")
        {
            frame.cmd = makeCMD(cmdL, Lc_ch_3, La_Enable);
            frame.data = value.toInt();
            sendFrame(frame);
        }
        else if(attribute == "AudioSource")
        {
            frame.cmd = makeCMD(cmdL, Lc_ch_3, La_audio_source);
            frame.data = getAudioSourceCode(value);
            sendFrame(frame);
        }
        else if(attribute == "GAIN")
        {
            frame.cmd = makeCMD(cmdL, Lc_ch_3, La_gain);
            frame.data = value.toInt();
            sendFrame(frame);
        }
        else if(attribute == "MUTE")
        {
            frame.cmd = makeCMD(cmdL, Lc_ch_3, La_mute);
            frame.data = value.toInt();
            sendFrame(frame);
        }
    }
    else if(objectName == "Loopback_LB56")
    {
        if(attribute == "Enable")
        {
            frame.cmd = makeCMD(cmdL, Lc_ch_5, La_Enable);
            frame.data = value.toInt();
            sendFrame(frame);
        }
        else if(attribute == "AudioSource")
        {
            frame.cmd = makeCMD(cmdL, Lc_ch_5, La_audio_source);
            frame.data = getAudioSourceCode(value);
            sendFrame(frame);
        }
        else if(attribute == "GAIN")
        {
            frame.cmd = makeCMD(cmdL, Lc_ch_5, La_gain);
            frame.data = value.toInt();
            sendFrame(frame);
        }
        else if(attribute == "MUTE")
        {
            frame.cmd = makeCMD(cmdL, Lc_ch_5, La_mute);
            frame.data = value.toInt();
            sendFrame(frame);
        }
    }
}
void DeviceM62::doActionSaveFieldOutput(QString& objectName, QString& attribute, QString& value)
{
    attribute.remove(0, QString("Save_").length());
    DeviceType1::FrameInfo frame;
    frame.protocol = DeviceType1::read_nack;
    frame.current = 1;
    frame.total = 1;
    if(objectName == "Output_HP")
    {
        if(attribute == "Enable")
        {
            frame.cmd = makeCMD(cmdO, Oc_ch_4, Oa_Enable);
            frame.data = value.toInt();
            sendFrame(frame);
        }
        else if(attribute == "AudioSource")
        {
            frame.cmd = makeCMD(cmdO, Oc_ch_4, Oa_audio_source);
            frame.data = getAudioSourceCode(value);
            sendFrame(frame);
        }
        else if(attribute == "GAIN")
        {
            frame.cmd = makeCMD(cmdO, Oc_ch_4, Oa_gain);
            frame.data = GainToVolume_HP(value.toFloat());
            sendFrame(frame);
        }
        else if(attribute == "MUTE")
        {
            frame.cmd = makeCMD(cmdO, Oc_ch_4, Oa_mute);
            frame.data = value.toInt();
            sendFrame(frame);
        }
    }
    else if(objectName == "Output_OTGOUT")
    {
        if(attribute == "Enable")
        {
            frame.cmd = makeCMD(cmdO, Oc_ch_2, Oa_Enable);
            frame.data = value.toInt();
            sendFrame(frame);
        }
        else if(attribute == "AudioSource")
        {
            frame.cmd = makeCMD(cmdO, Oc_ch_2, Oa_audio_source);
            frame.data = getAudioSourceCode(value);
            sendFrame(frame);
        }
        else if(attribute == "GAIN")
        {
            frame.cmd = makeCMD(cmdO, Oc_ch_2, Oa_gain);
            frame.data = GainToVolume_OTGOUT(value.toFloat());
            sendFrame(frame);
        }
        else if(attribute == "MUTE")
        {
            frame.cmd = makeCMD(cmdO, Oc_ch_2, Oa_mute);
            frame.data = value.toInt();
            sendFrame(frame);
        }
    }
}
void DeviceM62::doActionSaveOthers(QString& objectName, QString& attribute, QString& value)
{
    attribute.remove(0, QString("Save_").length());
    DeviceType1::FrameInfo frame;
    frame.protocol = DeviceType1::read_nack;
    frame.current = 1;
    frame.total = 1;
    if(objectName == "ConfigMenuLive")
    {
        if(attribute == "Enable")
        {
            frame.cmd = makeCMD(cmdE, Ec_ducking, Ea_ducking_Enable);
            frame.data = value.toInt();
            sendFrame(frame);
        }
        else if(attribute == "Threshold")
        {
            frame.cmd = makeCMD(cmdE, Ec_ducking, Ea_ducking_threshold);
            frame.data = value.toInt();
            sendFrame(frame);
        }
        else if(attribute == "Attack")
        {
            frame.cmd = makeCMD(cmdE, Ec_ducking, Ea_ducking_attack);
            frame.data = value.toInt();
            sendFrame(frame);
        }
        else if(attribute == "Reduction")
        {
            frame.cmd = makeCMD(cmdE, Ec_ducking, Ea_ducking_reduction);
            frame.data = value.toInt();
            sendFrame(frame);
        }
        else if(attribute == "Release")
        {
            frame.cmd = makeCMD(cmdE, Ec_ducking, Ea_ducking_release);
            frame.data = value.toInt();
            sendFrame(frame);
        }
        else if(attribute == "MapIN1")
        {
            frame.cmd = makeCMD(cmdE, Ec_ducking, Ea_ducking_map_in1);
            frame.data = value.toInt();
            sendFrame(frame);
        }
        else if(attribute == "MapIN2")
        {
            frame.cmd = makeCMD(cmdE, Ec_ducking, Ea_ducking_map_in2);
            frame.data = value.toInt();
            sendFrame(frame);
        }
        else if(attribute == "DuckingSwitch")
        {
            frame.cmd = makeCMD(cmdE, Ec_ducking, Ea_ducking_DuckingSwitch);
            frame.data = value.toInt();
            sendFrame(frame);
        }
    }
    else if(objectName == "ConfigMenuTyro")
    {
        if(attribute == "Enable")
        {
            frame.cmd = makeCMD(cmdE, Ec_ducking, Ea_ducking_Enable);
            frame.data = value.toInt();
            sendFrame(frame);
        }
        else if(attribute == "DuckingSwitch")
        {
            frame.cmd = makeCMD(cmdE, Ec_ducking, Ea_ducking_DuckingSwitch);
            frame.data = value.toInt();
            sendFrame(frame);
        }
    }
}

