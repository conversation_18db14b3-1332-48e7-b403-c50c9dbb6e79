/************************************************************************
 *
 *  Module:       WnTrace.h
 *
 *  Description:  Win32 trace support
 *
 *  Runtime Env.: Win32
 *
 *  Author(s):
 *    <PERSON><PERSON>,  Udo.E<PERSON><EMAIL>
 *
 *  Companies:
 *    Thesycon GmbH, Germany      http://www.thesycon.de
 *
 ************************************************************************/

#include "libwn_min_global.h"

// Module is empty if .h file was not included (category turned off).
#ifdef __WnTrace_h__

#include "WnStringUtils_impl.h"

// optionally put everything into a namespace
#ifdef LIBWN_NAMESPACE
namespace LIBWN_NAMESPACE {
#endif

// The default trace instance
WnTraceLogContext gDefaultWnTraceInstance;

// The global trace context used by the WNTRACE macro
WnTraceLogContext* gWnTrace = &gDefaultWnTraceInstance;



void
WnSetTraceMask(
    unsigned int newMask
    )
{
#if WNTRACE_ENABLE
    gWnTrace->SetMask(newMask);
#else
    TB_UNUSED_PARAM(newMask)
#endif
}

unsigned int
WnGetTraceMask()
{
#if WNTRACE_ENABLE
    return gWnTrace->GetMask();
#else
    return 0;
#endif
}



//
// global trace print functions, mapped to global trace context
//

template<int bitnb> // bitnb 0..31, or -1
void
tprint_impl(const char* fmt, ...)
{
    va_list args;
    va_start(args,fmt);
    gWnTrace->VPrintfEx(bitnb, fmt, args);
    va_end(args);
}

template<int bitnb> // bitnb 0..31, or -1
void
tprint_impl(const WCHAR* fmt, ...)
{
    va_list args;
    va_start(args,fmt);
    gWnTrace->VPrintfEx(bitnb, fmt, args);
    va_end(args);
}


// explicit instantiation for every bit nb
template void tprint_impl<-1>(const char* fmt, ...);
template void tprint_impl<0>(const char* fmt, ...);
template void tprint_impl<1>(const char* fmt, ...);
template void tprint_impl<2>(const char* fmt, ...);
template void tprint_impl<3>(const char* fmt, ...);
template void tprint_impl<4>(const char* fmt, ...);
template void tprint_impl<5>(const char* fmt, ...);
template void tprint_impl<6>(const char* fmt, ...);
template void tprint_impl<7>(const char* fmt, ...);
template void tprint_impl<8>(const char* fmt, ...);
template void tprint_impl<9>(const char* fmt, ...);
template void tprint_impl<10>(const char* fmt, ...);
template void tprint_impl<11>(const char* fmt, ...);
template void tprint_impl<12>(const char* fmt, ...);
template void tprint_impl<13>(const char* fmt, ...);
template void tprint_impl<14>(const char* fmt, ...);
template void tprint_impl<15>(const char* fmt, ...);
template void tprint_impl<16>(const char* fmt, ...);
template void tprint_impl<17>(const char* fmt, ...);
template void tprint_impl<18>(const char* fmt, ...);
template void tprint_impl<19>(const char* fmt, ...);
template void tprint_impl<20>(const char* fmt, ...);
template void tprint_impl<21>(const char* fmt, ...);
template void tprint_impl<22>(const char* fmt, ...);
template void tprint_impl<23>(const char* fmt, ...);
template void tprint_impl<24>(const char* fmt, ...);
template void tprint_impl<25>(const char* fmt, ...);
template void tprint_impl<26>(const char* fmt, ...);
template void tprint_impl<27>(const char* fmt, ...);
template void tprint_impl<28>(const char* fmt, ...);
template void tprint_impl<29>(const char* fmt, ...);
template void tprint_impl<30>(const char* fmt, ...);
template void tprint_impl<31>(const char* fmt, ...);

// explicit instantiation for every bit nb
template void tprint_impl<-1>(const WCHAR* fmt, ...);
template void tprint_impl<0>(const WCHAR* fmt, ...);
template void tprint_impl<1>(const WCHAR* fmt, ...);
template void tprint_impl<2>(const WCHAR* fmt, ...);
template void tprint_impl<3>(const WCHAR* fmt, ...);
template void tprint_impl<4>(const WCHAR* fmt, ...);
template void tprint_impl<5>(const WCHAR* fmt, ...);
template void tprint_impl<6>(const WCHAR* fmt, ...);
template void tprint_impl<7>(const WCHAR* fmt, ...);
template void tprint_impl<8>(const WCHAR* fmt, ...);
template void tprint_impl<9>(const WCHAR* fmt, ...);
template void tprint_impl<10>(const WCHAR* fmt, ...);
template void tprint_impl<11>(const WCHAR* fmt, ...);
template void tprint_impl<12>(const WCHAR* fmt, ...);
template void tprint_impl<13>(const WCHAR* fmt, ...);
template void tprint_impl<14>(const WCHAR* fmt, ...);
template void tprint_impl<15>(const WCHAR* fmt, ...);
template void tprint_impl<16>(const WCHAR* fmt, ...);
template void tprint_impl<17>(const WCHAR* fmt, ...);
template void tprint_impl<18>(const WCHAR* fmt, ...);
template void tprint_impl<19>(const WCHAR* fmt, ...);
template void tprint_impl<20>(const WCHAR* fmt, ...);
template void tprint_impl<21>(const WCHAR* fmt, ...);
template void tprint_impl<22>(const WCHAR* fmt, ...);
template void tprint_impl<23>(const WCHAR* fmt, ...);
template void tprint_impl<24>(const WCHAR* fmt, ...);
template void tprint_impl<25>(const WCHAR* fmt, ...);
template void tprint_impl<26>(const WCHAR* fmt, ...);
template void tprint_impl<27>(const WCHAR* fmt, ...);
template void tprint_impl<28>(const WCHAR* fmt, ...);
template void tprint_impl<29>(const WCHAR* fmt, ...);
template void tprint_impl<30>(const WCHAR* fmt, ...);
template void tprint_impl<31>(const WCHAR* fmt, ...);




void
WnInitTraceModule(
    const WnTraceInfoEntry* traceInfo,
    HMODULE hModule,
    WnTraceLogContext* traceLogContext,
    const GUID& etwTraceProviderGuid
    )
#if WNTRACE_ENABLE
{
    WNERR err;

    // use the user-provided trace instance, if any
    if ( traceLogContext != NULL ) {
        gWnTrace = traceLogContext;
    }
    if ( etwTraceProviderGuid != GUID_NULL ) {
        gDefaultWnTraceInstance.RegisterEtwTracing(etwTraceProviderGuid);
    }

    // init bit descriptions
    gWnTrace->SetTraceBitInfo(traceInfo);


    WnTraceLogSettings settings(hModule);

    //
    // NOTE: Because of UAC on Windows 7 (and Vista) we cannot write to HKEY_LOCAL_MACHINE
    // if we are running as a normal user process.
    // If we are running as a service (without an interactive user context), we can write
    // to HKEY_LOCAL_MACHINE.
    //
    // We give existing settings under HKEY_CURRENT_USER higher priority than settings under HKEY_LOCAL_MACHINE.
    //

    // try to read trace settings from HKEY_CURRENT_USER
    err = settings.LoadSettingsFromRegistry(*gWnTrace, HKEY_CURRENT_USER);
    if ( SUCC(err) ) {
        // done
    } else {
        // try to read trace settings from HKEY_LOCAL_MACHINE
        err = settings.LoadSettingsFromRegistry(*gWnTrace, HKEY_LOCAL_MACHINE);
        if ( SUCC(err) ) {
            // done
        } else {

          // no trace settings found in registry

          // try to write to sub key under HKEY_LOCAL_MACHINE
          err = settings.SaveSettingsToRegistry(*gWnTrace, HKEY_LOCAL_MACHINE);
          if ( SUCC(err) ) {
              // we do have sufficient privileges to write to HKEY_LOCAL_MACHINE
              // done
          } else {
              // try to write to sub key under HKEY_CURRENT_USER, ignore error
              settings.SaveSettingsToRegistry(*gWnTrace, HKEY_CURRENT_USER);
          }
      }
  }

}
#else
{
    TB_UNUSED_PARAM(traceInfo)
    TB_UNUSED_PARAM(hModule)
    TB_UNUSED_PARAM(traceLogContext)
    TB_UNUSED_PARAM(etwTraceProviderGuid)
    // empty
}
#endif




#if TB_DEBUG
void
WnDbgOutput(
    const char* format,
    ...
    )
{
    va_list argptr;
    va_start(argptr, format);

    char buf[512];
    WnStringVPrintf_impl(buf, TB_ARRAY_ELEMENTS(buf), format, argptr);

#ifdef UNDER_CE
    WCHAR wbuf[TB_ARRAY_ELEMENTS(buf)];
    TbStringNCopyToArray(wbuf,buf);
    ::OutputDebugStringW(wbuf);
#else
    ::OutputDebugStringA(buf);
#endif

    va_end(argptr);
}
#endif


#ifdef LIBWN_NAMESPACE
}
#endif

#endif //__WnTrace_h__

/********************************* EOF *********************************/
