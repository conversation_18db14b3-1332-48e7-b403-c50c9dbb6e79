#ifndef PUSHBUTTONGROUPS1M11_H
#define PUSHBUTTONGROUPS1M11_H


#include <QHash>
#include <QTimer>
#include <QWidget>
#include <QPushButton>
#include <QResizeEvent>


namespace Ui {
class PushButtonGroupS1M11;
}


class PushButtonGroupS1M11 : public QWidget
{
    Q_OBJECT
public:
    explicit PushButtonGroupS1M11(QWidget* parent=nullptr);
    ~PushButtonGroupS1M11();
    PushButtonGroupS1M11& setFont(QFont font);
    PushButtonGroupS1M11& setLanguage(QString language);
    PushButtonGroupS1M11& setState(QString button, QString state, bool needEmit=true);
    QString getState(QString button);
protected:
    bool eventFilter(QObject* obj, QEvent* e) override;
    void resizeEvent(QResizeEvent* e) override;
    void showEvent(QShowEvent*) override { resizeEvent(nullptr); }
private:
    Ui::PushButtonGroupS1M11* ui;
    QFont mFont;
    QTimer mTimer;
    QPushButton* mButton;
    Qt::MouseButton mMouseButton;
    unsigned int mBitmap=0;
    QHash<unsigned int, QString> mStyle;
private slots:
    void on_PushButton48V_clicked(bool checked);
    void on_PushButtonAUTO_clicked(bool checked);
    void on_PushButtonEQ_clicked(bool checked);
    void on_PushButtonDUCKING_clicked(bool checked);
signals:
    void mouseClickEvent(QString button, QString event);
    void stateChanged(QString button, QString state);
};


#endif // PUSHBUTTONGROUPS1M11_H

