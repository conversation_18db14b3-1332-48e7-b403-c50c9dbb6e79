/************************************************************************
 *  Long name:    TUSBAUDIO_GetExtendedInfo wrapper
 *  Description:  Implements specific TUSBAUDIO_GetExtendedInfo API calls.
 *  Author(s):    <PERSON>
 *  Company:      Thesycon GmbH, Ilmenau
 ************************************************************************/

#ifdef TUSBAUDIOAPI_USES_LIBTL

// include our OS library which pulls in windows.h
#include "libtlwin.h"

// open our namespaces
using namespace thesycon;


#else

// Exclude rarely-used stuff from Windows headers
#ifndef VC_EXTRALEAN
#define VC_EXTRALEAN
#endif
// libwn_min, pulls in windows.h
#include "libwn.h"

#endif


#include "TUsbAudioApiExtendedInfo.h"




TUsbAudioApiExtendedInfo::TUsbAudioApiExtendedInfo(TUsbAudioApiDll& apiDll)
    : mApiDll {apiDll}
{
}


TUsbAudioStatus
TUsbAudioApiExtendedInfo::GetWdmSoundDeviceCount(
    TUsbAudioHandle deviceHandle,
    unsigned int& inputWdmSoundDeviceCount,
    unsigned int& outputWdmSoundDeviceCount
    )
{
    inputWdmSoundDeviceCount = 0;
    outputWdmSoundDeviceCount = 0;

    TUsbAudioWdmSoundDeviceCount buf;
    memset(&buf, 0, sizeof(buf));

    unsigned int byteCount = 0;

    TUsbAudioStatus st = mApiDll.TUSBAUDIO_GetExtendedInfo(
                                    deviceHandle,
                                    ExtendedInfoId_GetWdmSoundDeviceCount,
                                    nullptr,
                                    0,
                                    &buf,
                                    sizeof(buf),
                                    &byteCount
                                    );
    if (SUCC(st)) {
        inputWdmSoundDeviceCount = buf.inputWdmSoundDeviceCount;
        outputWdmSoundDeviceCount = buf.outputWdmSoundDeviceCount;
    }

    return st;
}

TUsbAudioStatus
TUsbAudioApiExtendedInfo::GetWdmSoundDevices(
    TUsbAudioHandle deviceHandle,
    bool input,
    TUsbAudioWdmSoundDevice* wdmSoundDeviceInfo,
    unsigned int maxNumWdmSoundDeviceInfo,
    unsigned int& numWdmSoundDeviceInfo
    )
{
    numWdmSoundDeviceInfo = 0;

    unsigned int byteCount = 0;
    TUsbAudioStatus st = mApiDll.TUSBAUDIO_GetExtendedInfo(
                                    deviceHandle,
                                    input ? ExtendedInfoId_GetWdmInputSoundDevices : ExtendedInfoId_GetWdmOutputSoundDevices,
                                    nullptr,
                                    0,
                                    wdmSoundDeviceInfo,
                                    maxNumWdmSoundDeviceInfo * sizeof(TUsbAudioWdmSoundDevice),
                                    &byteCount
                                    );
    if (SUCC(st)) {
        numWdmSoundDeviceInfo = byteCount / sizeof(TUsbAudioWdmSoundDevice);
    }

    return st;
}

TUsbAudioStatus
TUsbAudioApiExtendedInfo::GetWdmChannelInfo(
    TUsbAudioHandle deviceHandle,
    unsigned __int64 wdmSoundDeviceId,
    TUsbAudioWdmChannelInfo* wdmChannelInfo,
    unsigned int maxNumWdmChannelInfo,
    unsigned int& numWdmChannelInfo
    )
{
    numWdmChannelInfo = 0;

    TUsbAudioObjectId id = wdmSoundDeviceId;

    unsigned int byteCount = 0;
    TUsbAudioStatus st = mApiDll.TUSBAUDIO_GetExtendedInfo(
                                    deviceHandle,
                                    ExtendedInfoId_GetWdmChannelInfo,
                                    &id,
                                    sizeof(id),
                                    wdmChannelInfo,
                                    maxNumWdmChannelInfo * sizeof(TUsbAudioWdmChannelInfo),
                                    &byteCount
                                    );
    if (SUCC(st)) {
        numWdmChannelInfo = byteCount / sizeof(TUsbAudioWdmChannelInfo);
    }

    return st;
}

TUsbAudioStatus
TUsbAudioApiExtendedInfo::GetHardwareChannelCount(
    TUsbAudioHandle deviceHandle,
    unsigned int& inputHardwareChannelCount,
    unsigned int& outputHardwareChannelCount
    )
{
    inputHardwareChannelCount = 0;
    outputHardwareChannelCount = 0;

    TUsbAudioHardwareChannelCount buf;
    memset(&buf, 0, sizeof(buf));

    unsigned int byteCount = 0;

    TUsbAudioStatus st = mApiDll.TUSBAUDIO_GetExtendedInfo(
                                    deviceHandle,
                                    ExtendedInfoId_GetHardwareChannelCount,
                                    nullptr,
                                    0,
                                    &buf,
                                    sizeof(buf),
                                    &byteCount
                                    );
    if (SUCC(st)) {
        inputHardwareChannelCount = buf.inputHardwareChannelCount;
        outputHardwareChannelCount = buf.outputHardwareChannelCount;
    }

    return st;
}

TUsbAudioStatus
TUsbAudioApiExtendedInfo::GetVirtualChannelCount(
    TUsbAudioHandle deviceHandle,
    unsigned int& inputVirtualChannelCount,
    unsigned int& outputVirtualChannelCount
    )
{
    inputVirtualChannelCount = 0;
    outputVirtualChannelCount = 0;

    TUsbAudioVirtualChannelCount buf;
    memset(&buf, 0, sizeof(buf));

    unsigned int byteCount = 0;

    TUsbAudioStatus st = mApiDll.TUSBAUDIO_GetExtendedInfo(
                                    deviceHandle,
                                    ExtendedInfoId_GetVirtualChannelCount,
                                    nullptr,
                                    0,
                                    &buf,
                                    sizeof(buf),
                                    &byteCount
                                    );
    if (SUCC(st)) {
        inputVirtualChannelCount = buf.inputVirtualChannelCount;
        outputVirtualChannelCount = buf.outputVirtualChannelCount;
    }

    return st;
}

TUsbAudioStatus
TUsbAudioApiExtendedInfo::GetHardwareChannelInfo(
    TUsbAudioHandle deviceHandle,
    bool input,
    TUsbAudioHardwareChannelInfo* hardwareChannelInfo,
    unsigned int maxNumHardwareChannelInfo,
    unsigned int& numHardwareChannelInfo
    )
{
    numHardwareChannelInfo = 0;

    unsigned int byteCount = 0;
    TUsbAudioStatus st = mApiDll.TUSBAUDIO_GetExtendedInfo(
                                    deviceHandle,
                                    input ? ExtendedInfoId_GetHardwareInputChannelInfo : ExtendedInfoId_GetHardwareOutputChannelInfo,
                                    nullptr,
                                    0,
                                    hardwareChannelInfo,
                                    maxNumHardwareChannelInfo * sizeof(TUsbAudioHardwareChannelInfo),
                                    &byteCount
                                    );
    if (SUCC(st)) {
        numHardwareChannelInfo = byteCount / sizeof(TUsbAudioHardwareChannelInfo);
    }

    return st;
}

TUsbAudioStatus
TUsbAudioApiExtendedInfo::GetHardwareChannelName(
    TUsbAudioHandle deviceHandle,
    unsigned __int64 channelId,
    TUsbAudioHardwareChannelName& hardwareChannelInfo
    )
{
    TUsbAudioObjectId id = channelId;

    unsigned int byteCount = 0;
    TUsbAudioStatus st = mApiDll.TUSBAUDIO_GetExtendedInfo(
                                    deviceHandle,
                                    ExtendedInfoId_GetHardwareChannelName,
                                    &id,
                                    sizeof(id),
                                    &hardwareChannelInfo,
                                    sizeof(TUsbAudioHardwareChannelName),
                                    &byteCount
                                    );

    return st;
}

TUsbAudioStatus
TUsbAudioApiExtendedInfo::GetValidSoundDeviceProfiles(
    TUsbAudioHandle deviceHandle,
    unsigned int streamFormatId,
    TUsbAudioSoundDeviceProfileInfo* soundDeviceProfileInfo,
    unsigned int maxNumSoundDeviceProfileInfo,
    unsigned int& numSoundDeviceProfileInfo
    )
{
    numSoundDeviceProfileInfo = 0;

    unsigned int byteCount = 0;
    TUsbAudioStatus st = mApiDll.TUSBAUDIO_GetExtendedInfo(
                                    deviceHandle,
                                    ExtendedInfoId_GetValidSoundDeviceProfiles,
                                    &streamFormatId,
                                    sizeof(streamFormatId),
                                    soundDeviceProfileInfo,
                                    maxNumSoundDeviceProfileInfo * sizeof(TUsbAudioSoundDeviceProfileInfo),
                                    &byteCount
                                    );
    if (SUCC(st)) {
        numSoundDeviceProfileInfo = byteCount / sizeof(TUsbAudioSoundDeviceProfileInfo);
    }

    return st;
}

TUsbAudioStatus
TUsbAudioApiExtendedInfo::GetCurrentSoundDeviceProfile(
    TUsbAudioHandle deviceHandle,
    unsigned int streamFormatId,
    TUsbAudioSoundDeviceProfileInfo& soundDeviceProfileInfo
    )
{
    unsigned int byteCount = 0;
    TUsbAudioStatus st = mApiDll.TUSBAUDIO_GetExtendedInfo(
                                    deviceHandle,
                                    ExtendedInfoId_GetCurrentSoundDeviceProfile,
                                    &streamFormatId,
                                    sizeof(streamFormatId),
                                    &soundDeviceProfileInfo,
                                    sizeof(TUsbAudioSoundDeviceProfileInfo),
                                    &byteCount
                                    );

    return st;
}

TUsbAudioStatus
TUsbAudioApiExtendedInfo::GetHardwareChannelHiddenFlags(
    TUsbAudioHandle deviceHandle,
    unsigned int streamFormatId,
    TUsbAudioHardwareChannelHiddenFlags& hiddenFlags
    )
{
    unsigned int byteCount = 0;
    TUsbAudioStatus st = mApiDll.TUSBAUDIO_GetExtendedInfo(
                                    deviceHandle,
                                    ExtendedInfoId_GetHardwareChannelHiddenFlags,
                                    &streamFormatId,
                                    sizeof(streamFormatId),
                                    &hiddenFlags,
                                    sizeof(TUsbAudioHardwareChannelHiddenFlags),
                                    &byteCount
                                    );

    return st;
}

TUsbAudioStatus
TUsbAudioApiExtendedInfo::GetValidVirtualSoundDeviceProfiles(
    TUsbAudioHandle deviceHandle,
    bool input,
    TUsbAudioSoundDeviceProfileInfo* soundDeviceProfileInfo,
    unsigned int maxNumSoundDeviceProfileInfo,
    unsigned int& numSoundDeviceProfileInfo
    )
{
    numSoundDeviceProfileInfo = 0;

    unsigned int byteCount = 0;
    unsigned int inBuf = (input ? 1 : 0);
    TUsbAudioStatus st = mApiDll.TUSBAUDIO_GetExtendedInfo(
                                    deviceHandle,
                                    ExtendedInfoId_GetValidVirtualSoundDeviceProfiles,
                                    &inBuf,
                                    sizeof(inBuf),
                                    soundDeviceProfileInfo,
                                    maxNumSoundDeviceProfileInfo * sizeof(TUsbAudioSoundDeviceProfileInfo),
                                    &byteCount
                                    );
    if (SUCC(st)) {
        numSoundDeviceProfileInfo = byteCount / sizeof(TUsbAudioSoundDeviceProfileInfo);
    }

    return st;
}

TUsbAudioStatus
TUsbAudioApiExtendedInfo::GetCurrentVirtualSoundDeviceProfile(
    TUsbAudioHandle deviceHandle,
    bool input,
    TUsbAudioSoundDeviceProfileInfo& soundDeviceProfileInfo
    )
{
    unsigned int byteCount = 0;
    unsigned int inBuf = (input ? 1 : 0);
    TUsbAudioStatus st = mApiDll.TUSBAUDIO_GetExtendedInfo(
                                    deviceHandle,
                                    ExtendedInfoId_GetCurrentVirtualSoundDeviceProfile,
                                    &inBuf,
                                    sizeof(inBuf),
                                    &soundDeviceProfileInfo,
                                    sizeof(TUsbAudioSoundDeviceProfileInfo),
                                    &byteCount
                                    );

    return st;
}
