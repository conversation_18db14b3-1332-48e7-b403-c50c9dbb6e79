<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MessageBoxWidget4</class>
 <widget class="QWidget" name="MessageBoxWidget4">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>300</width>
    <height>200</height>
   </rect>
  </property>
  <property name="sizePolicy">
   <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
    <horstretch>0</horstretch>
    <verstretch>0</verstretch>
   </sizepolicy>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QGridLayout" name="gridLayout_3" rowstretch="70,90,130,60,90">
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <property name="spacing">
    <number>0</number>
   </property>
   <item row="0" column="0">
    <spacer name="verticalSpacer_2">
     <property name="orientation">
      <enum>Qt::Orientation::Vertical</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>20</width>
       <height>34</height>
      </size>
     </property>
    </spacer>
   </item>
   <item row="1" column="0">
    <layout class="QGridLayout" name="gridLayout_2" columnstretch="8,100,8">
     <property name="spacing">
      <number>0</number>
     </property>
     <item row="0" column="0">
      <spacer name="horizontalSpacer">
       <property name="orientation">
        <enum>Qt::Orientation::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item row="0" column="1">
      <layout class="QGridLayout" name="gridLayout" rowstretch="100,60,100">
       <property name="spacing">
        <number>0</number>
       </property>
       <item row="0" column="0">
        <widget class="QCheckBox" name="CheckBox1">
         <property name="sizePolicy">
          <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
           <horstretch>0</horstretch>
           <verstretch>0</verstretch>
          </sizepolicy>
         </property>
         <property name="minimumSize">
          <size>
           <width>1</width>
           <height>1</height>
          </size>
         </property>
         <property name="text">
          <string/>
         </property>
        </widget>
       </item>
       <item row="1" column="0">
        <spacer name="verticalSpacer">
         <property name="orientation">
          <enum>Qt::Orientation::Vertical</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>20</width>
           <height>17</height>
          </size>
         </property>
        </spacer>
       </item>
       <item row="2" column="0">
        <widget class="QCheckBox" name="CheckBox2">
         <property name="sizePolicy">
          <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
           <horstretch>0</horstretch>
           <verstretch>0</verstretch>
          </sizepolicy>
         </property>
         <property name="minimumSize">
          <size>
           <width>1</width>
           <height>1</height>
          </size>
         </property>
         <property name="text">
          <string/>
         </property>
        </widget>
       </item>
      </layout>
     </item>
     <item row="0" column="2">
      <spacer name="horizontalSpacer_2">
       <property name="orientation">
        <enum>Qt::Orientation::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
    </layout>
   </item>
   <item row="2" column="0">
    <spacer name="verticalSpacer_3">
     <property name="orientation">
      <enum>Qt::Orientation::Vertical</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>20</width>
       <height>35</height>
      </size>
     </property>
    </spacer>
   </item>
   <item row="3" column="0">
    <layout class="QGridLayout" name="gridLayout_11" columnstretch="100,80,10,80,100">
     <property name="spacing">
      <number>0</number>
     </property>
     <item row="0" column="0">
      <spacer name="horizontalSpacer_23">
       <property name="orientation">
        <enum>Qt::Orientation::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>1</height>
        </size>
       </property>
      </spacer>
     </item>
     <item row="0" column="1">
      <widget class="QPushButton" name="PushButton1">
       <property name="sizePolicy">
        <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="minimumSize">
        <size>
         <width>1</width>
         <height>1</height>
        </size>
       </property>
       <property name="text">
        <string/>
       </property>
      </widget>
     </item>
     <item row="0" column="2">
      <spacer name="horizontalSpacer_24">
       <property name="orientation">
        <enum>Qt::Orientation::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item row="0" column="3">
      <widget class="QPushButton" name="PushButton2">
       <property name="sizePolicy">
        <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="minimumSize">
        <size>
         <width>1</width>
         <height>1</height>
        </size>
       </property>
       <property name="text">
        <string/>
       </property>
      </widget>
     </item>
     <item row="0" column="4">
      <spacer name="horizontalSpacer_25">
       <property name="orientation">
        <enum>Qt::Orientation::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>1</height>
        </size>
       </property>
      </spacer>
     </item>
    </layout>
   </item>
   <item row="4" column="0">
    <spacer name="verticalSpacer_4">
     <property name="orientation">
      <enum>Qt::Orientation::Vertical</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>20</width>
       <height>34</height>
      </size>
     </property>
    </spacer>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
