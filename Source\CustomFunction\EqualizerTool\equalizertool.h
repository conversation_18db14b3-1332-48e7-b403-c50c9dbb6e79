#ifndef EQUALIZERTOOL_H
#define EQUALIZERTOOL_H


#include <QHash>
#include <QObject>
#include <QPointF>
#include <complex>


class EqualizerTool : public QObject
{
    Q_OBJECT
public:
    EqualizerTool(unsigned int freqStart=20, unsigned int freqEnd=22000, unsigned int sampleCount=676, bool check=false);
    ~EqualizerTool();
    enum FilterType
    {
        Peaking,
        LowPass,
        HighPass,
        LowShelf,
        HighShelf,
        BandPass,
        BandStop,
        Notch,
        AllPass,
        Custom
    };
    struct BandData
    {
        QString name;
        bool status;
        FilterType type;
        double freq;
        double gain;
        double q;
    };
    void setLanguage(QString language) { mLanguage = language; }
    QString importCurve(QString name, QString path="");
    QString exportCurve(QString name, QString path="");
    QString importBandData(QString path="");
    QString exportBandData(QString path="");
    void modifyPreGain(double gain);
    void modifyBandData(QString name, bool status, FilterType type, double freq, double gain, double q, double sampleRate=48000);
    void modifyBandData(BandData data, double sampleRate=48000);
    void modifyBandData(QVector<BandData> dataList, double sampleRate=48000);
    QHash<QString, BandData>& getBandData() { return mFilterParameter; }
    QVector<QPointF>& getCurve(QString name);
    QVector<QPointF>& getCurveBand(QString name) { return *(mCurveBand.value(name)); }
    QVector<QPointF>& getCurveTarget() { return mCurveTarget; }
    QVector<QPointF>& getCurveTargetRaw() { return mCurveTargetRaw; }
    QVector<QPointF>& getCurveTargetCompensated() { return mCurveTargetCompensated; }
    QVector<QPointF>& getCurveSource() { return mCurveSource; }
    QVector<QPointF>& getCurveSourceRaw() { return mCurveSourceRaw; }
    QVector<QPointF>& getCurveSourceCompensated() { return mCurveSourceCompensated; }
    QVector<QPointF>& getCurveCombined() { return mCurveCombined; }
    QVector<QPointF>& getCurveFilteredRaw() { return mCurveFilteredRaw; }
    QVector<QPointF>& getCurveFilteredRawAlignment() { return mCurveFilteredRawAlignment; }
    QVector<QPointF>& getCurveFilteredCompensated() { return mCurveFilteredCompensated; }
    QVector<QPointF>& getCurveFilteredCompensatedAlignment() { return mCurveFilteredCompensatedAlignment; }
private:
    struct FilterCoefficient
    {
        QVector<double> numerator;
        QVector<double> denominator;
    };
    double mPreGain=0;
    QString mLanguage="English";
    static constexpr double mPI=3.14159265358979323846;
    unsigned int mFreqStart, mFreqEnd;
    QVector<unsigned int> mSampleTick;
    QHash<QString, BandData> mFilterParameter;
    QHash<QString, FilterCoefficient> mFilterCoefficient;
    QHash<QString, QVector<std::complex<double>>*> mFrequencyResponse;
    QHash<QString, QVector<QPointF>*> mCurveBand;
    QVector<QPointF> mCurveTarget;
    QVector<QPointF> mCurveTargetRaw;
    QVector<QPointF> mCurveTargetCompensated;
    QVector<QPointF> mCurveSource;
    QVector<QPointF> mCurveSourceRaw;
    QVector<QPointF> mCurveSourceCompensated;
    QVector<QPointF> mCurveCombined;
    QVector<QPointF> mCurveFilteredRaw;
    QVector<QPointF> mCurveFilteredRawAlignment;
    QVector<QPointF> mCurveFilteredCompensated;
    QVector<QPointF> mCurveFilteredCompensatedAlignment;
    FilterCoefficient calculateFilterCoefficient(FilterType type, double freq, double gain, double q, double sampleRate=48000);
    void calculateCurveBand(QString name);
    void calculateCurveTarget();
    void calculateCurveSource();
    void calculateCurveCombined();
    void calculateCurveFiltered();
signals:
};


#endif // EQUALIZERTOOL_H

