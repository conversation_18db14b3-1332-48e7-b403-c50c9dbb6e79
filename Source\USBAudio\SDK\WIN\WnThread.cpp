/************************************************************************
 *
 *  Module:       WnThread.cpp
 *
 *  Description:  Thread object wrapper
 *
 *  Runtime Env.: Win32
 *
 *  Author(s):
 *    <PERSON><PERSON>,  <EMAIL>
 *
 *  Companies:
 *    Thesycon GmbH, Germany      http://www.thesycon.de
 *
 ************************************************************************/

#include "libwn_min_global.h"

// Module is empty if .h file was not included (category turned off).
#ifdef __WnThread_h__

#if !defined(UNDER_CE)
#include <process.h>
#endif

// optionally put everything into a namespace
#ifdef LIBWN_NAMESPACE
namespace LIBWN_NAMESPACE {
#endif

// ctor
WnThread::WnThread()
{
    mThreadId = 0;
}


// dtor
WnThread::~WnThread()
{
    // WaitForTerminationAndClose MUST BE called before this instance is destroyed!
    WNASSERT(!IsValid());
    WNASSERT(!IsRunning());
}


//virtual
void
WnThread::Close()
{
    // call base class
    WnHandle::Close();

    mThreadId = 0;
}


bool
WnThread::IsRunning() const
{
    if ( !IsValid() ) {
        // no valid handle
        return false;
    }

    DWORD err = Wait(0);
    if ( WAIT_OBJECT_0 == err ) {
        // handle is signaled
        return false;
    }

    // thread is still executing
    return true;
}




WNERR
WnThread::CreateCrtAwareThread(
    WnThreadCallbackInterface* callback,
    unsigned int stackSize,
    unsigned int flags
    )
{
#ifdef UNDER_CE
    return CreateNativeThread(callback, stackSize, flags);
#else
    if ( IsValid() ) {
        WNTRACE(TRCERR,tprint(__FUNCTION__": FATAL: thread is already created\n"));
        return ERROR_ALREADY_EXISTS;
    }

    // create a new thread
    // use _beginthreadex which is a CRT wrapper for the Win32 call CreateThread()
    HANDLE h = (HANDLE) _beginthreadex(
                                      NULL,                 // void *security,
                                      stackSize,            // unsigned stack_size,
                                      beginthread_routine,  // unsigned ( __stdcall *start_address )( void * ),
                                      callback,             // void *arglist,
                                      flags,                // unsigned initflag,
                                      (unsigned int*)&mThreadId   // unsigned *thrdaddr
                                      );
    if ( NULL == h ) {
        // failed
        DWORD err = ::GetLastError();
        WNTRACE(TRCERR,tprint(__FUNCTION__": _beginthreadex failed, err=0x%X\n", err));
        mThreadId = 0;
        return err;
    }

    // success
    mHandle = h;

    // thread has been created successfully
    return ERROR_SUCCESS;
#endif // UNDER_CE

}//CreateCrtAwareThread


WNERR
WnThread::CreateNativeThread(
    WnThreadCallbackInterface* callback,
    DWORD stackSize,
    DWORD flags
    )
{
    if ( IsValid() ) {
        WNTRACE(TRCERR,tprint(__FUNCTION__": FATAL: thread is already created\n"));
        return ERROR_ALREADY_EXISTS;
    }

    // create a new thread
    HANDLE h = ::CreateThread(
                            NULL,             //LPSECURITY_ATTRIBUTES lpThreadAttributes,
                            stackSize,        //SIZE_T dwStackSize,
                            thread_routine,   //LPTHREAD_START_ROUTINE lpStartAddress,
                            callback,         //LPVOID lpParameter,
                            flags,            //DWORD dwCreationFlags,
                            &mThreadId        //LPDWORD lpThreadId
                            );
    if ( NULL == h ) {
        // failed
        DWORD err = ::GetLastError();
        WNTRACE(TRCERR,tprint(__FUNCTION__": CreateThread failed, err=0x%X\n", err));
        mThreadId = 0;
        return err;
    }

    // success
    mHandle = h;

    // thread has been created successfully
    return ERROR_SUCCESS;

}//CreateNativeThread



//static
unsigned int
__stdcall
WnThread::beginthread_routine(
    void* context
    )
{
    WnThreadCallbackInterface* callback = (WnThreadCallbackInterface*)context;
    return callback->ThreadRoutine();
}

//static
DWORD
WINAPI
WnThread::thread_routine(
    void* context
    )
{
    WnThreadCallbackInterface* callback = (WnThreadCallbackInterface*)context;
    return callback->ThreadRoutine();
}


DWORD
WnThread::WaitForTerminationAndClose(
    DWORD timeoutMilliseconds
    )
{
    if ( !IsValid() ) {
        // thread does not exist
        return ERROR_SUCCESS;
    }

    // wait for the thread to terminate
    DWORD err = Wait(timeoutMilliseconds);
    switch(err) {

    case WAIT_OBJECT_0:
        // close thread handle
        Close();
        err = ERROR_SUCCESS;
        break;

    case WAIT_TIMEOUT:
        WNTRACE(TRCINF,tprint(__FUNCTION__": Wait timed out\n"));
        break;

    case WAIT_FAILED:
        WNTRACE(TRCERR,tprint(__FUNCTION__": Wait failed, err=0x%X\n", ::GetLastError() ));
        break;

    default:
        WNTRACE(TRCERR,tprint(__FUNCTION__": WaitForSingleObject unknown return code, err=0x%X\n", err));
        break;
    }

    return err;

}//WaitForTerminationAndClose


DWORD
WnThread::WaitForTermination(
    DWORD timeoutMilliseconds
    )
{
    if ( !IsValid() ) {
        // thread does not exist
        return ERROR_SUCCESS;
    }

    // wait for the thread to terminate
    DWORD err = Wait(timeoutMilliseconds);
    switch(err) {

    case WAIT_OBJECT_0:
        break;

    case WAIT_TIMEOUT:
        WNTRACE(TRCINF,tprint(__FUNCTION__": Wait timed out\n"));
        break;

    case WAIT_FAILED:
        WNTRACE(TRCERR,tprint(__FUNCTION__": Wait failed, err=0x%X\n", ::GetLastError() ));
        break;

    default:
        WNTRACE(TRCERR,tprint(__FUNCTION__": WaitForSingleObject unknown return code, err=0x%X\n", err));
        break;
    }

    return err;

}//WaitForTermination


WNERR
WnThread::SetThreadPriority(int nPriority)
{
    WNASSERT(IsValid());
    WNERR err = ERROR_SUCCESS;

    BOOL succ = ::SetThreadPriority(GetHandle(), nPriority);
    if (!succ) {
        // failed
        err = ::GetLastError();
        WNTRACE(TRCERR,tprint(__FUNCTION__": SetThreadPriority failed, err=0x%X\n", err));
    }

    return err;
}

//static
WNERR
WnThread::SetCurrentThreadPriority(int nPriority)
{
    WNERR err = ERROR_SUCCESS;

    BOOL succ = ::SetThreadPriority(::GetCurrentThread(), nPriority);
    if (!succ) {
        // failed
        err = ::GetLastError();
        WNTRACE(TRCERR,tprint(__FUNCTION__": SetThreadPriority failed, err=0x%X\n", err));
    }

    return err;
}

WNERR
WnThread::GetThreadPriority(int& nPriority)
{
    WNERR err = ERROR_SUCCESS;

    nPriority = ::GetThreadPriority(GetHandle());
    if (THREAD_PRIORITY_ERROR_RETURN == nPriority) {
        // failed
        err = ::GetLastError();
        WNTRACE(TRCERR,tprint(__FUNCTION__": GetThreadPriority failed, err=0x%X\n", err));
    }

    return err;
}

//static
WNERR
WnThread::GetCurrentThreadPriority(int& nPriority)
{
    WNERR err = ERROR_SUCCESS;

    nPriority = ::GetThreadPriority(::GetCurrentThread());
    if (THREAD_PRIORITY_ERROR_RETURN == nPriority) {
        // failed
        err = ::GetLastError();
        WNTRACE(TRCERR,tprint(__FUNCTION__": GetThreadPriority failed, err=0x%X\n", err));
    }

    return err;
}

#ifdef LIBWN_NAMESPACE
}
#endif

#endif

/*************************** EOF **************************************/
