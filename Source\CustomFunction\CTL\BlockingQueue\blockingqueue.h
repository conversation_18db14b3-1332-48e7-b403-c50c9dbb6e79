#ifndef BLOCKINGQUEUE_H
#define BLOCKINGQUEUE_H


#include <QQueue>
#include <QMutex>
#include <QWaitCondition>


template<typename T>
class BlockingQueue
{
public:
    void enqueue(const T& value)
    {
        QMutexLocker locker(&mMutex);
        mQueue.enqueue(value);
        mNotEmpty.wakeOne();
    }
    T dequeue()
    {
        QMutexLocker locker(&mMutex);
        while(mQueue.isEmpty())
        {
            mNotEmpty.wait(&mMutex);
        }
        return mQueue.dequeue();
    }
    bool tryDequeue(T& out)
    {
        QMutexLocker locker(&mMutex);
        if(mQueue.isEmpty())
        {
            return false;
        }
        out = mQueue.dequeue();
        return true;
    }
    bool isEmpty()
    {
        QMutexLocker locker(&mMutex);
        return mQueue.isEmpty();
    }
private:
    QQueue<T> mQueue;
    mutable QMutex mMutex;
    QWaitCondition mNotEmpty;
};


#endif // BLOCKINGQUEUE_H

