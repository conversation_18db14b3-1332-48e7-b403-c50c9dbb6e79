/************************************************************************
 *
 *  Module:       CommonPluginProperties.h
 *  Description:  common data structs for DSP plugins
 *
 *  Runtime Env.: any
 *
 *  Author(s):
 *    <PERSON><PERSON>,  Udo.E<PERSON><EMAIL>
 *    <PERSON>, <PERSON>@thesycon.de
 *  Company:
 *    Thesycon GmbH, Germany      http://www.thesycon.de
 *
 ************************************************************************/

#ifndef __CommonPluginProperties_h__
#define __CommonPluginProperties_h__

#include "dsp_types.h"


// struct alignment = 1 byte
#include <pshpack1.h>



//
// common header for the property data buffer
//
typedef struct tagCommonPropertyHeader
{
    // module that implements this property
    int moduleId;

    // module-specific property ID, defined by an enum
    int propertyId;

} CommonPropertyHeader;



// restore previous alignment
#include <poppack.h>



#endif // __CommonPluginProperties_h__

/******************************** EOF ***********************************/
