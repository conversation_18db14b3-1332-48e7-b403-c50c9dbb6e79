#include "inputs1m6.h"
#include "globalfont.h"
#include "ui_inputs1m6.h"


InputS1M6::InputS1M6(QWidget* parent, QString name)
    : InputBase(parent)
    , WorkspaceObserver(name)
    , AppSettingsObserver(name)
    , ui(new Ui::InputS1M6)
{
    ui->setupUi(this);
    installEventFilter(this);
    resize(minimumWidth(), minimumHeight());
    QString style;
    style = "QFrame {"
            "   background-color: #161616;"
            "   border-radius: 8px;"
            "}";
    ui->frame->setStyleSheet(style);
    style = "QWidget {"
            "   background-color: rgba(31, 31, 31, 153);"
            "   border-radius: 8px;"
            "}";
    ui->widgetOverlay->setStyleSheet(style);
    ui->widgetOverlay->setAttribute(Qt::WA_TransparentForMouseEvents);
    ui->widgetOverlay->setHidden(true);
    style = "QLineEdit {"
            "   color: rgb(161, 161, 161);"
            "   background-color: rgb(46, 46, 46);"
            "   border-radius: 8px;"
            "   selection-color: rgb(0, 121, 107);"
            "   selection-background-color: rgb(224, 247, 250);"
            "}";
    ui->lineEdit->setStyleSheet(style);
    ui->lineEdit->setAttribute(Qt::WA_Hover);
    ui->lineEdit->installEventFilter(this);
    style = "QPushButton {"
            "   background-color: transparent;"
            "   image: url(:/Icon/WidgetCloseBlack.png);"
            "}"
            "QPushButton:hover {"
            "   border: 2px solid rgb(46, 46, 46);"
            "   border-radius: 3px;"
            "}";
    ui->pushButtonClose->setStyleSheet(style);
    ui->pushButtonClose->setParent(this);
    ui->pushButtonClose->setFocusPolicy(Qt::NoFocus);
    ui->pushButtonClose->hide();
    ui->pushButtonClose->setAttribute(Qt::WA_Hover);
    ui->pushButtonClose->installEventFilter(this);
    ui->widgetVolumeMeter->setWidthRatio(5, 10, 20, 26);
    ui->widgetVolumeMeter->setHeightRatio(8, 3, 2, 2, 85, 0);
    ui->widgetVolumeMeter->setScaleLineHidden(true);
    ui->widgetDial->showCircle(false).showInfinitesimal(true);
    mTimer.setSingleShot(true);
    mTimer.setInterval(100);
    connect(&mTimer, SIGNAL(timeout()), this, SLOT(in_mTimer_timeout()), Qt::UniqueConnection);
    connect(ui->widgetDial, SIGNAL(valueChanged(float)), this, SLOT(in_widgetDial_valueChanged(float)), Qt::UniqueConnection);
    connect(ui->widgetPushButtonGroup2, SIGNAL(stateChanged(QString, QString)), this, SLOT(in_widgetPushButtonGroup2_stateChanged(QString, QString)), Qt::UniqueConnection);
}
InputS1M6::~InputS1M6()
{
    delete ui;
}


// override
bool InputS1M6::eventFilter(QObject* obj, QEvent* e)
{
    if((obj == ui->lineEdit || obj == ui->pushButtonClose) && isWidgetEnable() && isWidgetMovable())
    {
        if(e->type() == QEvent::HoverEnter)
        {
            if(ui->pushButtonClose->isHidden())
            {
                mTimer.start();
            }
            return true;
        }
        else if(e->type() == QEvent::HoverLeave)
        {
            QTimer::singleShot(0, [this](){
                if(!ui->lineEdit->underMouse() && !ui->pushButtonClose->underMouse())
                {
                    mTimer.stop();
                    ui->pushButtonClose->hide();
                }
            });
        }
        if(obj == ui->lineEdit && e->type() == QEvent::MouseButtonPress && ui->lineEdit->isEnabled())
        {
            mTimer.stop();
            ui->pushButtonClose->hide();
        }
    }
    else if(obj == this && isWidgetEnable() && isWidgetMovable())
    {
        if(e->type() == QEvent::Move)
        {
            QTimer::singleShot(0, [this](){
                if(ui->lineEdit->geometry().contains(ui->frame->mapFromGlobal(QCursor::pos())))
                {
                    mTimer.start();
                }
            });
        }
    }
    return QWidget::eventFilter(obj, e);
}
void InputS1M6::resizeEvent(QResizeEvent* e)
{
    Q_UNUSED(e);
    // W
    float wPixelPerRatio=size().width() / 100.0;
    int wSpace1=wPixelPerRatio * 15;
    int wMeter=wPixelPerRatio * 70;
    int xMeter=wSpace1;
    // H
    float hPixelPerRatio = size().height() / 100.0;
    int hLineEdit=hPixelPerRatio * 8;
    int hSpace1=hPixelPerRatio * 0;
    int hMeter=hPixelPerRatio * 50;
    int hSpace2=hPixelPerRatio * 1;
    int hDial=hPixelPerRatio * 19;
    int hSpace3=hPixelPerRatio * 1;
    int hButtonGroup2=hPixelPerRatio * 20;
    int hPushButtonClose=hLineEdit / 100.0 * 50;
    ui->lineEdit->setGeometry(0, 0, size().width(), hLineEdit);
    ui->pushButtonClose->setGeometry(size().width() - hPushButtonClose * 1.3, (hLineEdit - hPushButtonClose) / 2, hPushButtonClose, hPushButtonClose);
    ui->widgetVolumeMeter->setGeometry(xMeter, hLineEdit + hSpace1, wMeter, hMeter);
    ui->widgetDial->setGeometry(0, hLineEdit + hSpace1 + hMeter + hSpace2, size().width(), hDial);
    ui->widgetPushButtonGroup2->setGeometry(wPixelPerRatio * 1.9, hPixelPerRatio * 75.5, size().width() - wPixelPerRatio * 3.8, (size().width() - wPixelPerRatio * 3.8) / ui->widgetPushButtonGroup2->minimumWidth() * ui->widgetPushButtonGroup2->minimumHeight());
    ui->widgetOverlay->setGeometry(rect().x(), rect().y(), rect().width(), hLineEdit);
    mFont.setPointSize(GLBFHandle.getSuitablePointSize(mFont, ui->lineEdit->height()) - 3);
    if(mFont.pointSize() < 8)
    {
        mFont.setPointSize(mFont.pointSize());
    }
    else if(mFont.pointSize() < 12)
    {
        mFont.setPointSize(mFont.pointSize() - 1);
    }
    else if(mFont.pointSize() < 17)
    {
        mFont.setPointSize(mFont.pointSize() - 2);
    }
    else if(mFont.pointSize() < 22)
    {
        mFont.setPointSize(mFont.pointSize() - 3);
    }
    mFont.setPointSize(mFont.pointSize() - 1);
    ui->lineEdit->setFont(mFont);
    int radius=size().width() * 0.06;
    QString style;
    style = QString("QFrame {"
                    "   background-color: #161616;"
                    "   border-radius: %1px;"
                    "}").arg(radius);
    ui->frame->setStyleSheet(style);
    style = QString("QWidget {"
                    "   background-color: rgba(31, 31, 31, 153);"
                    "   border-top-left-radius: %1px; border-top-right-radius: %1px;"
                    "}").arg(radius);
    ui->widgetOverlay->setStyleSheet(style);
    style = QString("QLineEdit {"
                    "   color: rgb(161, 161, 161);"
                    "   background-color: rgb(46, 46, 46);"
                    "   border-top-left-radius: %1px; border-top-right-radius: %1px;"
                    "   selection-color: rgb(0, 121, 107);"
                    "   selection-background-color: rgb(224, 247, 250);"
                    "}").arg(radius);
    ui->lineEdit->setStyleSheet(style);
}
void InputS1M6::updateAttribute()
{
    if(isWidgetReady())
    {
        float gain;
        if(isWidgetEnable())
        {
            if(mMuteAffectGain)
            {
                gain = ui->widgetPushButtonGroup2->getState("MUTE").toInt() ? (mDisableGAIN) : (ui->widgetDial->getValue());
            }
            else
            {
                gain = ui->widgetDial->getValue();
            }
            if(mPreGAIN != gain)
            {
                mPreGAIN = gain;
                emit attributeChanged(this->objectName(), "GAIN", QString::number(mPreGAIN));
            }
            if(mPreMUTE != ui->widgetPushButtonGroup2->getState("MUTE").toInt())
            {
                mPreMUTE = ui->widgetPushButtonGroup2->getState("MUTE").toInt();
                emit attributeChanged(this->objectName(), "MUTE", QString::number(mPreMUTE));
            }
            if(mPreANTI != ui->widgetPushButtonGroup2->getState("ANTI").toInt())
            {
                mPreANTI = ui->widgetPushButtonGroup2->getState("ANTI").toInt();
                emit attributeChanged(this->objectName(), "ANTI", QString::number(mPreANTI));
            }
        }
        else
        {
            if(mMuteAffectGain)
            {
                gain = mDisableGAIN;
            }
            else
            {
                gain = ui->widgetDial->getValue();
            }
            if(mPreGAIN != gain)
            {
                mPreGAIN = gain;
                emit attributeChanged(this->objectName(), "GAIN", QString::number(mPreGAIN));
            }
            if(mPreMUTE != static_cast<int>(true))
            {
                mPreMUTE = static_cast<int>(true);
                emit attributeChanged(this->objectName(), "MUTE", QString::number(mPreMUTE));
            }
            if(mPreANTI != static_cast<int>(false))
            {
                mPreANTI = static_cast<int>(false);
                emit attributeChanged(this->objectName(), "ANTI", QString::number(mPreANTI));
            }
        }
    }
}
void InputS1M6::setSoloState(bool state)
{
    ui->widgetPushButtonGroup2->setState("SOLO", QString::number(state), false);
}
void InputS1M6::setSoloStateLeft(bool state)
{
    Q_UNUSED(state);
}
void InputS1M6::setSoloStateRight(bool state)
{
    Q_UNUSED(state);
}
void InputS1M6::setMuteState(bool state)
{
    ui->widgetPushButtonGroup2->setState("MUTE", QString::number(state), false);
}
void InputS1M6::setMuteStateLeft(bool state)
{
    Q_UNUSED(state);
}
void InputS1M6::setMuteStateRight(bool state)
{
    Q_UNUSED(state);
}
void InputS1M6::setSoloClicked(bool state)
{
    ui->widgetPushButtonGroup2->setState("SOLO", QString::number(state), true);
}
void InputS1M6::setSoloClickedLeft(bool state)
{
    Q_UNUSED(state);
}
void InputS1M6::setSoloClickedRight(bool state)
{
    Q_UNUSED(state);
}
void InputS1M6::setMuteClicked(bool state)
{
    ui->widgetPushButtonGroup2->setState("MUTE", QString::number(state), true);
}
void InputS1M6::setMuteClickedLeft(bool state)
{
    Q_UNUSED(state);
}
void InputS1M6::setMuteClickedRight(bool state)
{
    Q_UNUSED(state);
}
bool InputS1M6::getSoloState()
{
    return (bool) ui->widgetPushButtonGroup2->getState("SOLO").toInt();
}
bool InputS1M6::getSoloStateLeft()
{
    return false;
}
bool InputS1M6::getSoloStateRight()
{
    return false;
}
bool InputS1M6::getMuteState()
{
    return (bool) ui->widgetPushButtonGroup2->getState("MUTE").toInt();
}
bool InputS1M6::getMuteStateLeft()
{
    return false;
}
bool InputS1M6::getMuteStateRight()
{
    return false;
}
void InputS1M6::loadSettings()
{
    mPreMUTE = -2147483648;
    mPreANTI = -2147483648;
    mPreGAIN = -2147483648;
    setWidgetReady(false);
    WorkspaceObserver::getSettings()->beginGroup(objectName());
    bool flag=WorkspaceObserver::getSettings()->contains(objectName());
    WorkspaceObserver::getSettings()->endGroup();
    if(!flag)
    {
        WorkspaceObserver::setValue(objectName(), true);
        WorkspaceObserver::setValue("ChannelName", getChannelName());
        WorkspaceObserver::setValue("ANTI", false);
        WorkspaceObserver::setValue("SOLO", false);
        WorkspaceObserver::setValue("MUTE", false);
        WorkspaceObserver::setValue("GAIN", ui->widgetDial->getDefault());
    }
    ui->lineEdit->setText(WorkspaceObserver::value("ChannelName").toString());
    ui->widgetPushButtonGroup2->setState("ANTI", QString::number(WorkspaceObserver::value("ANTI").toBool()), false);
    ui->widgetDial->setValue(WorkspaceObserver::value("GAIN").toFloat());
    // must load SOLO & MUTE at the end
    loadSoloMuteState(true,
                      WorkspaceObserver::value("SOLO").toBool(),
                      false,
                      false,
                      WorkspaceObserver::value("MUTE").toBool(),
                      false,
                      false
                      );
    if(isWidgetEmitAction())
    {
        emit attributeChanged(this->objectName(), "Save_ChannelName", WorkspaceObserver::value("ChannelName").toString());
        emit attributeChanged(this->objectName(), "Save_Enable", QString::number(isWidgetEnable()));
        emit attributeChanged(this->objectName(), "Save_ANTI", QString::number(WorkspaceObserver::value("ANTI").toBool()));
        emit attributeChanged(this->objectName(), "Save_SOLO", QString::number(WorkspaceObserver::value("SOLO").toBool()));
        emit attributeChanged(this->objectName(), "Save_MUTE", QString::number(WorkspaceObserver::value("MUTE").toBool()));
        emit attributeChanged(this->objectName(), "Save_GAIN", WorkspaceObserver::value("GAIN").toString());
    }
    setWidgetReady(true);
    updateAttribute();
}
void InputS1M6::AppSettingsChanged(QString objectName, QString attribute, QString value)
{
    Q_UNUSED(objectName);
    Q_UNUSED(attribute);
    Q_UNUSED(value);
}


// slot
void InputS1M6::in_mTimer_timeout()
{
    ui->pushButtonClose->show();
}
void InputS1M6::in_widgetDial_valueChanged(float value)
{
    save("GAIN", value);
    updateAttribute();
    if(mGainAffectMute && ui->widgetPushButtonGroup2->getState("MUTE").toInt())
    {
        ui->widgetPushButtonGroup2->setState("MUTE", QString::number(false), true);
    }
}
void InputS1M6::in_widgetPushButtonGroup2_stateChanged(QString button, QString state)
{
    if(button == "SOLO")
    {
        if(doSolo())
        {
            save("SOLO", (bool) state.toInt());
        }
    }
    else if(button == "MUTE")
    {
        if(doMute())
        {
            save("MUTE", (bool) state.toInt());
        }
        updateAttribute();
    }
    else if(button == "ANTI")
    {
        save("ANTI", (bool) state.toInt());
        updateAttribute();
    }
}
void InputS1M6::on_lineEdit_textChanged(const QString& arg1)
{
    QFont font=ui->lineEdit->font();
    font.setPointSize(5);
    if(!GLBFHandle.isSuitable(font, arg1, minimumWidth() - 6))
    {
        ui->lineEdit->setText(arg1.chopped(1));
    }
}
void InputS1M6::on_lineEdit_editingFinished()
{
    if(ui->lineEdit->text().isEmpty())
    {
        ui->lineEdit->setText(getChannelName());
    }
    ui->lineEdit->clearFocus();
    save("ChannelName", ui->lineEdit->text());
}
void InputS1M6::on_pushButtonClose_clicked()
{
    emit attributeChanged(getChannelName(), "Hide", QString::number(static_cast<int>(true)));
    ui->pushButtonClose->hide();
}


// setter & getter
void InputS1M6::save(QAnyStringView key, const QVariant& value)
{
    WorkspaceObserver::setValue(key, value);
    if(isWidgetEmitAction())
    {
        emit attributeChanged(this->objectName(), "Save_" + key.toString(), value.typeId() == QMetaType::Bool ? (QString::number(value.toBool())) : (value.toString()));
    }
}
InputS1M6& InputS1M6::setName(QString name)
{
    setObjectName(name);
    WorkspaceObserver::setObserverName(name);
    AppSettingsObserver::setObserverName(name);
    return *this;
}
InputS1M6& InputS1M6::setFont(QFont font)
{
    mFont = font;
    ui->widgetVolumeMeter->setFont(font);
    ui->widgetDial->setFont(font);
    ui->widgetPushButtonGroup2->setFont(font);
    QResizeEvent e(size(), size());
    resizeEvent(&e);
    update();
    return *this;
}
InputS1M6& InputS1M6::setVolumeMeterLeft(int value)
{
    ui->widgetVolumeMeter->setValueLeft(value);
    return *this;
}
InputS1M6& InputS1M6::setVolumeMeterLeftClear()
{
    ui->widgetVolumeMeter->setMeterLeftClear();
    return *this;
}
InputS1M6& InputS1M6::setVolumeMeterLeftSlip()
{
    ui->widgetVolumeMeter->setMeterLeftSlip();
    return *this;
}
InputS1M6& InputS1M6::setVolumeMeterRight(int value)
{
    ui->widgetVolumeMeter->setValueRight(value);
    return *this;
}
InputS1M6& InputS1M6::setVolumeMeterRightClear()
{
    ui->widgetVolumeMeter->setMeterRightClear();
    return *this;
}
InputS1M6& InputS1M6::setVolumeMeterRightSlip()
{
    ui->widgetVolumeMeter->setMeterRightSlip();
    return *this;
}
InputS1M6& InputS1M6::setGain(float value)
{
    ui->widgetDial->setValue(value);
    return *this;
}
InputS1M6& InputS1M6::setGainLock(bool state)
{
    ui->widgetDial->setMovable(!state);
    return *this;
}
InputS1M6& InputS1M6::setMuteAffectGain(bool state)
{
    mMuteAffectGain = state;
    return *this;
}
InputS1M6& InputS1M6::setGainAffectMute(bool state)
{
    mGainAffectMute = state;
    return *this;
}
InputS1M6& InputS1M6::setGainRange(int valueStart, int valueEnd05, int valueEnd10, int valueEnd20)
{
    ui->widgetDial->setRange(valueStart, valueEnd05, valueEnd10, valueEnd20);
    return *this;
}
InputS1M6& InputS1M6::setGainDefault(float value)
{
    ui->widgetDial->setDefault(value);
    return *this;
}
InputS1M6& InputS1M6::setGainWidgetDisable(float value)
{
    mDisableGAIN = value;
    return *this;
}
InputS1M6& InputS1M6::setChannelNameEditable(bool state)
{
    ui->lineEdit->setEnabled(state);
    return *this;
}
InputS1M6& InputS1M6::setValueGAIN(float value)
{
    mPreGAIN = value;
    ui->widgetDial->setValue(mPreGAIN);
    save("GAIN", mPreGAIN);
    return *this;
}
InputS1M6& InputS1M6::setValueMUTE(bool state)
{
    mPreMUTE = state;
    ui->widgetPushButtonGroup2->setState("MUTE", QString::number(mPreMUTE), false);
    if(doMute())
    {
        save("MUTE", mPreMUTE);
    }
    return *this;
}
InputS1M6& InputS1M6::setOverlay(bool state)
{
    ui->widgetOverlay->setHidden(!state);
    return *this;
}

