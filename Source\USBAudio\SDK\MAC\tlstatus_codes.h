/************************************************************************

    Description:
        generic status code definitions

    Author(s):
        <PERSON>
        
    Thesycon Software Solutions GmbH & Co. KG, Germany, www.thesycon.de

************************************************************************/

#if !defined(__tlstatus_codes_h__) || defined(__tlstatus_codes_str_impl__)
#define __tlstatus_codes_h__
#define __in_tlstatus_codes_h__

// in case of resource compiler this file is empty
#ifndef RC_INVOKED

// in case we are included from the function body, skip this part
#if !defined(__tlstatus_codes_str_impl__)

// pull in header file for compiler detection
#include "tlrt_platform.h"

// our private status codes are in the range 0x001..0x3FFF
#define TLSTATUS_SUBCODE_BITS       14
#define TLSTATUS_SUBCODE_MASK       ((1u << TLSTATUS_SUBCODE_BITS) - 1)

// extract the sub-code from the specified TLSTATUS code
#define TLSTATUS_EXTRACT_SUBCODE(status) ( (status) & TLSTATUS_SUBCODE_MASK )

// status code range base values
#define TLSTATUS_BASE_EX      0x1000    // project-specific extensions
#define TLSTATUS_BASE_APP     0x2000    // application-specific codes


#ifdef TLRT_COMPILER_MICROSOFT
// Windows

#if defined(TLRT_COMPILER_MICROSOFT_KERNEL_MODE)
    // Windows kernel mode: TLSTATUS is compatible with NTSTATUS
    typedef long TLSTATUS;
#elif defined(TLRT_COMPILER_MICROSOFT_USER_MODE)
    // Windows user mode: TLSTATUS is compatible with DWORD
    typedef unsigned long TLSTATUS;
#else
    #error One of TLRT_COMPILER_MICROSOFT_xxx must be defined.
#endif


// NTSTATUS custom bit
#define TLSTATUS_NTSTATUS_CUSTOM_BIT  0x20000000
// NTSTATUS severity=error
#define TLSTATUS_NTSTATUS_SEV_ERROR   0xC0000000

// facility codes which (hopefully) do not overlap with codes used by MS
#define TLSTATUS_PRIV_STATUS_FACILITY_CODE  0x0E000000
#define TLSTATUS_USBD_STATUS_FACILITY_CODE  0x0F000000
#define TLSTATUS_WSA_STATUS_FACILITY_CODE   0x0D000000

//
// Generate a status code from sub-code x.
//
// Allowed range for x: 0x001..0x3FFF
// We use severity=error and set the C bit
// Examples:
// 0xEE000001  TLSTATUS_FAILED
// 0xEE000003  TLSTATUS_TIMEOUT
//
#define TLSTATUS_CODE(x) ( (TLSTATUS)( TLSTATUS_NTSTATUS_SEV_ERROR | TLSTATUS_NTSTATUS_CUSTOM_BIT | TLSTATUS_PRIV_STATUS_FACILITY_CODE | ((x) & TLSTATUS_SUBCODE_MASK) ))
#define TLSTATUS_SUCCESS_CODE(x) ( (TLSTATUS)( TLSTATUS_NTSTATUS_CUSTOM_BIT | TLSTATUS_PRIV_STATUS_FACILITY_CODE | ((x) & TLSTATUS_SUBCODE_MASK) ))


//
// NTSTATUS to TLSTATUS mapping, see ntstatus.h
//
// We add the C bit (bit 29) to each NTSTATUS code.
// So the upper bits 31..30 still contain the severity code.
// Examples:
// 0xC0000001L (STATUS_UNSUCCESSFUL) maps to 0xE0000001L
// 0x40000000L (STATUS_OBJECT_NAME_EXISTS) maps to 0x60000000L
//
#define TLSTATUS_FROM_NTSTATUS(x) ( (TLSTATUS)((x) | TLSTATUS_NTSTATUS_CUSTOM_BIT) )
#define IS_TLSTATUS_FROM_NTSTATUS(x) ( 0 != ((x) & TLSTATUS_NTSTATUS_CUSTOM_BIT) )

//
// USBD_STATUS to TLSTATUS mapping, see usb.h
//
// For USBD status codes (from URB completion) we add the C bit and use a specific facility code.
// Example:
// 0xC0000004L (USBD_STATUS_STALL_PID) maps to 0xEF000004 (TLSTATUS_USBD_STATUS_STALL_PID)
//
#define TLSTATUS_FROM_USBD_STATUS(x) ( (TLSTATUS)((x) | TLSTATUS_NTSTATUS_CUSTOM_BIT | TLSTATUS_USBD_STATUS_FACILITY_CODE) )

//
// WSA status to TLSTATUS mapping
//
// For WSA status codes (from WSA API) we add the C bit, the error severity and use a specific facility code.
//
#define TLSTATUS_FROM_WSA_STATUS(x) ( (TLSTATUS)((x) | TLSTATUS_NTSTATUS_SEV_ERROR | TLSTATUS_NTSTATUS_CUSTOM_BIT | TLSTATUS_WSA_STATUS_FACILITY_CODE ))

//
// HRESULTs follow the same conventions as Win32 error codes and can simply be casted to TLSTATUS
//
#define TLSTATUS_FROM_HRESULT(x) ( (TLSTATUS)(x) )

//
// for status codes from errno with prefix 0xE0010000
//
#define TLSTATUS_FROM_ERRNO(x) ((TLSTATUS)( 0xE0010000U | ((x) & 0xFFFF) ))

//
// for status codes from CONFIGRET (Config Manager) with prefix 0xE0020000
//
#define TLSTATUS_FROM_CONFIGRET(x) ((0 == x) ? TLSTATUS_SUCCESS : (TLSTATUS)( 0xE0020000U | ((x) & 0xFFFF) ))


#elif defined(TLRT_COMPILER_APPLE)
// Apple
#if defined(TLRT_COMPILER_APPLE_DRIVERKIT_MODE)
#include <DriverKit/IOReturn.h>
#else
#include <IOKit/IOReturn.h>
#endif

typedef IOReturn TLSTATUS;

//
// iokit_vendor_specific_err x -14bit (0 - 0x3fff)
//

#define TLSTATUS_CODE(x) ((TLSTATUS)iokit_vendor_specific_err((x) & TLSTATUS_SUBCODE_MASK))

// for  status codes from errno
#define TLSTATUS_FROM_ERRNO(x) ((TLSTATUS)iokit_vendor_specific_err((x) + 0x3000))


#else
// default, e.g. Linux and embedded
typedef unsigned int TLSTATUS;

// For private status codes we use the range 0xEE000001 ... 0xEE003FFF.
// Note: To have identical numerical values for private status codes on Windows and Linux/embedded, we use an 0xEE prefix.
#define TLSTATUS_CODE(x) ((TLSTATUS)( 0xEE000000U | ((x) & TLSTATUS_SUBCODE_MASK) ))

// for status codes from errno with prefix 0xE0010000
#define TLSTATUS_FROM_ERRNO(x) ((TLSTATUS)( 0xE0010000U | ((x) & 0xFFFF) ))

#endif


// if not included from function body, define an empty macro
#define case_TLSTATUS_(val,txt)

#endif //!defined(__tlstatus_codes_str_impl__)



///////////////////////////////////////////////////////////////////
// common status codes
// This set of generic status codes may be used in all kernel mode and
// user mode modules. Don't add project-specific codes to this set.
// Add specific codes to tstatus_codes_ex.h instead.
// The generic status codes values have to use the range from
// 0x0001 .. 0x0FFF.
// Under Windows this is mapped to
// 0xE0000001 ... 0xE0000FFF
///////////////////////////////////////////////////////////////////

#define        TLSTATUS_SUCCESS                   0
case_TLSTATUS_(TLSTATUS_SUCCESS, "The operation completed successfully.")

#define        TLSTATUS_FAILED                    TLSTATUS_CODE(0x001)
case_TLSTATUS_(TLSTATUS_FAILED, "The operation failed.")

#define        TLSTATUS_INTERNAL_ERROR            TLSTATUS_CODE(0x002)
case_TLSTATUS_(TLSTATUS_INTERNAL_ERROR, "An internal error occurred.")

#define        TLSTATUS_TIMEOUT                   TLSTATUS_CODE(0x003)
case_TLSTATUS_(TLSTATUS_TIMEOUT, "The operation timed out.")

#define        TLSTATUS_REJECTED                  TLSTATUS_CODE(0x004)
case_TLSTATUS_(TLSTATUS_REJECTED, "The operation cannot be executed.")

#define        TLSTATUS_ABORTED                   TLSTATUS_CODE(0x005)
case_TLSTATUS_(TLSTATUS_ABORTED, "The operation was aborted.")

#define        TLSTATUS_IN_USE                    TLSTATUS_CODE(0x006)
case_TLSTATUS_(TLSTATUS_IN_USE, "The requested resource is currently in use.")

#define        TLSTATUS_BUSY                      TLSTATUS_CODE(0x007)
case_TLSTATUS_(TLSTATUS_BUSY, "The device is currently busy.")

#define        TLSTATUS_ALREADY_DONE              TLSTATUS_CODE(0x008)
case_TLSTATUS_(TLSTATUS_ALREADY_DONE, "The operation was already executed and cannot be executed again.")

#define        TLSTATUS_PENDING                   TLSTATUS_CODE(0x009)
case_TLSTATUS_(TLSTATUS_PENDING, "The operation is pending and will be completed at a later time.")

#define        TLSTATUS_WOULD_BLOCK               TLSTATUS_CODE(0x00A)
case_TLSTATUS_(TLSTATUS_WOULD_BLOCK, "The operation cannot be completed immediately.")

#define        TLSTATUS_ACCESS_DENIED             TLSTATUS_CODE(0x00B)
case_TLSTATUS_(TLSTATUS_ACCESS_DENIED, "The access to the object is denied.")


#define        TLSTATUS_MISSING_ARGUMENT          TLSTATUS_CODE(0x010)
case_TLSTATUS_(TLSTATUS_MISSING_ARGUMENT, "Missing argument.")

#define        TLSTATUS_MISSING_OPTION            TLSTATUS_CODE(0x011)
case_TLSTATUS_(TLSTATUS_MISSING_OPTION, "Missing option.")

#define        TLSTATUS_MISSING_VALUE             TLSTATUS_CODE(0x012)
case_TLSTATUS_(TLSTATUS_MISSING_VALUE, "Missing value.")

#define        TLSTATUS_UNKNOWN_OPTION            TLSTATUS_CODE(0x013)
case_TLSTATUS_(TLSTATUS_UNKNOWN_OPTION, "Unknown option.")


#define        TLSTATUS_NO_MEMORY                 TLSTATUS_CODE(0x020)
case_TLSTATUS_(TLSTATUS_NO_MEMORY, "Not enough memory available to complete the operation.")

#define        TLSTATUS_NO_RESOURCES              TLSTATUS_CODE(0x021)
case_TLSTATUS_(TLSTATUS_NO_RESOURCES, "Not enough resources available to complete the operation.")

#define        TLSTATUS_NO_MORE_ITEMS             TLSTATUS_CODE(0x022)
case_TLSTATUS_(TLSTATUS_NO_MORE_ITEMS, "There is no more data available.")

#define        TLSTATUS_NO_DEVICES                TLSTATUS_CODE(0x023)
case_TLSTATUS_(TLSTATUS_NO_DEVICES, "There are no devices available in the system.")

#define        TLSTATUS_NO_DATA                   TLSTATUS_CODE(0x024)
case_TLSTATUS_(TLSTATUS_NO_DATA, "There is no data available.")

#define        TLSTATUS_NO_ADDRESS                TLSTATUS_CODE(0x025)
case_TLSTATUS_(TLSTATUS_NO_ADDRESS, "There is no address available.")

#define        TLSTATUS_NO_SPACE                  TLSTATUS_CODE(0x026)
case_TLSTATUS_(TLSTATUS_NO_SPACE, "There is no free space available.")

#define        TLSTATUS_NO_MATCH                  TLSTATUS_CODE(0x027)
case_TLSTATUS_(TLSTATUS_NO_MATCH, "No match found.")

#define        TLSTATUS_MORE_DATA_REQUIRED        TLSTATUS_CODE(0x028)
case_TLSTATUS_(TLSTATUS_MORE_DATA_REQUIRED, "More data is required.")

#define        TLSTATUS_RETRY_REQUIRED            TLSTATUS_CODE(0x029)
case_TLSTATUS_(TLSTATUS_RETRY_REQUIRED, "The operation should be retried.")

#define        TLSTATUS_FEATURE_NOT_SUPPORTED     TLSTATUS_CODE(0x02A)
case_TLSTATUS_(TLSTATUS_FEATURE_NOT_SUPPORTED, "The feature is not supported.")


#define        TLSTATUS_NOT_SUPPORTED             TLSTATUS_CODE(0x030)
case_TLSTATUS_(TLSTATUS_NOT_SUPPORTED, "The operation is not supported.")

#define        TLSTATUS_NOT_POSSIBLE              TLSTATUS_CODE(0x031)
case_TLSTATUS_(TLSTATUS_NOT_POSSIBLE, "The operation cannot be executed.")

#define        TLSTATUS_NOT_ALLOWED               TLSTATUS_CODE(0x032)
case_TLSTATUS_(TLSTATUS_NOT_ALLOWED, "There is no permission to execute the operation.")

#define        TLSTATUS_NOT_OPENED                TLSTATUS_CODE(0x033)
case_TLSTATUS_(TLSTATUS_NOT_OPENED, "The device or object was not opened.")

#define        TLSTATUS_NOT_AVAILABLE             TLSTATUS_CODE(0x034)
case_TLSTATUS_(TLSTATUS_NOT_AVAILABLE, "The specified resource or object is not available.")

#define        TLSTATUS_INSTANCE_NOT_AVAILABLE    TLSTATUS_CODE(0x035)
case_TLSTATUS_(TLSTATUS_INSTANCE_NOT_AVAILABLE, "The specified instance is not available.")

#define        TLSTATUS_NOT_INITIALIZED           TLSTATUS_CODE(0x036)
case_TLSTATUS_(TLSTATUS_NOT_INITIALIZED, "The module or object was not initialized.")

#define        TLSTATUS_FORMAT_NOT_SUPPORTED      TLSTATUS_CODE(0x037)
case_TLSTATUS_(TLSTATUS_FORMAT_NOT_SUPPORTED, "The data format is not supported.")

#define        TLSTATUS_NOT_PRESENT               TLSTATUS_CODE(0x038)
case_TLSTATUS_(TLSTATUS_NOT_PRESENT, "The object or data is not present.")

#define        TLSTATUS_NOT_FOUND                 TLSTATUS_CODE(0x039)
case_TLSTATUS_(TLSTATUS_NOT_FOUND,  "The object or data was not found.")

#define        TLSTATUS_NOT_ENABLED               TLSTATUS_CODE(0x03A)
case_TLSTATUS_(TLSTATUS_NOT_ENABLED, "The operation is not enabled.")

#define        TLSTATUS_CONVERSION_FAILED         TLSTATUS_CODE(0x03B)
case_TLSTATUS_(TLSTATUS_CONVERSION_FAILED, "Data conversion operation failed.")

#define        TLSTATUS_NOT_HANDLED               TLSTATUS_CODE(0x03C)
case_TLSTATUS_(TLSTATUS_NOT_HANDLED, "The operation was not executed by this function.")

#define        TLSTATUS_DUPLICATE                 TLSTATUS_CODE(0x03D)
case_TLSTATUS_(TLSTATUS_DUPLICATE, "The object already exists.")

#define        TLSTATUS_NOT_COMPATIBLE            TLSTATUS_CODE(0x03E)
case_TLSTATUS_(TLSTATUS_NOT_COMPATIBLE, "The given data is not compatible.")

#define        TLSTATUS_TOO_MANY_FOUND            TLSTATUS_CODE(0x03F)
case_TLSTATUS_(TLSTATUS_TOO_MANY_FOUND, "Too many objects found.")

#define        TLSTATUS_INVALID_IOCTL             TLSTATUS_CODE(0x040)
case_TLSTATUS_(TLSTATUS_INVALID_IOCTL, "The specified IOCTL code is invalid.")

#define        TLSTATUS_INVALID_PARAMETER         TLSTATUS_CODE(0x041)
case_TLSTATUS_(TLSTATUS_INVALID_PARAMETER, "An invalid parameter was passed to the function.")

#define        TLSTATUS_INVALID_LENGTH            TLSTATUS_CODE(0x042)
case_TLSTATUS_(TLSTATUS_INVALID_LENGTH, "An invalid length was specified.")

#define        TLSTATUS_INVALID_BUFFER_SIZE       TLSTATUS_CODE(0x043)
case_TLSTATUS_(TLSTATUS_INVALID_BUFFER_SIZE, "An invalid buffer size was specified.")

#define        TLSTATUS_INVALID_INBUF_SIZE        TLSTATUS_CODE(0x044)
case_TLSTATUS_(TLSTATUS_INVALID_INBUF_SIZE, "An invalid input buffer size was specified.")

#define        TLSTATUS_INVALID_OUTBUF_SIZE       TLSTATUS_CODE(0x045)
case_TLSTATUS_(TLSTATUS_INVALID_OUTBUF_SIZE, "An invalid output buffer size was specified.")

#define        TLSTATUS_INVALID_TYPE              TLSTATUS_CODE(0x046)
case_TLSTATUS_(TLSTATUS_INVALID_TYPE, "An invalid type was specified.")

#define        TLSTATUS_INVALID_INDEX             TLSTATUS_CODE(0x047)
case_TLSTATUS_(TLSTATUS_INVALID_INDEX, "An invalid index was specified.")

#define        TLSTATUS_INVALID_HANDLE            TLSTATUS_CODE(0x048)
case_TLSTATUS_(TLSTATUS_INVALID_HANDLE, "An invalid handle was specified.")

#define        TLSTATUS_INVALID_DEVICE_STATE      TLSTATUS_CODE(0x049)
case_TLSTATUS_(TLSTATUS_INVALID_DEVICE_STATE, "The operation cannot be performed in the current state of the device.")

#define        TLSTATUS_INVALID_DEVICE_CONFIG     TLSTATUS_CODE(0x04A)
case_TLSTATUS_(TLSTATUS_INVALID_DEVICE_CONFIG, "An invalid device configuration was detected.")

#define        TLSTATUS_INVALID_LICENSE_DATA      TLSTATUS_CODE(0x04B)
case_TLSTATUS_(TLSTATUS_INVALID_LICENSE_DATA, "The license data record is invalid.")


#define        TLSTATUS_INVALID_DESCRIPTOR        TLSTATUS_CODE(0x050)
case_TLSTATUS_(TLSTATUS_INVALID_DESCRIPTOR, "Invalid descriptor.")

#define        TLSTATUS_INVALID_FORMAT            TLSTATUS_CODE(0x051)
case_TLSTATUS_(TLSTATUS_INVALID_FORMAT, "An invalid format was specified.")

#define        TLSTATUS_INVALID_CONFIGURATION     TLSTATUS_CODE(0x052)
case_TLSTATUS_(TLSTATUS_INVALID_CONFIGURATION, "An invalid configuration was specified.")

#define        TLSTATUS_INVALID_MODE              TLSTATUS_CODE(0x053)
case_TLSTATUS_(TLSTATUS_INVALID_MODE, "An invalid mode was specified.")

#define        TLSTATUS_INVALID_COMMAND           TLSTATUS_CODE(0x054)
case_TLSTATUS_(TLSTATUS_INVALID_COMMAND, "An invalid command was specified.")

#define        TLSTATUS_INVALID_FILE              TLSTATUS_CODE(0x055)
case_TLSTATUS_(TLSTATUS_INVALID_FILE, "An invalid file was specified, or the file was not found.")

#define        TLSTATUS_INVALID_EP_INTERVAL       TLSTATUS_CODE(0x056)
case_TLSTATUS_(TLSTATUS_INVALID_EP_INTERVAL, "Invalid endpoint polling interval (not supported by MS).")

#define        TLSTATUS_INVALID_DATA              TLSTATUS_CODE(0x057)
case_TLSTATUS_(TLSTATUS_INVALID_DATA, "Invalid data was given.")

#define        TLSTATUS_INVALID_ADDRESS           TLSTATUS_CODE(0x058)
case_TLSTATUS_(TLSTATUS_INVALID_ADDRESS, "An invalid address was given.")

#define        TLSTATUS_INVALID_SIZE              TLSTATUS_CODE(0x059)
case_TLSTATUS_(TLSTATUS_INVALID_SIZE, "An invalid size was specified.")

#define        TLSTATUS_INVALID_DEVICE_RESPONSE   TLSTATUS_CODE(0x05A)
case_TLSTATUS_(TLSTATUS_INVALID_DEVICE_RESPONSE, "Invalid data was returned by the device.")


#define        TLSTATUS_VERSION_MISMATCH          TLSTATUS_CODE(0x060)
case_TLSTATUS_(TLSTATUS_VERSION_MISMATCH, "The version numbers of software modules do not match.")

#define        TLSTATUS_LENGTH_MISMATCH           TLSTATUS_CODE(0x061)
case_TLSTATUS_(TLSTATUS_LENGTH_MISMATCH, "An unexpected length was detected.")

#define        TLSTATUS_MAGIC_MISMATCH            TLSTATUS_CODE(0x062)
case_TLSTATUS_(TLSTATUS_MAGIC_MISMATCH, "An unexpected marker was detected.")

#define        TLSTATUS_VALUE_UNKNOWN             TLSTATUS_CODE(0x063)
case_TLSTATUS_(TLSTATUS_VALUE_UNKNOWN, "The specified value is unknown.")

#define        TLSTATUS_UNEXPECTED_DEVICE_RESPONSE  TLSTATUS_CODE(0x064)
case_TLSTATUS_(TLSTATUS_UNEXPECTED_DEVICE_RESPONSE, "An unexpected response was received from the device.")

#define        TLSTATUS_USB_STALL                   TLSTATUS_CODE(0x065)
case_TLSTATUS_(TLSTATUS_USB_STALL, "The device returned STALL.")

#define        TLSTATUS_UNEXPECTED_RESULT           TLSTATUS_CODE(0x066)
case_TLSTATUS_(TLSTATUS_UNEXPECTED_RESULT, "A function returned an unexpected result.")

#define        TLSTATUS_OUT_OF_RANGE                TLSTATUS_CODE(0x067)
case_TLSTATUS_(TLSTATUS_OUT_OF_RANGE, "A value is not within the valid range of the underlying type.")

#define        TLSTATUS_TRANSACTION_ERROR           TLSTATUS_CODE(0x068)
case_TLSTATUS_(TLSTATUS_TRANSACTION_ERROR, "A transaction failed.")

#define        TLSTATUS_CHECKSUM_ERROR              TLSTATUS_CODE(0x069)
case_TLSTATUS_(TLSTATUS_CHECKSUM_ERROR, "A checksum error has been detected.")

#define        TLSTATUS_TRANSFER_FAILED              TLSTATUS_CODE(0x06A)
case_TLSTATUS_(TLSTATUS_TRANSFER_FAILED, "The data transfer has failed.")

#define        TLSTATUS_CHECKSUM_MISSING              TLSTATUS_CODE(0x06B)
case_TLSTATUS_(TLSTATUS_CHECKSUM_MISSING, "No checksum present.")


#define        TLSTATUS_ENUM_REQUIRED             TLSTATUS_CODE(0x070)
case_TLSTATUS_(TLSTATUS_ENUM_REQUIRED, "Device enumeration must be performed again.")

#define        TLSTATUS_DEVICE_REMOVED            TLSTATUS_CODE(0x071)
case_TLSTATUS_(TLSTATUS_DEVICE_REMOVED, "The device has been removed from the system.")

#define        TLSTATUS_DEVICES_EXIST             TLSTATUS_CODE(0x072)
case_TLSTATUS_(TLSTATUS_DEVICES_EXIST, "One or more device instances exist in the system.")

#define        TLSTATUS_WRONG_DEVICE_STATE        TLSTATUS_CODE(0x073)
case_TLSTATUS_(TLSTATUS_WRONG_DEVICE_STATE, "The device is not in the expected state.")

#define        TLSTATUS_BUFFER_TOO_SMALL          TLSTATUS_CODE(0x074)
case_TLSTATUS_(TLSTATUS_BUFFER_TOO_SMALL, "The specified buffer is too small.")

#define        TLSTATUS_RESTART_REQUIRED          TLSTATUS_CODE(0x075)
case_TLSTATUS_(TLSTATUS_RESTART_REQUIRED, "A restart is required.")

#define        TLSTATUS_INVALID_CMDLINE           TLSTATUS_CODE(0x076)
case_TLSTATUS_(TLSTATUS_INVALID_CMDLINE, "The command line of the application is invalid.")


#define        TLSTATUS_REGISTRY_ACCESS_FAILED    TLSTATUS_CODE(0x080)
case_TLSTATUS_(TLSTATUS_REGISTRY_ACCESS_FAILED, "Unable to access the system registry.")

#define        TLSTATUS_OBJECT_NOT_FOUND          TLSTATUS_CODE(0x081)
case_TLSTATUS_(TLSTATUS_OBJECT_NOT_FOUND, "The specified object was not found.")

#define        TLSTATUS_NOT_IN_HIGH_SPEED_MODE    TLSTATUS_CODE(0x082)
case_TLSTATUS_(TLSTATUS_NOT_IN_HIGH_SPEED_MODE, "The device is not operating in USB high-speed mode.")

#define        TLSTATUS_WRONG_DIRECTION           TLSTATUS_CODE(0x083)
case_TLSTATUS_(TLSTATUS_WRONG_DIRECTION, "An unexpected direction argument was specified.")

#define        TLSTATUS_PARAMETER_REJECTED        TLSTATUS_CODE(0x084)
case_TLSTATUS_(TLSTATUS_PARAMETER_REJECTED, "A parameter cannot be accepted.")

#define        TLSTATUS_ALREADY_OPEN              TLSTATUS_CODE(0x085)
case_TLSTATUS_(TLSTATUS_ALREADY_OPEN, "The device or object is already opened.")

#define        TLSTATUS_HARDWARE_ACCESS_FAILED    TLSTATUS_CODE(0x086)
case_TLSTATUS_(TLSTATUS_HARDWARE_ACCESS_FAILED, "Unable to access the hardware.")

#define        TLSTATUS_HARDWARE_ERROR            TLSTATUS_CODE(0x087)
case_TLSTATUS_(TLSTATUS_HARDWARE_ERROR, "The hardware reported an error.")

#define        TLSTATUS_OBJECT_EXISTS             TLSTATUS_CODE(0x088)
case_TLSTATUS_(TLSTATUS_OBJECT_EXISTS, "The specified object exists.")

#define        TLSTATUS_OBJECT_FOUND              TLSTATUS_CODE(0x089)
case_TLSTATUS_(TLSTATUS_OBJECT_FOUND, "The specified object has been found.")

#define        TLSTATUS_NO_CLOCK                  TLSTATUS_CODE(0x08A)
case_TLSTATUS_(TLSTATUS_NO_CLOCK, "A required clock is not available.")


#define        TLSTATUS_OPEN_FILE_FAILED          TLSTATUS_CODE(0x090)
case_TLSTATUS_(TLSTATUS_OPEN_FILE_FAILED, "Unable to open the specified file.")

#define        TLSTATUS_READ_FILE_FAILED          TLSTATUS_CODE(0x091)
case_TLSTATUS_(TLSTATUS_READ_FILE_FAILED, "A file read operation failed.")

#define        TLSTATUS_WRITE_FILE_FAILED         TLSTATUS_CODE(0x092)
case_TLSTATUS_(TLSTATUS_WRITE_FILE_FAILED, "A file write operation failed.")

#define        TLSTATUS_IS_READ_ONLY              TLSTATUS_CODE(0x093)
case_TLSTATUS_(TLSTATUS_IS_READ_ONLY, "The operation failed because the object is read-only.")

#define        TLSTATUS_SYSTEM_IS_OWNER           TLSTATUS_CODE(0x094)
case_TLSTATUS_(TLSTATUS_SYSTEM_IS_OWNER, "The operation failed because the object belongs to the system.")

#define        TLSTATUS_PENDING_FILE_DELETION     TLSTATUS_CODE(0x095)
case_TLSTATUS_(TLSTATUS_PENDING_FILE_DELETION, "The operation failed since deletion of files not yet completed.")

#define        TLSTATUS_FILE_NOT_FOUND            TLSTATUS_CODE(0x096)
case_TLSTATUS_(TLSTATUS_FILE_NOT_FOUND, "The specified file was not found.")


#define        TLSTATUS_LICENSE_CHECK_FAILED      TLSTATUS_CODE(0x0A0)
case_TLSTATUS_(TLSTATUS_LICENSE_CHECK_FAILED, "License check failed.")



#define        TLSTATUS_DEFERRED_DATA_STAGE       TLSTATUS_CODE(0x100)
case_TLSTATUS_(TLSTATUS_DEFERRED_DATA_STAGE, "The data stage is to be completed at a later point in time.")

#define        TLSTATUS_FRAME_NUMBER_NOT_RCV      TLSTATUS_CODE(0x101)
case_TLSTATUS_(TLSTATUS_FRAME_NUMBER_NOT_RCV, "The frame number was not received.")

#define        TLSTATUS_EP_DISABLE                TLSTATUS_CODE(0x102)
case_TLSTATUS_(TLSTATUS_EP_DISABLE, "The endpoint has been disabled (because of unconfigure, USB reset, etc.).")

#define        TLSTATUS_EP_RESET                  TLSTATUS_CODE(0x103)
case_TLSTATUS_(TLSTATUS_EP_RESET, "The operation was canceled because a reset pipe request (CLEAR_FEATURE endpoint stall) was received.")

#define        TLSTATUS_EP_CLOSED                 TLSTATUS_CODE(0x104)
case_TLSTATUS_(TLSTATUS_EP_CLOSED, "The operation was canceled because the endpoint was closed.")


#define        TLSTATUS_ADMIN_PRIVILEGES_REQUIRED       TLSTATUS_CODE(0x110)
case_TLSTATUS_(TLSTATUS_ADMIN_PRIVILEGES_REQUIRED, "The operation requires administrator privileges.")

#define        TLSTATUS_NOT_ALLOWED_IN_WOW64            TLSTATUS_CODE(0x111)
case_TLSTATUS_(TLSTATUS_NOT_ALLOWED_IN_WOW64, "The operation is performed in the WOW64 environment which is not allowed.")

#define        TLSTATUS_DOWNGRADE_REJECTED              TLSTATUS_CODE(0x112)
case_TLSTATUS_(TLSTATUS_DOWNGRADE_REJECTED, "The operation was rejected because a downgrade is not allowed.")

#define        TLSTATUS_PROCESS_IS_ELEVATED             TLSTATUS_CODE(0x113)
case_TLSTATUS_(TLSTATUS_PROCESS_IS_ELEVATED, "The process is running with elevated privileges.")


#define        TLSTATUS_I2C_NO_ACK_FOR_ADDRESS          TLSTATUS_CODE(0x120)
case_TLSTATUS_(TLSTATUS_I2C_NO_ACK_FOR_ADDRESS, "No ACK was received for the I2C slave address.")

#define        TLSTATUS_I2C_NO_ACK                      TLSTATUS_CODE(0x121)
case_TLSTATUS_(TLSTATUS_I2C_NO_ACK, "No ACK was received for a data byte sent on I2C.")


#define        TLSTATUS_MEMTEST_WALKING0                TLSTATUS_CODE(0x130)
case_TLSTATUS_(TLSTATUS_MEMTEST_WALKING0, "Memory test failure: walking zero.")

#define        TLSTATUS_MEMTEST_WALKING1                TLSTATUS_CODE(0x131)
case_TLSTATUS_(TLSTATUS_MEMTEST_WALKING1, "Memory test failure: walking one.")

#define        TLSTATUS_MEMTEST_ADDR_WALKING0           TLSTATUS_CODE(0x132)
case_TLSTATUS_(TLSTATUS_MEMTEST_ADDR_WALKING0, "Memory test failure: address line walking zero.")

#define        TLSTATUS_MEMTEST_ADDR_WALKING1           TLSTATUS_CODE(0x133)
case_TLSTATUS_(TLSTATUS_MEMTEST_ADDR_WALKING1, "Memory test failure: address line walking one.")

#define        TLSTATUS_MEMTEST_ADDR_PATTERN            TLSTATUS_CODE(0x134)
case_TLSTATUS_(TLSTATUS_MEMTEST_ADDR_PATTERN, "Memory test failure: address pattern.")

#define        TLSTATUS_MEMTEST_INV_ADDR_PATTERN        TLSTATUS_CODE(0x135)
case_TLSTATUS_(TLSTATUS_MEMTEST_INV_ADDR_PATTERN, "Memory test failure: inverse address pattern.")



#ifdef TLRT_COMPILER_MICROSOFT
//
// Windows USB bus driver status codes, see also usb.h in the WDK
//
// mapped to 0xEF000001 ... 0xEFFFFFFF, see also TLSTATUS_FROM_USBD_STATUS
//
#define        TLSTATUS_USBD_STATUS_CRC                               TLSTATUS_FROM_USBD_STATUS(0xC0000001L)
case_TLSTATUS_(TLSTATUS_USBD_STATUS_CRC, "USB CRC error.")
#define        TLSTATUS_USBD_STATUS_BTSTUFF                           TLSTATUS_FROM_USBD_STATUS(0xC0000002L)
case_TLSTATUS_(TLSTATUS_USBD_STATUS_BTSTUFF, "USB bit stuff error.")
#define        TLSTATUS_USBD_STATUS_DATA_TOGGLE_MISMATCH              TLSTATUS_FROM_USBD_STATUS(0xC0000003L)
case_TLSTATUS_(TLSTATUS_USBD_STATUS_DATA_TOGGLE_MISMATCH, "USB data toggle mismatch.")
#define        TLSTATUS_USBD_STATUS_STALL_PID                         TLSTATUS_FROM_USBD_STATUS(0xC0000004L)
case_TLSTATUS_(TLSTATUS_USBD_STATUS_STALL_PID, "USB stall error.")
#define        TLSTATUS_USBD_STATUS_DEV_NOT_RESPONDING                TLSTATUS_FROM_USBD_STATUS(0xC0000005L)
case_TLSTATUS_(TLSTATUS_USBD_STATUS_DEV_NOT_RESPONDING, "USB device not responding.")
#define        TLSTATUS_USBD_STATUS_PID_CHECK_FAILURE                 TLSTATUS_FROM_USBD_STATUS(0xC0000006L)
case_TLSTATUS_(TLSTATUS_USBD_STATUS_PID_CHECK_FAILURE, "USB PID check failure.")
#define        TLSTATUS_USBD_STATUS_UNEXPECTED_PID                    TLSTATUS_FROM_USBD_STATUS(0xC0000007L)
case_TLSTATUS_(TLSTATUS_USBD_STATUS_UNEXPECTED_PID, "USB PID unexpected.")
#define        TLSTATUS_USBD_STATUS_DATA_OVERRUN                      TLSTATUS_FROM_USBD_STATUS(0xC0000008L)
case_TLSTATUS_(TLSTATUS_USBD_STATUS_DATA_OVERRUN, "USB data overrun.")
#define        TLSTATUS_USBD_STATUS_DATA_UNDERRUN                     TLSTATUS_FROM_USBD_STATUS(0xC0000009L)
case_TLSTATUS_(TLSTATUS_USBD_STATUS_DATA_UNDERRUN, "USB data underrun.")
#define        TLSTATUS_USBD_STATUS_RESERVED1                         TLSTATUS_FROM_USBD_STATUS(0xC000000AL)
case_TLSTATUS_(TLSTATUS_USBD_STATUS_RESERVED1, "USB reserved 1")
#define        TLSTATUS_USBD_STATUS_RESERVED2                         TLSTATUS_FROM_USBD_STATUS(0xC000000BL)
case_TLSTATUS_(TLSTATUS_USBD_STATUS_RESERVED2, "USB reserved 2")
#define        TLSTATUS_USBD_STATUS_BUFFER_OVERRUN                    TLSTATUS_FROM_USBD_STATUS(0xC000000CL)
case_TLSTATUS_(TLSTATUS_USBD_STATUS_BUFFER_OVERRUN, "USB buffer overrun.")
#define        TLSTATUS_USBD_STATUS_BUFFER_UNDERRUN                   TLSTATUS_FROM_USBD_STATUS(0xC000000DL)
case_TLSTATUS_(TLSTATUS_USBD_STATUS_BUFFER_UNDERRUN, "USB buffer underrun.")
#define        TLSTATUS_USBD_STATUS_NOT_ACCESSED                      TLSTATUS_FROM_USBD_STATUS(0xC000000FL)
case_TLSTATUS_(TLSTATUS_USBD_STATUS_NOT_ACCESSED, "USB not accessed error.")
#define        TLSTATUS_USBD_STATUS_FIFO                              TLSTATUS_FROM_USBD_STATUS(0xC0000010L)
case_TLSTATUS_(TLSTATUS_USBD_STATUS_FIFO, "USB FIFO error.")
#define        TLSTATUS_USBD_STATUS_XACT_ERROR                        TLSTATUS_FROM_USBD_STATUS(0xC0000011L)
case_TLSTATUS_(TLSTATUS_USBD_STATUS_XACT_ERROR, "USB XACT error.")
#define        TLSTATUS_USBD_STATUS_BABBLE_DETECTED                   TLSTATUS_FROM_USBD_STATUS(0xC0000012L)
case_TLSTATUS_(TLSTATUS_USBD_STATUS_BABBLE_DETECTED, "USB babble detected.")
#define        TLSTATUS_USBD_STATUS_DATA_BUFFER_ERROR                 TLSTATUS_FROM_USBD_STATUS(0xC0000013L)
case_TLSTATUS_(TLSTATUS_USBD_STATUS_DATA_BUFFER_ERROR, "USB data buffer error.")
#define        TLSTATUS_USBD_STATUS_ENDPOINT_HALTED                   TLSTATUS_FROM_USBD_STATUS(0xC0000030L)
case_TLSTATUS_(TLSTATUS_USBD_STATUS_ENDPOINT_HALTED, "USB endpoint halted.")
#define        TLSTATUS_USBD_STATUS_INVALID_URB_FUNCTION              TLSTATUS_FROM_USBD_STATUS(0x80000200L)
case_TLSTATUS_(TLSTATUS_USBD_STATUS_INVALID_URB_FUNCTION, "USB: invalid URB function.")
#define        TLSTATUS_USBD_STATUS_INVALID_PARAMETER                 TLSTATUS_FROM_USBD_STATUS(0x80000300L)
case_TLSTATUS_(TLSTATUS_USBD_STATUS_INVALID_PARAMETER, "USB: invalid parameter.")
#define        TLSTATUS_USBD_STATUS_ERROR_BUSY                        TLSTATUS_FROM_USBD_STATUS(0x80000400L)
case_TLSTATUS_(TLSTATUS_USBD_STATUS_ERROR_BUSY, "USB: busy error.")
#define        TLSTATUS_USBD_STATUS_INVALID_PIPE_HANDLE               TLSTATUS_FROM_USBD_STATUS(0x80000600L)
case_TLSTATUS_(TLSTATUS_USBD_STATUS_INVALID_PIPE_HANDLE, "USB: invalid pipe handle.")
#define        TLSTATUS_USBD_STATUS_NO_BANDWIDTH                      TLSTATUS_FROM_USBD_STATUS(0x80000700L)
case_TLSTATUS_(TLSTATUS_USBD_STATUS_NO_BANDWIDTH, "USB: no bandwidth.")
#define        TLSTATUS_USBD_STATUS_INTERNAL_HC_ERROR                 TLSTATUS_FROM_USBD_STATUS(0x80000800L)
case_TLSTATUS_(TLSTATUS_USBD_STATUS_INTERNAL_HC_ERROR, "USB: internal HC error.")
#define        TLSTATUS_USBD_STATUS_ERROR_SHORT_TRANSFER              TLSTATUS_FROM_USBD_STATUS(0x80000900L)
case_TLSTATUS_(TLSTATUS_USBD_STATUS_ERROR_SHORT_TRANSFER, "USB short transfer error.")
#define        TLSTATUS_USBD_STATUS_BAD_START_FRAME                   TLSTATUS_FROM_USBD_STATUS(0xC0000A00L)
case_TLSTATUS_(TLSTATUS_USBD_STATUS_BAD_START_FRAME, "USB: bad start frame.")
#define        TLSTATUS_USBD_STATUS_ISOCH_REQUEST_FAILED              TLSTATUS_FROM_USBD_STATUS(0xC0000B00L)
case_TLSTATUS_(TLSTATUS_USBD_STATUS_ISOCH_REQUEST_FAILED, "USB isochronous request failed.")
#define        TLSTATUS_USBD_STATUS_FRAME_CONTROL_OWNED               TLSTATUS_FROM_USBD_STATUS(0xC0000C00L)
case_TLSTATUS_(TLSTATUS_USBD_STATUS_FRAME_CONTROL_OWNED, "USB frame control owned.")
#define        TLSTATUS_USBD_STATUS_FRAME_CONTROL_NOT_OWNED           TLSTATUS_FROM_USBD_STATUS(0xC0000D00L)
case_TLSTATUS_(TLSTATUS_USBD_STATUS_FRAME_CONTROL_NOT_OWNED, "USB frame control owned.")
#define        TLSTATUS_USBD_STATUS_NOT_SUPPORTED                     TLSTATUS_FROM_USBD_STATUS(0xC0000E00L)
case_TLSTATUS_(TLSTATUS_USBD_STATUS_NOT_SUPPORTED, "USB request not supported.")
#define        TLSTATUS_USBD_STATUS_INVALID_CONFIGURATION_DESCRIPTOR  TLSTATUS_FROM_USBD_STATUS(0xC0000F00L)
case_TLSTATUS_(TLSTATUS_USBD_STATUS_INVALID_CONFIGURATION_DESCRIPTOR, "USB configuration descriptor invalid.")
#define        TLSTATUS_USBD_STATUS_INSUFFICIENT_RESOURCES            TLSTATUS_FROM_USBD_STATUS(0xC0001000L)
case_TLSTATUS_(TLSTATUS_USBD_STATUS_INSUFFICIENT_RESOURCES, "USB: insufficient resources.")
#define        TLSTATUS_USBD_STATUS_SET_CONFIG_FAILED                 TLSTATUS_FROM_USBD_STATUS(0xC0002000L)
case_TLSTATUS_(TLSTATUS_USBD_STATUS_SET_CONFIG_FAILED, "USB set configuration failed.")
#define        TLSTATUS_USBD_STATUS_BUFFER_TOO_SMALL                  TLSTATUS_FROM_USBD_STATUS(0xC0003000L)
case_TLSTATUS_(TLSTATUS_USBD_STATUS_BUFFER_TOO_SMALL, "USB buffer too small.")
#define        TLSTATUS_USBD_STATUS_INTERFACE_NOT_FOUND               TLSTATUS_FROM_USBD_STATUS(0xC0004000L)
case_TLSTATUS_(TLSTATUS_USBD_STATUS_INTERFACE_NOT_FOUND, "USB interface not found.")
#define        TLSTATUS_USBD_STATUS_INVALID_PIPE_FLAGS                TLSTATUS_FROM_USBD_STATUS(0xC0005000L)
case_TLSTATUS_(TLSTATUS_USBD_STATUS_INVALID_PIPE_FLAGS, "USB: invalid pipe flags.")
#define        TLSTATUS_USBD_STATUS_TIMEOUT                           TLSTATUS_FROM_USBD_STATUS(0xC0006000L)
case_TLSTATUS_(TLSTATUS_USBD_STATUS_TIMEOUT, "USB request timed out.")
#define        TLSTATUS_USBD_STATUS_DEVICE_GONE                       TLSTATUS_FROM_USBD_STATUS(0xC0007000L)
case_TLSTATUS_(TLSTATUS_USBD_STATUS_DEVICE_GONE, "USB device gone.")
#define        TLSTATUS_USBD_STATUS_STATUS_NOT_MAPPED                 TLSTATUS_FROM_USBD_STATUS(0xC0008000L)
case_TLSTATUS_(TLSTATUS_USBD_STATUS_STATUS_NOT_MAPPED, "USB: not mapped error.")
#define        TLSTATUS_USBD_STATUS_HUB_INTERNAL_ERROR                TLSTATUS_FROM_USBD_STATUS(0xC0009000L)
case_TLSTATUS_(TLSTATUS_USBD_STATUS_HUB_INTERNAL_ERROR, "USB hub internal error.")
#define        TLSTATUS_USBD_STATUS_CANCELED                          TLSTATUS_FROM_USBD_STATUS(0xC0010000L)
case_TLSTATUS_(TLSTATUS_USBD_STATUS_CANCELED, "USB request canceled.")
#define        TLSTATUS_USBD_STATUS_ISO_NOT_ACCESSED_BY_HW            TLSTATUS_FROM_USBD_STATUS(0xC0020000L)
case_TLSTATUS_(TLSTATUS_USBD_STATUS_ISO_NOT_ACCESSED_BY_HW, "USB isochronous request not accessed by hardware.")
#define        TLSTATUS_USBD_STATUS_ISO_TD_ERROR                      TLSTATUS_FROM_USBD_STATUS(0xC0030000L)
case_TLSTATUS_(TLSTATUS_USBD_STATUS_ISO_TD_ERROR, "USB isochronous TD error.")
#define        TLSTATUS_USBD_STATUS_ISO_NA_LATE_USBPORT               TLSTATUS_FROM_USBD_STATUS(0xC0040000L)
case_TLSTATUS_(TLSTATUS_USBD_STATUS_ISO_NA_LATE_USBPORT, "USB isochronous request not accessed late (USB port).")
#define        TLSTATUS_USBD_STATUS_ISO_NOT_ACCESSED_LATE             TLSTATUS_FROM_USBD_STATUS(0xC0050000L)
case_TLSTATUS_(TLSTATUS_USBD_STATUS_ISO_NOT_ACCESSED_LATE, "USB isochronous request not accessed late.")
#define        TLSTATUS_USBD_STATUS_BAD_DESCRIPTOR                    TLSTATUS_FROM_USBD_STATUS(0xC0100000L)
case_TLSTATUS_(TLSTATUS_USBD_STATUS_BAD_DESCRIPTOR, "USB: bad descriptor.")
#define        TLSTATUS_USBD_STATUS_BAD_DESCRIPTOR_BLEN               TLSTATUS_FROM_USBD_STATUS(0xC0100001L)
case_TLSTATUS_(TLSTATUS_USBD_STATUS_BAD_DESCRIPTOR_BLEN, "USB: bad descriptor bLength.")
#define        TLSTATUS_USBD_STATUS_BAD_DESCRIPTOR_TYPE               TLSTATUS_FROM_USBD_STATUS(0xC0100002L)
case_TLSTATUS_(TLSTATUS_USBD_STATUS_BAD_DESCRIPTOR_TYPE, "USB: bad descriptor type.")
#define        TLSTATUS_USBD_STATUS_BAD_INTERFACE_DESCRIPTOR          TLSTATUS_FROM_USBD_STATUS(0xC0100003L)
case_TLSTATUS_(TLSTATUS_USBD_STATUS_BAD_INTERFACE_DESCRIPTOR, "USB: bad interface descriptor.")
#define        TLSTATUS_USBD_STATUS_BAD_ENDPOINT_DESCRIPTOR           TLSTATUS_FROM_USBD_STATUS(0xC0100004L)
case_TLSTATUS_(TLSTATUS_USBD_STATUS_BAD_ENDPOINT_DESCRIPTOR, "USB: bad endpoint descriptor.")
#define        TLSTATUS_USBD_STATUS_BAD_INTERFACE_ASSOC_DESCRIPTOR    TLSTATUS_FROM_USBD_STATUS(0xC0100005L)
case_TLSTATUS_(TLSTATUS_USBD_STATUS_BAD_INTERFACE_ASSOC_DESCRIPTOR, "USB: bad interface association descriptor.")
#define        TLSTATUS_USBD_STATUS_BAD_CONFIG_DESC_LENGTH            TLSTATUS_FROM_USBD_STATUS(0xC0100006L)
case_TLSTATUS_(TLSTATUS_USBD_STATUS_BAD_CONFIG_DESC_LENGTH, "USB: bad config descriptor length.")
#define        TLSTATUS_USBD_STATUS_BAD_NUMBER_OF_INTERFACES          TLSTATUS_FROM_USBD_STATUS(0xC0100007L)
case_TLSTATUS_(TLSTATUS_USBD_STATUS_BAD_NUMBER_OF_INTERFACES, "USB: bad number of interfaces.")
#define        TLSTATUS_USBD_STATUS_BAD_NUMBER_OF_ENDPOINTS           TLSTATUS_FROM_USBD_STATUS(0xC0100008L)
case_TLSTATUS_(TLSTATUS_USBD_STATUS_BAD_NUMBER_OF_ENDPOINTS, "USB: bad number of endpoints.")
#define        TLSTATUS_USBD_STATUS_BAD_ENDPOINT_ADDRESS              TLSTATUS_FROM_USBD_STATUS(0xC0100009L)
case_TLSTATUS_(TLSTATUS_USBD_STATUS_BAD_ENDPOINT_ADDRESS, "USB: bad endpoint address.")



// config manager status codes
//#define        TLSTATUS_CR_SUCCESS                                  TLSTATUS_FROM_CONFIGRET(0x00000000)    // CR_SUCCESS
//case_TLSTATUS_(TLSTATUS_CR_SUCCESS, "CR: success.")
#define        TLSTATUS_CR_DEFAULT                                  TLSTATUS_FROM_CONFIGRET(0x00000001)    // CR_DEFAULT
case_TLSTATUS_(TLSTATUS_CR_DEFAULT, "CR: default.")
#define        TLSTATUS_CR_OUT_OF_MEMORY                            TLSTATUS_FROM_CONFIGRET(0x00000002)    // CR_OUT_OF_MEMORY
case_TLSTATUS_(TLSTATUS_CR_OUT_OF_MEMORY, "CR: out of memory.")
#define        TLSTATUS_CR_INVALID_POINTER                          TLSTATUS_FROM_CONFIGRET(0x00000003)    // CR_INVALID_POINTER
case_TLSTATUS_(TLSTATUS_CR_INVALID_POINTER, "CR: invalid pointer.")
#define        TLSTATUS_CR_INVALID_FLAG                             TLSTATUS_FROM_CONFIGRET(0x00000004)    // CR_INVALID_FLAG
case_TLSTATUS_(TLSTATUS_CR_INVALID_FLAG, "CR: invalid flag.")
#define        TLSTATUS_CR_INVALID_DEVNODE                          TLSTATUS_FROM_CONFIGRET(0x00000005)    // CR_INVALID_DEVNODE
case_TLSTATUS_(TLSTATUS_CR_INVALID_DEVNODE, "CR: invalid device node.")
#define        TLSTATUS_CR_INVALID_RES_DES                          TLSTATUS_FROM_CONFIGRET(0x00000006)    // CR_INVALID_RES_DES
case_TLSTATUS_(TLSTATUS_CR_INVALID_RES_DES, "CR: invalid res des.")
#define        TLSTATUS_CR_INVALID_LOG_CONF                         TLSTATUS_FROM_CONFIGRET(0x00000007)    // CR_INVALID_LOG_CONF
case_TLSTATUS_(TLSTATUS_CR_INVALID_LOG_CONF, "CR: invalid log conf.")
#define        TLSTATUS_CR_INVALID_ARBITRATOR                       TLSTATUS_FROM_CONFIGRET(0x00000008)    // CR_INVALID_ARBITRATOR
case_TLSTATUS_(TLSTATUS_CR_INVALID_ARBITRATOR, "CR: invalid arbitrator.")
#define        TLSTATUS_CR_INVALID_NODELIST                         TLSTATUS_FROM_CONFIGRET(0x00000009)    // CR_INVALID_NODELIST
case_TLSTATUS_(TLSTATUS_CR_INVALID_NODELIST, "CR: invalid nodelist.")
#define        TLSTATUS_CR_DEVNODE_HAS_REQS                         TLSTATUS_FROM_CONFIGRET(0x0000000A)    // CR_DEVNODE_HAS_REQS
case_TLSTATUS_(TLSTATUS_CR_DEVNODE_HAS_REQS, "CR: device has reqs.")
#define        TLSTATUS_CR_INVALID_RESOURCEID                       TLSTATUS_FROM_CONFIGRET(0x0000000B)    // CR_INVALID_RESOURCEID
case_TLSTATUS_(TLSTATUS_CR_INVALID_RESOURCEID, "CR: invalid resource ID.")
#define        TLSTATUS_CR_DLVXD_NOT_FOUND                          TLSTATUS_FROM_CONFIGRET(0x0000000C)    // CR_DLVXD_NOT_FOUND
case_TLSTATUS_(TLSTATUS_CR_DLVXD_NOT_FOUND, "CR: DLVXD not found.")
#define        TLSTATUS_CR_NO_SUCH_DEVNODE                          TLSTATUS_FROM_CONFIGRET(0x0000000D)    // CR_NO_SUCH_DEVNODE
case_TLSTATUS_(TLSTATUS_CR_NO_SUCH_DEVNODE, "CR: no such devnode.")
#define        TLSTATUS_CR_NO_MORE_LOG_CONF                         TLSTATUS_FROM_CONFIGRET(0x0000000E)    // CR_NO_MORE_LOG_CONF
case_TLSTATUS_(TLSTATUS_CR_NO_MORE_LOG_CONF, "CR: no more log conf.")
#define        TLSTATUS_CR_NO_MORE_RES_DES                          TLSTATUS_FROM_CONFIGRET(0x0000000F)    // CR_NO_MORE_RES_DES
case_TLSTATUS_(TLSTATUS_CR_NO_MORE_RES_DES, "CR: no more des res.")
#define        TLSTATUS_CR_ALREADY_SUCH_DEVNODE                     TLSTATUS_FROM_CONFIGRET(0x00000010)    // CR_ALREADY_SUCH_DEVNODE
case_TLSTATUS_(TLSTATUS_CR_ALREADY_SUCH_DEVNODE, "CR: already such devnode.")
#define        TLSTATUS_CR_INVALID_RANGE_LIST                       TLSTATUS_FROM_CONFIGRET(0x00000011)    // CR_INVALID_RANGE_LIST
case_TLSTATUS_(TLSTATUS_CR_INVALID_RANGE_LIST, "CR: invalid range list.")
#define        TLSTATUS_CR_INVALID_RANGE                            TLSTATUS_FROM_CONFIGRET(0x00000012)    // CR_INVALID_RANGE
case_TLSTATUS_(TLSTATUS_CR_INVALID_RANGE, "CR: invalid range.")
#define        TLSTATUS_CR_FAILURE                                  TLSTATUS_FROM_CONFIGRET(0x00000013)    // CR_FAILURE
case_TLSTATUS_(TLSTATUS_CR_FAILURE, "CR: failure.")
#define        TLSTATUS_CR_NO_SUCH_LOGICAL_DEV                      TLSTATUS_FROM_CONFIGRET(0x00000014)    // CR_NO_SUCH_LOGICAL_DEV
case_TLSTATUS_(TLSTATUS_CR_NO_SUCH_LOGICAL_DEV, "CR: no such logical device.")
#define        TLSTATUS_CR_CREATE_BLOCKED                           TLSTATUS_FROM_CONFIGRET(0x00000015)    // CR_CREATE_BLOCKED
case_TLSTATUS_(TLSTATUS_CR_CREATE_BLOCKED, "CR: create blocked.")
#define        TLSTATUS_CR_NOT_SYSTEM_VM                            TLSTATUS_FROM_CONFIGRET(0x00000016)    // CR_NOT_SYSTEM_VM
case_TLSTATUS_(TLSTATUS_CR_NOT_SYSTEM_VM, "CR: not system VM.")
#define        TLSTATUS_CR_REMOVE_VETOED                            TLSTATUS_FROM_CONFIGRET(0x00000017)    // CR_REMOVE_VETOED
case_TLSTATUS_(TLSTATUS_CR_REMOVE_VETOED, "CR: remove vetoed.")
#define        TLSTATUS_CR_APM_VETOED                               TLSTATUS_FROM_CONFIGRET(0x00000018)    // CR_APM_VETOED
case_TLSTATUS_(TLSTATUS_CR_APM_VETOED, "CR: APM vetoed.")
#define        TLSTATUS_CR_INVALID_LOAD_TYPE                        TLSTATUS_FROM_CONFIGRET(0x00000019)    // CR_INVALID_LOAD_TYPE
case_TLSTATUS_(TLSTATUS_CR_INVALID_LOAD_TYPE, "CR: invalid load type.")
#define        TLSTATUS_CR_BUFFER_SMALL                             TLSTATUS_FROM_CONFIGRET(0x0000001A)    // CR_BUFFER_SMALL
case_TLSTATUS_(TLSTATUS_CR_BUFFER_SMALL, "CR: buffer too small.")
#define        TLSTATUS_CR_NO_ARBITRATOR                            TLSTATUS_FROM_CONFIGRET(0x0000001B)    // CR_NO_ARBITRATOR
case_TLSTATUS_(TLSTATUS_CR_NO_ARBITRATOR, "CR: no arbitrator.")
#define        TLSTATUS_CR_NO_REGISTRY_HANDLE                       TLSTATUS_FROM_CONFIGRET(0x0000001C)    // CR_NO_REGISTRY_HANDLE
case_TLSTATUS_(TLSTATUS_CR_NO_REGISTRY_HANDLE, "CR: no registry handle.")
#define        TLSTATUS_CR_REGISTRY_ERROR                           TLSTATUS_FROM_CONFIGRET(0x0000001D)    // CR_REGISTRY_ERROR
case_TLSTATUS_(TLSTATUS_CR_REGISTRY_ERROR, "CR: registry error.")
#define        TLSTATUS_CR_INVALID_DEVICE_ID                        TLSTATUS_FROM_CONFIGRET(0x0000001E)    // CR_INVALID_DEVICE_ID
case_TLSTATUS_(TLSTATUS_CR_INVALID_DEVICE_ID, "CR: invalid device ID.")
#define        TLSTATUS_CR_INVALID_DATA                             TLSTATUS_FROM_CONFIGRET(0x0000001F)    // CR_INVALID_DATA
case_TLSTATUS_(TLSTATUS_CR_INVALID_DATA, "CR: invalid data.")
#define        TLSTATUS_CR_INVALID_API                              TLSTATUS_FROM_CONFIGRET(0x00000020)    // CR_INVALID_API
case_TLSTATUS_(TLSTATUS_CR_INVALID_API, "CR: invalid API.")
#define        TLSTATUS_CR_DEVLOADER_NOT_READY                      TLSTATUS_FROM_CONFIGRET(0x00000021)    // CR_DEVLOADER_NOT_READY
case_TLSTATUS_(TLSTATUS_CR_DEVLOADER_NOT_READY, "CR: devloader not ready.")
#define        TLSTATUS_CR_NEED_RESTART                             TLSTATUS_FROM_CONFIGRET(0x00000022)    // CR_NEED_RESTART
case_TLSTATUS_(TLSTATUS_CR_NEED_RESTART, "CR: need restart.")
#define        TLSTATUS_CR_NO_MORE_HW_PROFILES                      TLSTATUS_FROM_CONFIGRET(0x00000023)    // CR_NO_MORE_HW_PROFILES
case_TLSTATUS_(TLSTATUS_CR_NO_MORE_HW_PROFILES, "CR: no more hardware profiles.")
#define        TLSTATUS_CR_DEVICE_NOT_THERE                         TLSTATUS_FROM_CONFIGRET(0x00000024)    // CR_DEVICE_NOT_THERE
case_TLSTATUS_(TLSTATUS_CR_DEVICE_NOT_THERE, "CR: device not there.")
#define        TLSTATUS_CR_NO_SUCH_VALUE                            TLSTATUS_FROM_CONFIGRET(0x00000025)    // CR_NO_SUCH_VALUE
case_TLSTATUS_(TLSTATUS_CR_NO_SUCH_VALUE, "CR: no such value.")
#define        TLSTATUS_CR_WRONG_TYPE                               TLSTATUS_FROM_CONFIGRET(0x00000026)    // CR_WRONG_TYPE
case_TLSTATUS_(TLSTATUS_CR_WRONG_TYPE, "CR: wrong type.")
#define        TLSTATUS_CR_INVALID_PRIORITY                         TLSTATUS_FROM_CONFIGRET(0x00000027)    // CR_INVALID_PRIORITY
case_TLSTATUS_(TLSTATUS_CR_INVALID_PRIORITY, "CR: invalid priority.")
#define        TLSTATUS_CR_NOT_DISABLEABLE                          TLSTATUS_FROM_CONFIGRET(0x00000028)    // CR_NOT_DISABLEABLE
case_TLSTATUS_(TLSTATUS_CR_NOT_DISABLEABLE, "CR: not disableable.")
#define        TLSTATUS_CR_FREE_RESOURCES                           TLSTATUS_FROM_CONFIGRET(0x00000029)    // CR_FREE_RESOURCES
case_TLSTATUS_(TLSTATUS_CR_FREE_RESOURCES, "CR: free resources.")
#define        TLSTATUS_CR_QUERY_VETOED                             TLSTATUS_FROM_CONFIGRET(0x0000002A)    // CR_QUERY_VETOED
case_TLSTATUS_(TLSTATUS_CR_QUERY_VETOED, "CR: query vetoed.")
#define        TLSTATUS_CR_CANT_SHARE_IRQ                           TLSTATUS_FROM_CONFIGRET(0x0000002B)    // CR_CANT_SHARE_IRQ
case_TLSTATUS_(TLSTATUS_CR_CANT_SHARE_IRQ, "CR: can not share IRQ.")
#define        TLSTATUS_CR_NO_DEPENDENT                             TLSTATUS_FROM_CONFIGRET(0x0000002C)    // CR_NO_DEPENDENT
case_TLSTATUS_(TLSTATUS_CR_NO_DEPENDENT, "CR: no dependent.")
#define        TLSTATUS_CR_SAME_RESOURCES                           TLSTATUS_FROM_CONFIGRET(0x0000002D)    // CR_SAME_RESOURCES
case_TLSTATUS_(TLSTATUS_CR_SAME_RESOURCES, "CR: same resources.")
#define        TLSTATUS_CR_NO_SUCH_REGISTRY_KEY                     TLSTATUS_FROM_CONFIGRET(0x0000002E)    // CR_NO_SUCH_REGISTRY_KEY
case_TLSTATUS_(TLSTATUS_CR_NO_SUCH_REGISTRY_KEY, "CR: no such registry key.")
#define        TLSTATUS_CR_INVALID_MACHINENAME                      TLSTATUS_FROM_CONFIGRET(0x0000002F)    // CR_INVALID_MACHINENAME
case_TLSTATUS_(TLSTATUS_CR_INVALID_MACHINENAME, "CR: invalid machine name.")
#define        TLSTATUS_CR_REMOTE_COMM_FAILURE                      TLSTATUS_FROM_CONFIGRET(0x00000030)    // CR_REMOTE_COMM_FAILURE
case_TLSTATUS_(TLSTATUS_CR_REMOTE_COMM_FAILURE, "CR: remote communication failure.")
#define        TLSTATUS_CR_MACHINE_UNAVAILABLE                      TLSTATUS_FROM_CONFIGRET(0x00000031)    // CR_MACHINE_UNAVAILABLE
case_TLSTATUS_(TLSTATUS_CR_MACHINE_UNAVAILABLE, "CR: machine unavailable.")
#define        TLSTATUS_CR_NO_CM_SERVICES                           TLSTATUS_FROM_CONFIGRET(0x00000032)    // CR_NO_CM_SERVICES
case_TLSTATUS_(TLSTATUS_CR_NO_CM_SERVICES, "CR: no config manager services.")
#define        TLSTATUS_CR_ACCESS_DENIED                            TLSTATUS_FROM_CONFIGRET(0x00000033)    // CR_ACCESS_DENIED
case_TLSTATUS_(TLSTATUS_CR_ACCESS_DENIED, "CR: access denied.")
#define        TLSTATUS_CR_CALL_NOT_IMPLEMENTED                     TLSTATUS_FROM_CONFIGRET(0x00000034)    // CR_CALL_NOT_IMPLEMENTED
case_TLSTATUS_(TLSTATUS_CR_CALL_NOT_IMPLEMENTED, "CR: call not implemented.")
#define        TLSTATUS_CR_INVALID_PROPERTY                         TLSTATUS_FROM_CONFIGRET(0x00000035)    // CR_INVALID_PROPERTY
case_TLSTATUS_(TLSTATUS_CR_INVALID_PROPERTY, "CR: invalid property.")
#define        TLSTATUS_CR_DEVICE_INTERFACE_ACTIVE                  TLSTATUS_FROM_CONFIGRET(0x00000036)    // CR_DEVICE_INTERFACE_ACTIVE
case_TLSTATUS_(TLSTATUS_CR_DEVICE_INTERFACE_ACTIVE, "CR: device interface active.")
#define        TLSTATUS_CR_NO_SUCH_DEVICE_INTERFACE                 TLSTATUS_FROM_CONFIGRET(0x00000037)    // CR_NO_SUCH_DEVICE_INTERFACE
case_TLSTATUS_(TLSTATUS_CR_NO_SUCH_DEVICE_INTERFACE, "CR: no such device interface.")
#define        TLSTATUS_CR_INVALID_REFERENCE_STRING                 TLSTATUS_FROM_CONFIGRET(0x00000038)    // CR_INVALID_REFERENCE_STRING
case_TLSTATUS_(TLSTATUS_CR_INVALID_REFERENCE_STRING, "CR: invalid reference string.")
#define        TLSTATUS_CR_INVALID_CONFLICT_LIST                    TLSTATUS_FROM_CONFIGRET(0x00000039)    // CR_INVALID_CONFLICT_LIST
case_TLSTATUS_(TLSTATUS_CR_INVALID_CONFLICT_LIST, "CR: invalid conflict list.")
#define        TLSTATUS_CR_INVALID_INDEX                            TLSTATUS_FROM_CONFIGRET(0x0000003A)    // CR_INVALID_INDEX
case_TLSTATUS_(TLSTATUS_CR_INVALID_INDEX, "CR: invalid index.")
#define        TLSTATUS_CR_INVALID_STRUCTURE_SIZE                   TLSTATUS_FROM_CONFIGRET(0x0000003b)    // CR_INVALID_STRUCTURE_SIZE
case_TLSTATUS_(TLSTATUS_CR_INVALID_STRUCTURE_SIZE, "CR: invalid structure size.")
#define        TLSTATUS_NUM_CR_RESULTS                              TLSTATUS_FROM_CONFIGRET(0x0000003C)    // NUM_CR_RESULTS
case_TLSTATUS_(TLSTATUS_NUM_CR_RESULTS, "CR: num results.")

#endif


#if defined(TLRT_COMPILER_GNU_LINUX) || defined(TLRT_COMPILER_APPLE_USER_MODE)
// the error numbers from Linux
#include <errno.h>
#define        TLSTATUS_LINUX_EPERM              TLSTATUS_FROM_ERRNO(EPERM)
case_TLSTATUS_(TLSTATUS_LINUX_EPERM, "Operation not permitted.")
#define        TLSTATUS_LINUX_ENOENT              TLSTATUS_FROM_ERRNO(ENOENT)
case_TLSTATUS_(TLSTATUS_LINUX_ENOENT, "No such file or directory.")
#define        TLSTATUS_LINUX_ESRCH              TLSTATUS_FROM_ERRNO(ESRCH)
case_TLSTATUS_(TLSTATUS_LINUX_ESRCH, "No such process.")
#define        TLSTATUS_LINUX_EINTR             TLSTATUS_FROM_ERRNO(EINTR)
case_TLSTATUS_(TLSTATUS_LINUX_EINTR, "Interrupted system call.")
#define        TLSTATUS_LINUX_EIO             TLSTATUS_FROM_ERRNO(EIO)
case_TLSTATUS_(TLSTATUS_LINUX_EIO, "I/O error.")
#define        TLSTATUS_LINUX_ENXIO              TLSTATUS_FROM_ERRNO(ENXIO)
case_TLSTATUS_(TLSTATUS_LINUX_ENXIO, "No such device or address.")
#define        TLSTATUS_LINUX_E2BIG              TLSTATUS_FROM_ERRNO(E2BIG)
case_TLSTATUS_(TLSTATUS_LINUX_E2BIG, "Argument list too long.")
#define        TLSTATUS_LINUX_ENOEXEC              TLSTATUS_FROM_ERRNO(ENOEXEC)
case_TLSTATUS_(TLSTATUS_LINUX_ENOEXEC, "Exec format error.")
#define        TLSTATUS_LINUX_EBADF              TLSTATUS_FROM_ERRNO(EBADF)
case_TLSTATUS_(TLSTATUS_LINUX_EBADF, "Bad file number.")
#define        TLSTATUS_LINUX_ECHILD              TLSTATUS_FROM_ERRNO(ECHILD)
case_TLSTATUS_(TLSTATUS_LINUX_ECHILD, "No child processes.")
#define        TLSTATUS_LINUX_EAGAIN              TLSTATUS_FROM_ERRNO(EAGAIN)
case_TLSTATUS_(TLSTATUS_LINUX_EAGAIN, "Try again.")
#define        TLSTATUS_LINUX_ENOMEM              TLSTATUS_FROM_ERRNO(ENOMEM)
case_TLSTATUS_(TLSTATUS_LINUX_ENOMEM, "Out of memory.")
#define        TLSTATUS_LINUX_EACCES              TLSTATUS_FROM_ERRNO(EACCES)
case_TLSTATUS_(TLSTATUS_LINUX_EACCES, "Permission denied.")
#define        TLSTATUS_LINUX_EFAULT              TLSTATUS_FROM_ERRNO(EFAULT)
case_TLSTATUS_(TLSTATUS_LINUX_EFAULT, "Bad address.")
#define        TLSTATUS_LINUX_ENOTBLK              TLSTATUS_FROM_ERRNO(ENOTBLK)
case_TLSTATUS_(TLSTATUS_LINUX_ENOTBLK, "Block device required.")
#define        TLSTATUS_LINUX_EBUSY              TLSTATUS_FROM_ERRNO(EBUSY)
case_TLSTATUS_(TLSTATUS_LINUX_EBUSY, "Device or resource busy.")
#define        TLSTATUS_LINUX_EEXIST              TLSTATUS_FROM_ERRNO(EEXIST)
case_TLSTATUS_(TLSTATUS_LINUX_EEXIST, "File exists.")
#define        TLSTATUS_LINUX_EXDEV              TLSTATUS_FROM_ERRNO(EXDEV)
case_TLSTATUS_(TLSTATUS_LINUX_EXDEV, "Cross-device link.")
#define        TLSTATUS_LINUX_ENODEV              TLSTATUS_FROM_ERRNO(ENODEV)
case_TLSTATUS_(TLSTATUS_LINUX_ENODEV, "No such device.")
#define        TLSTATUS_LINUX_ENOTDIR              TLSTATUS_FROM_ERRNO(ENOTDIR)
case_TLSTATUS_(TLSTATUS_LINUX_ENOTDIR, "Not a directory.")
#define        TLSTATUS_LINUX_EISDIR              TLSTATUS_FROM_ERRNO(EISDIR)
case_TLSTATUS_(TLSTATUS_LINUX_EISDIR, "Is a directory.")
#define        TLSTATUS_LINUX_EINVAL              TLSTATUS_FROM_ERRNO(EINVAL)
case_TLSTATUS_(TLSTATUS_LINUX_EINVAL, "Invalid argument.")
#define        TLSTATUS_LINUX_ENFILE              TLSTATUS_FROM_ERRNO(ENFILE)
case_TLSTATUS_(TLSTATUS_LINUX_ENFILE, "File table overflow.")
#define        TLSTATUS_LINUX_EMFILE              TLSTATUS_FROM_ERRNO(EMFILE)
case_TLSTATUS_(TLSTATUS_LINUX_EMFILE, "Too many open files.")
#define        TLSTATUS_LINUX_ENOTTY              TLSTATUS_FROM_ERRNO(ENOTTY)
case_TLSTATUS_(TLSTATUS_LINUX_ENOTTY, "Not a typewriter.")
#define        TLSTATUS_LINUX_ETXTBSY              TLSTATUS_FROM_ERRNO(ETXTBSY)
case_TLSTATUS_(TLSTATUS_LINUX_ETXTBSY, "Text file busy.")
#define        TLSTATUS_LINUX_EFBIG              TLSTATUS_FROM_ERRNO(EFBIG)
case_TLSTATUS_(TLSTATUS_LINUX_EFBIG, "File too large.")
#define        TLSTATUS_LINUX_ENOSPC              TLSTATUS_FROM_ERRNO(ENOSPC)
case_TLSTATUS_(TLSTATUS_LINUX_ENOSPC, "No space left on device.")
#define        TLSTATUS_LINUX_ESPIPE              TLSTATUS_FROM_ERRNO(ESPIPE)
case_TLSTATUS_(TLSTATUS_LINUX_ESPIPE, "Illegal seek.")
#define        TLSTATUS_LINUX_EROFS              TLSTATUS_FROM_ERRNO(EROFS)
case_TLSTATUS_(TLSTATUS_LINUX_EROFS, "Read-only file system.")
#define        TLSTATUS_LINUX_EMLINK              TLSTATUS_FROM_ERRNO(EMLINK)
case_TLSTATUS_(TLSTATUS_LINUX_EMLINK, "Too many links.")
#define        TLSTATUS_LINUX_EPIPE              TLSTATUS_FROM_ERRNO(EPIPE)
case_TLSTATUS_(TLSTATUS_LINUX_EPIPE, "Broken pipe.")
#define        TLSTATUS_LINUX_EDOM              TLSTATUS_FROM_ERRNO(EDOM)
case_TLSTATUS_(TLSTATUS_LINUX_EDOM, "Math argument out of domain of func.")
#define        TLSTATUS_LINUX_ERANGE              TLSTATUS_FROM_ERRNO(ERANGE)
case_TLSTATUS_(TLSTATUS_LINUX_ERANGE, "Math result not representable.")

#endif


// project-specific status codes
#include "tlstatus_codes_ex.h"


#if !defined(__tlstatus_codes_str_impl__)
#undef case_TLSTATUS_
#endif


#endif  //#ifndef RC_INVOKED

#undef __in_tlstatus_codes_h__
#endif

/*** EOF ***/
