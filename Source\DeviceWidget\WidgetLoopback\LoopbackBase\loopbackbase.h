#ifndef LOOPBACKBASE_H
#define LOOPBACKBASE_H


#include <QWidget>


class LoopbackBase : public QWidget
{
    Q_OBJECT
public:
    explicit LoopbackBase(QWidget* parent=nullptr) : QWidget(parent) { }
    virtual ~LoopbackBase() = default;
    LoopbackBase& setChannelName(QString name);
    LoopbackBase& setWidgetEnable(bool state=true);
    LoopbackBase& setWidgetEnableWithUpdate(bool state=true);
    LoopbackBase& setWidgetMovable(bool state=true);
    LoopbackBase& setWidgetReady(bool state=true);
    LoopbackBase& setWidgetEmitAction(bool state=true);
    QString getChannelName() { return mChannelName; }
    bool isWidgetEnable() { return mEnable; }
    bool isWidgetMovable() { return mMovable; }
    bool isWidgetReady() { return mReady; }
    bool isWidgetEmitAction() { return mEmitAction; }
protected:
    virtual void updateAttribute() = 0;
private:
    QString mChannelName="";
    bool mEnable=false;
    bool mMovable=false;
    bool mReady=false;
    bool mEmitAction=false;
signals:
    void attributeChanged(QString objectName, QString attribute, QString value);
};


#endif // LOOPBACKBASE_H

