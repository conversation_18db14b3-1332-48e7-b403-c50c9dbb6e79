#include "globalfont.h"
#include "messageboxwidget3.h"
#include "ui_messageboxwidget3.h"


MessageBoxWidget3::MessageBoxWidget3(QWidget *parent)
    : QWidget(parent)
    , ui(new Ui::MessageBoxWidget3)
{
    ui->setupUi(this);
    QString style;
    style = "QCheckBox {"
            "    color: rgb(255, 255, 255);"
            "    spacing: 15px;"
            "}"
            "QCheckBox::indicator {"
            "    width: 20px;"
            "    height: 20px;"
            "}"
            "QCheckBox::indicator:unchecked {"
            "    image: url(:/Icon/CheckBox_Uncheck.png);"
            "}"
            "QCheckBox::indicator:checked {"
            "    image: url(:/Icon/CheckBox_Checked.png);"
            "}";
    ui->CheckBox1->setStyleSheet(style);
    ui->CheckBox2->setStyleSheet(style);
    ui->CheckBox3->setStyleSheet(style);
    style = "QPushButton {"
            "   background-color: rgb(48, 48, 48);"
            "   color: rgb(255, 255, 255);"
            "   border: none;"
            "   border-radius: 8px;"
            "}";
    ui->PushButton1->setStyleSheet(style);
    style = "QPushButton {"
            "   background-color: rgb(1, 150, 65);"
            "   color: rgb(255, 255, 255);"
            "   border: none;"
            "   border-radius: 8px;"
            "}";
    ui->PushButton2->setStyleSheet(style);
    ui->CheckBox1->setCheckState(Qt::Checked);
    setLanguage("English");
}
MessageBoxWidget3::~MessageBoxWidget3()
{
    delete ui;
}


// override
void MessageBoxWidget3::resizeEvent(QResizeEvent* e)
{
    Q_UNUSED(e);
    QRect rect=ui->CheckBox1->rect();
    rect.setWidth(rect.width() - ui->CheckBox1->height() * 1.3);
    mFont.setPointSize(GLBFHandle.getSuitablePointSize(mFont, ui->CheckBox1->text(), rect));
    ui->CheckBox1->setFont(mFont);
    ui->CheckBox2->setFont(mFont);
    ui->CheckBox3->setFont(mFont);
    ui->PushButton1->setFont(mFont);
    ui->PushButton2->setFont(mFont);
    QString style;
    style = QString("QCheckBox {"
                    "    color: rgb(255, 255, 255);"
                    "    spacing: %1px;"
                    "}"
                    "QCheckBox::indicator {"
                    "    width: %2px;"
                    "    height: %2px;"
                    "}"
                    "QCheckBox::indicator:unchecked {"
                    "    image: url(:/Icon/CheckBox_Uncheck.png);"
                    "}"
                    "QCheckBox::indicator:checked {"
                    "    image: url(:/Icon/CheckBox_Checked.png);"
                    "}").arg(ui->CheckBox1->height() * 0.3).arg(ui->CheckBox1->height());
    ui->CheckBox1->setStyleSheet(style);
    ui->CheckBox2->setStyleSheet(style);
    ui->CheckBox3->setStyleSheet(style);
}


// slot
void MessageBoxWidget3::on_CheckBox1_checkStateChanged(const Qt::CheckState &arg1)
{
    if(arg1 == Qt::Unchecked) return;
    ui->CheckBox1->setEnabled(false);
    ui->CheckBox2->setEnabled(true);
    ui->CheckBox3->setEnabled(true);
    ui->CheckBox2->setCheckState(Qt::Unchecked);
    ui->CheckBox3->setCheckState(Qt::Unchecked);
    mSelectedItem = "Item1";
}
void MessageBoxWidget3::on_CheckBox2_checkStateChanged(const Qt::CheckState &arg1)
{
    if(arg1 == Qt::Unchecked) return;
    ui->CheckBox1->setEnabled(true);
    ui->CheckBox2->setEnabled(false);
    ui->CheckBox3->setEnabled(true);
    ui->CheckBox1->setCheckState(Qt::Unchecked);
    ui->CheckBox3->setCheckState(Qt::Unchecked);
    mSelectedItem = "Item2";
}
void MessageBoxWidget3::on_CheckBox3_checkStateChanged(const Qt::CheckState &arg1)
{
    if(arg1 == Qt::Unchecked) return;
    ui->CheckBox1->setEnabled(true);
    ui->CheckBox2->setEnabled(true);
    ui->CheckBox3->setEnabled(false);
    ui->CheckBox1->setCheckState(Qt::Unchecked);
    ui->CheckBox2->setCheckState(Qt::Unchecked);
    mSelectedItem = "Item3";
}
void MessageBoxWidget3::on_PushButton1_clicked()
{
    emit attributeChanged("", "Cancel", mSelectedItem);
}
void MessageBoxWidget3::on_PushButton2_clicked()
{
    emit attributeChanged("", "Ok", mSelectedItem);
}


// setter & getter
MessageBoxWidget3& MessageBoxWidget3::setFont(QFont font)
{
    mFont = font;
    QResizeEvent e(size(), size());
    resizeEvent(&e);
    update();
    return *this;
}
MessageBoxWidget3& MessageBoxWidget3::setLanguage(QString language)
{
    if(language == "English")
    {
        ui->CheckBox1->setText("Save current workspace and download to device");
        ui->CheckBox2->setText("Save as new workspace and download to device");
        ui->CheckBox3->setText("Download to device only");
        ui->PushButton1->setText("Cancel");
        ui->PushButton2->setText("Ok");
    }
    else if(language == "Chinese")
    {
        ui->CheckBox1->setText("保存当前工作区并下载到设备");
        ui->CheckBox2->setText("另存为新工作区并下载到设备");
        ui->CheckBox3->setText("仅下载到设备");
        ui->PushButton1->setText("取消");
        ui->PushButton2->setText("确定");
    }
    return *this;
}

