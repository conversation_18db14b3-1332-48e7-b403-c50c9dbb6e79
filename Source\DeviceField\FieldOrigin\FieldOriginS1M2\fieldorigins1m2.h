#ifndef FIELDORIGINS1M2_H
#define FIELDORIGINS1M2_H


#include <QWidget>
#include <QVector>
#include <QString>

#include "workspace.h"
#include "originbase.h"
#include "appsettings.h"
#include "fieldoriginbase3.h"


class FieldOriginS1M2 : public FieldOriginBase3, public WorkspaceObserver, public AppSettingsObserver
{
    Q_OBJECT
public:
    explicit FieldOriginS1M2(QWidget* parent=nullptr, QString name="");
    ~FieldOriginS1M2();
    FieldOriginS1M2& setName(QString name);
    FieldOriginS1M2& registerLanguage(QHash<QString, QHash<QString, QString>>& language);
    FieldOriginS1M2& modifyWidgetList(QVector<OriginBase*> list);
    FieldOriginS1M2& setVisibleListDefault(QVector<OriginBase*> list);
protected:
    void loadSettings() override;
    void AppSettingsChanged(QString objectName, QString attribute, QString value) override;
private:
    QHash<QString, QHash<QString, QString>> mLanguageContainer;
    QVector<OriginBase*> mWidgetList;
    QVariantList mVisibleListDefault;
private slots:
    void in_widgetBase_attributeChanged(QString objectName, QString attribute, QString value);
    void in_widgetList_attributeChanged(QString objectName, QString attribute, QString value);
signals:
    void attributeChanged(QString objectName, QString attribute, QString value);
};


#endif // FIELDORIGINS1M2_H

