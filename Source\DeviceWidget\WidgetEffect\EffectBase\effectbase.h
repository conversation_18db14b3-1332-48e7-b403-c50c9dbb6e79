#ifndef EFFECTBASE_H
#define EFFECTBASE_H


#include <QWidget>


class EffectBase : public QWidget
{
    Q_OBJECT
public:
    explicit EffectBase(QWidget* parent=nullptr) : QWidget(parent) { }
    virtual ~EffectBase() = default;
    EffectBase& setChannelName(QString name);
    EffectBase& setWidgetEnable(bool state=true);
    EffectBase& setWidgetEnableWithUpdate(bool state=true);
    EffectBase& setWidgetMovable(bool state=true);
    EffectBase& setWidgetReady(bool state=true);
    EffectBase& setWidgetEmitAction(bool state=true);
    QString getChannelName() { return mChannelName; }
    bool isWidgetEnable() { return mEnable; }
    bool isWidgetMovable() { return mMovable; }
    bool isWidgetReady() { return mReady; }
    bool isWidgetEmitAction() { return mEmitAction; }
protected:
    virtual void updateAttribute() = 0;
private:
    QString mChannelName="";
    bool mEnable=false;
    bool mMovable=false;
    bool mReady=false;
    bool mEmitAction=false;
signals:
    void attributeChanged(QString objectName, QString attribute, QString value);
};


#endif // EFFECTBASE_H

