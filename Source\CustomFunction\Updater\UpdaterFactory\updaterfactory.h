#ifndef UPDATERFACTORY_H
#define UPDATERFACTORY_H

#include "updatersoftware.h"
#include "updaterfirmwarem1.h"

#define UPDATER_INSTANCE(type) UpdaterFactory::getInstance(UpdaterFactory::UpdaterType::type)

class UpdaterFactory {
public:
    enum class UpdaterType {
        Software,
        Firmware
    };
    enum class UpdaterMethod {
        Method1,
        Method2
    };

    static void setMethod(UpdaterMethod method){
        mMethod = method;
    }

    static UpdaterBase* getInstance(UpdaterType type) {
        switch (type) {
            case UpdaterType::Software:
                return UpdaterSoftware::instance();
            case UpdaterType::Firmware:
                switch (mMethod) {
                    case UpdaterMethod::Method1:
                        return UpdaterFirmwareM1::instance();
                    case UpdaterMethod::Method2:
                        return UpdaterFirmwareM1::instance();
                    default:
                        return nullptr;
                }
            default:
                return nullptr;
        }
    }
private:
    inline static UpdaterMethod mMethod= UpdaterFactory::UpdaterMethod::Method1;
};

#endif // UPDATERFACTORY_H