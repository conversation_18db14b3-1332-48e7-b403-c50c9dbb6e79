/************************************************************************
 *
 *  Module:       tbase_al_impl_generic.h
 *  Description:
 *     abstraction layer implementation for common compilers
 *
 *  Author(s):
 *    <PERSON>
 *
 *  Companies:
 *    Thesycon GmbH, Germany      http://www.thesycon.de
 *
 ************************************************************************/

#ifndef __tbase_al_impl_generic_h__
#define __tbase_al_impl_generic_h__



/* memcpy, memmove and memset are defined by the C99 standard and should be declared in string.h */
#include <string.h>

/* note: lint complains about these symbols already defined as functions */

/*lint -esym(652,TbCopyMemory) */
#define TbCopyMemory(dst,src,count) memcpy((dst),(src),(count))

/*lint -esym(652,TbMoveMemory) */
#define TbMoveMemory(dst,src,count) memmove((dst),(src),(count))

/*lint -esym(652,TbSetMemory) */
#define TbSetMemory(mem,val,count)  memset((mem),(val),(count))

/*lint -esym(652,TbZeroMemory) */
#define TbZeroMemory(mem,count)     memset((mem),0,(count))

/*lint -esym(652,TbIsEqualMemory) */
#define TbIsEqualMemory(buf1,buf2,count)  ( 0 == memcmp((buf1),(buf2),(count)) )




#endif  /* __tbase_al_impl_generic_h__ */

/*************************** EOF **************************************/
