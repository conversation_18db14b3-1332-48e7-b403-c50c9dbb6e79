#ifndef FIELDINPUTS1M1_H
#define FIELDINPUTS1M1_H


#include <QWidget>
#include <QVector>
#include <QString>
#include <QVariantList>

#include "inputbase.h"
#include "workspace.h"
#include "appsettings.h"
#include "fieldinputbase1.h"


class FieldInputS1M1 : public FieldInputBase1, public WorkspaceObserver, public AppSettingsObserver
{
    Q_OBJECT
public:
    explicit FieldInputS1M1(QWidget* parent=nullptr, QString name="");
    ~FieldInputS1M1();
    FieldInputS1M1& setName(QString name);
    FieldInputS1M1& modifyWidgetList(QVector<InputBase*> list);
    FieldInputS1M1& setVisibleListDefault(QVector<InputBase*> list);
protected:
    void loadSettings() override;
    void AppSettingsChanged(QString objectName, QString attribute, QString value) override;
private:
    int mSoloState=0;
    QVector<InputBase*> mWidgetList;
    QVariantList mVisibleListDefault;
private slots:
    void in_widgetBase_attributeChanged(QString objectName, QString attribute, QString value);
    void in_widgetList_attributeChanged(QString objectName, QString attribute, QString value);
signals:
    void attributeChanged(QString objectName, QString attribute, QString value);
};


#endif // FIELDINPUTS1M1_H

