/************************************************************************
 *
 *  Module:       WnRegistryKey.cpp
 *
 *  Description:  Registry class wrapper
 *
 *  Runtime Env.: Win32
 *
 *  Author(s):
 *    <PERSON><PERSON>,  Udo<PERSON>@thesycon.de
 *    <PERSON>, <PERSON>@thesycon.de
 *
 *  Companies:
 *    Thesycon GmbH, Germany      http://www.thesycon.de
 *
 ************************************************************************/

#include "libwn_min_global.h"


// Module is empty if .h file was not included (category turned off).
#ifdef __WnRegistryKey_h__

// optionally put everything into a namespace
#ifdef LIBWN_NAMESPACE
namespace LIBWN_NAMESPACE {
#endif

// default constructor
WnRegistryKey::WnRegistryKey()
{
    mKeyHandle = NULL;
}

// construct from an existing handle
WnRegistryKey::WnRegistryKey(HKEY keyHandle)
{
    mKeyHandle = keyHandle;
}


// destructor
WnRegistryKey::~WnRegistryKey()
{
    Close();
}


void
WnRegistryKey::AttachHandle(
    HKEY keyHandle
    )
{
    // make sure the current key is closed
    Close();
    // save handle
    mKeyHandle = keyHandle;
}

HKEY
WnRegistryKey::DetachHandle()
{
    HKEY h = mKeyHandle;
    mKeyHandle = NULL;
    return h;
}


WNERR
WnRegistryKey::OpenKey(
    HKEY parentKey,
    const TCHAR* keyName,
    REGSAM desiredAccess
    )
{
    // make sure the current key is closed
    Close();

    LONG err = ::RegOpenKeyEx(
                        parentKey,      //HKEY hKey
                        keyName,        //LPCTSTR lpSubKey
                        0,              //DWORD ulOptions
                        desiredAccess,  //REGSAM samDesired
                        &mKeyHandle     //PHKEY phkResult
                        );
    return (WNERR)err;
}


WNERR
WnRegistryKey::CreateKey(
    HKEY parentKey,
    const TCHAR* keyName,
    REGSAM desiredAccess
    )
{
    // make sure the current key is closed
    Close();

    LONG err = ::RegCreateKeyEx(
                        parentKey,              //HKEY hKey
                        keyName,                //LPCTSTR lpSubKey
                        0,                      //DWORD Reserved
                        NULL,                   //LPTSTR lpClass,
                        REG_OPTION_NON_VOLATILE,//DWORD dwOptions
                        desiredAccess,          //REGSAM samDesired
                        NULL,                   //LPSECURITY_ATTRIBUTES lpSecurityAttributes,
                        &mKeyHandle,            //PHKEY phkResult,
                        NULL                    //LPDWORD lpdwDisposition
                        );
    return (WNERR)err;
}


WNERR
WnRegistryKey::Close()
{
    LONG err = ERROR_SUCCESS;
    if ( mKeyHandle != NULL ) {
        err = ::RegCloseKey( mKeyHandle );
        mKeyHandle = NULL;
    }
    return (WNERR)err;
}


#ifndef UNDER_CE
// Windows:

WNERR
WnRegistryKey::CreateDevRegKey(
    HDEVINFO deviceInfoSet,
    SP_DEVINFO_DATA* deviceInfoData,
    DWORD keyType,
    DWORD scope /*= DICS_FLAG_GLOBAL*/,
    DWORD hwProfile, /*= 0*/
    HINF infHandle,
    PCTSTR infSectionName
    )
{
    // make sure the current key is closed
    Close();

    HKEY key = ::SetupDiCreateDevRegKey(
                    deviceInfoSet,  // HDEVINFO DeviceInfoSet
                    deviceInfoData, // PSP_DEVINFO_DATA DeviceInfoData
                    scope,          // DWORD Scope
                    hwProfile,      // DWORD HwProfile
                    keyType,        // DWORD KeyType
                    infHandle,      // HINF InfHandle
                    infSectionName  // PCTSTR InfSectionName
                    );
    // Note: The function returns INVALID_HANDLE_VALUE(-1) in case of failure,
    // but also we cannot allow NULL.
    if ( INVALID_HANDLE_VALUE == key || NULL == key ) {
        WNERR err = ::GetLastError();
        WNTRACE(TRCERR,tprint(__FUNCTION__": SetupDiCreateDevRegKey failed, err=0x%X h=0x%p\n", err, key));
        return err;
    }

    // success
    mKeyHandle = key;
    return ERROR_SUCCESS;
}


WNERR
WnRegistryKey::OpenDevRegKey(
    HDEVINFO deviceInfoSet,
    SP_DEVINFO_DATA* deviceInfoData,
    DWORD keyType,
    REGSAM desiredAccess,
    DWORD scope /*= DICS_FLAG_GLOBAL*/,
    DWORD hwProfile /*= 0*/
    )
{
    // make sure the current key is closed
    Close();

    HKEY key = ::SetupDiOpenDevRegKey(
                    deviceInfoSet,  // HDEVINFO DeviceInfoSet
                    deviceInfoData, // PSP_DEVINFO_DATA DeviceInfoData
                    scope,          // DWORD Scope
                    hwProfile,      // DWORD HwProfile
                    keyType,        // DWORD KeyType
                    desiredAccess   // REGSAM samDesired
                    );
    // Note: The function returns INVALID_HANDLE_VALUE(-1) in case of failure,
    // but also we cannot allow NULL.
    if ( INVALID_HANDLE_VALUE == key || NULL == key ) {
        WNERR err = ::GetLastError();
        WNTRACE(TRCERR,tprint(__FUNCTION__": SetupDiOpenDevRegKey failed, err=0x%X h=0x%p\n", err, key));
        return err;
    }

    // success
    mKeyHandle = key;
    return ERROR_SUCCESS;
}
#else

// CE:
WNERR
WnRegistryKey::OpenDevRegKey(
    LPCTSTR ActiveKey
    )
{
    // make sure the current key is closed
    Close();

    HKEY key = OpenDeviceKey(ActiveKey);
    // Note: The function returns INVALID_HANDLE_VALUE(-1) in case of failure,
    // but also we cannot allow NULL.
    if ( INVALID_HANDLE_VALUE == key || NULL == key ) {
        WNERR err = ::GetLastError();
        WNTRACE(TRCERR,tprint(__FUNCTION__": OpenDeviceKey failed, err=0x%X h=0x%p\n", err, key));
        return err;
    }

    // success
    mKeyHandle = key;
    return ERROR_SUCCESS;
}

#endif


WNERR
WnRegistryKey::QueryValueType(
    const TCHAR* valueName,
    unsigned long* valueType
    )
{
    WNASSERT(IsValid());

    LONG err = ::RegQueryValueEx(
                        mKeyHandle, //HKEY hKey
                        valueName,  //LPTSTR lpValueName
                        NULL,       //LPDWORD lpReserved
                        valueType,  //LPDWORD lpType
                        NULL,       //LPBYTE lpData
                        NULL        //LPDWORD lpcbData
                        );
    return (WNERR)err;
}


WNERR
WnRegistryKey::QueryDword(
    const TCHAR* valueName,
    DWORD& value
    )
{
    WNASSERT(IsValid());

    DWORD valueLength = sizeof(DWORD);
    DWORD val;
    DWORD type;

    LONG err = ::RegQueryValueEx(
                        mKeyHandle,             //HKEY hKey
                        valueName,              //LPTSTR lpValueName
                        NULL,                   //LPDWORD lpReserved
                        &type,                  //LPDWORD lpType
                        (unsigned char*) &val,  //LPBYTE lpData
                        &valueLength            //LPDWORD lpcbData
                        );
    if ( err!=ERROR_SUCCESS ) {
        // failed
        return (WNERR)err;
    }

    if ( type==REG_DWORD && valueLength==sizeof(DWORD) ) {
        // ok
        value = val;
        return ERROR_SUCCESS;
    } else {
        WNTRACE(TRCERR,tprint(__FUNCTION__": type or length invalid\n"));
        return ERROR_BAD_FORMAT;
    }
}


WNERR
WnRegistryKey::SetDword(
    const TCHAR* valueName,
    DWORD value
    )
{
    WNASSERT(IsValid());

    DWORD dwordSize = sizeof(DWORD);

    LONG err = ::RegSetValueEx(
                        mKeyHandle,           //HKEY hKey
                        valueName,            //LPCTSTR lpValueName
                        0,                    //DWORD Reserved
                        REG_DWORD,            //DWORD dwType
                        (const BYTE*) &value, //CONST BYTE *lpData
                        dwordSize             //DWORD cbData
                        );
    return (WNERR)err;
}


WNERR
WnRegistryKey::GetDwordValue(
    const TCHAR* valueName,
    DWORD& value,
    DWORD defaultValue
    )
{
    WNASSERT(IsValid());

    // try to query the value
    LONG err = QueryDword(valueName, value);
    if ( err != ERROR_SUCCESS ) {
        // try to create the value
        err = SetDword(valueName, defaultValue);

        // return the default value
        value = defaultValue;
    }

    return (WNERR)err;

} // GetDwordValue


WNERR
WnRegistryKey::QueryString(
    const TCHAR* valueName,
    TCHAR* value,
    unsigned int maxChars
    )
{
    WNASSERT(IsValid());

    if ( maxChars < 1 ) {
        WNTRACE(TRCERR,tprint(__FUNCTION__": maxChars=%u invalid\n", maxChars));
        return ERROR_INVALID_PARAMETER;
    }

    DWORD bcnt = maxChars * sizeof(TCHAR);
    DWORD type;
    LONG err = ::RegQueryValueEx(
                        mKeyHandle,     //HKEY hKey,
                        valueName,      //LPTSTR lpValueName,
                        NULL,           //LPDWORD lpReserved,
                        &type,          //LPDWORD lpType,
                        (LPBYTE)value,  //LPBYTE lpData,
                        &bcnt           //LPDWORD lpcbData
                        );
    if ( err != ERROR_SUCCESS ) {
        value[0] = 0;
        return (WNERR)err;
    }
    if ( !(type==REG_SZ || type==REG_EXPAND_SZ) ) {
        WNTRACE(TRCERR,tprint(__FUNCTION__": unexpected type %u\n", type));
        value[0] = 0;
        return ERROR_BAD_FORMAT;
    }

    // ok, buffer contains string
    // number of chars returned (round down)
    unsigned int n = bcnt / sizeof(TCHAR);
    WNASSERT(n<=maxChars);

    // force null termination
    if ( n == 0 ) {
        value[0] = 0;
    } else {
        if ( value[n-1] == 0 ) {
            // string is already null-terminated
        } else {
            if ( n < maxChars ) {
                value[n] = 0;
            } else {
                value[n-1] = 0;
                return ERROR_BUFFER_OVERFLOW;
            }
        }
    }

    return ERROR_SUCCESS;

} //QueryString



WNERR
WnRegistryKey::SetString(
    const TCHAR* valueName,
    const TCHAR* value,
    DWORD type
    )
{
    WNASSERT(IsValid());

    if ( !(type==REG_SZ || type==REG_EXPAND_SZ) ) {
        WNTRACE(TRCERR,tprint(__FUNCTION__": type=%u invalid\n", type));
        return ERROR_BAD_FORMAT;
    }

    // buffer length in bytes
    DWORD bcnt = (DWORD)((TbStringLen(value) + 1) * sizeof(TCHAR));

    LONG err = ::RegSetValueEx(
                        mKeyHandle,           //HKEY hKey,
                        valueName,            //LPCTSTR lpValueName,
                        0,                    //DWORD Reserved,
                        type,                 //DWORD dwType,
                        (const BYTE*) value,  //CONST BYTE* lpData,
                        bcnt                  //DWORD cbData
                        );
    return (WNERR)err;

} //SetString



WNERR
WnRegistryKey::GetStringValue(
    const TCHAR* valueName,
    TCHAR* value,
    unsigned int maxChars,
    const TCHAR* defaultValue,
    ULONG type
    )
{
    WNASSERT(IsValid());

    // try to query the value
    LONG err = QueryString(valueName, value, maxChars);
    if ( err != ERROR_SUCCESS ) {
        // failed
        // try to create the value
        err = SetString(valueName, defaultValue, type);

        // return the default value
        TbStringNCopy(value, defaultValue, maxChars);
    }

    return (WNERR)err;

} // GetStringValue



WNERR
WnRegistryKey::QueryMultiString(
    const TCHAR* valueName,
    TCHAR* value,
    unsigned int maxChars
    )
{
    WNASSERT(IsValid());

    if ( maxChars < 2 ) {
        WNTRACE(TRCERR,tprint(__FUNCTION__": maxChars=%u invalid\n", maxChars));
        return ERROR_INVALID_PARAMETER;
    }

    DWORD bcnt = maxChars * sizeof(TCHAR);
    DWORD type;
    LONG err = ::RegQueryValueEx(
                        mKeyHandle,     //HKEY hKey,
                        valueName,      //LPTSTR lpValueName,
                        NULL,           //LPDWORD lpReserved,
                        &type,          //LPDWORD lpType,
                        (LPBYTE)value,  //LPBYTE lpData,
                        &bcnt           //LPDWORD lpcbData
                        );
    if ( err != ERROR_SUCCESS ) {
        value[0] = 0;
        value[1] = 0;
        return (WNERR)err;
    }
    if ( type != REG_MULTI_SZ ) {
        WNTRACE(TRCERR,tprint(__FUNCTION__": unexpected type %u\n", type));
        value[0] = 0;
        value[1] = 0;
        return ERROR_BAD_FORMAT;
    }

    // ok, buffer contains multi-string
    // number of chars returned (round down)
    unsigned int n = bcnt / sizeof(TCHAR);
    WNASSERT(n<=maxChars);

    // force double-null termination
    if ( n < 2 ) {
        value[0] = 0;
        value[1] = 0;
    } else {
        if ( value[n-1] == 0 && value[n-2] == 0 ) {
            // string is already double-null-terminated
        } else {
            if ( (n+1) < maxChars ) {
                // force null termination
                value[n] = 0;
                value[n+1] = 0;
            } else {
                value[n-1] = 0;
                value[n-2] = 0;
                return ERROR_BUFFER_OVERFLOW;
            }
        }
    }

    return ERROR_SUCCESS;

} //QueryMultiString



WNERR
WnRegistryKey::SetMultiString(
    const TCHAR* valueName,
    const TCHAR* value
    )
{
    WNASSERT(IsValid());

    // multi-string is terminated by empty string (double null)
    const TCHAR* p = value;
    unsigned int ccnt = 0;
    unsigned int n;
    while ( (n = TbStringLen(p)) != 0 ) {
        ccnt += (n + 1);
        p += (n + 1);
    }
    ccnt += 1;

    // buffer length in bytes
    DWORD bcnt = ccnt * sizeof(WCHAR);

    LONG err = ::RegSetValueEx(
                        mKeyHandle,           //HKEY hKey,
                        valueName,            //LPCTSTR lpValueName,
                        0,                    //DWORD Reserved,
                        REG_MULTI_SZ,         //DWORD dwType,
                        (const BYTE*)value,   //CONST BYTE* lpData,
                        bcnt                  //DWORD cbData
                        );
    return (WNERR)err;

} //SetMultiString



WNERR
WnRegistryKey::GetMultiStringValue(
    const TCHAR* valueName,
    TCHAR* value,
    unsigned int maxChars,
    const TCHAR* defaultValue
    )
{
    WNASSERT(IsValid());

    // try to query the value
    LONG err = QueryMultiString(valueName, value, maxChars);
    if ( err != ERROR_SUCCESS ) {
        // failed
        // try to create the value
        err = SetMultiString(valueName, defaultValue);

        // return the default value
        unsigned int ccnt;
        while ( 0 != (ccnt = TbStringNCopy(value, defaultValue, maxChars)) ) {
            value += (ccnt + 1) ;
            defaultValue += (ccnt + 1);
            maxChars -= (ccnt + 1);
        }
    }

    return (WNERR)err;

} // GetMultiStringValue




WNERR
WnRegistryKey::QueryBinaryData(
    const TCHAR* valueName,
    unsigned char* value,
    unsigned int maxBytes
    )
{
    WNASSERT(IsValid());

    unsigned int n = maxBytes;
    LONG err = ::RegQueryValueEx(
                        mKeyHandle,         //HKEY hKey
                        valueName,          //LPTSTR lpValueName
                        NULL,               //LPDWORD lpReserved
                        NULL,               //LPDWORD lpType
                        value,              //LPBYTE lpData
                        (LPDWORD) &n        //LPDWORD lpcbData
                        );
    return (WNERR)err;
}


WNERR
WnRegistryKey::SetBinaryData(
    const TCHAR* valueName,
    const unsigned char* value,
    unsigned int numberOfBytes
    )
{
    WNASSERT(IsValid());

    LONG err = ::RegSetValueEx(
                        mKeyHandle,   //HKEY hKey
                        valueName,    //LPCTSTR lpValueName
                        0,            //DWORD Reserved
                        REG_BINARY,   //DWORD dwType
                        value,        //CONST BYTE *lpData
                        numberOfBytes //DWORD cbData
                        );
    return (WNERR)err;
}



WNERR
WnRegistryKey::DeleteValue(
    const TCHAR* valueName
    )
{
    WNASSERT(IsValid());

    LONG err = ::RegDeleteValue(
                        mKeyHandle, //HKEY hKey
                        valueName   //LPCTSTR lpValueName
                        );
    return (WNERR)err;
}


WNERR
WnRegistryKey::DeleteSubKey(
    const TCHAR* keyName
    )
{
    WNASSERT(IsValid());

    LONG err = ::RegDeleteKey(
                        mKeyHandle, //HKEY hKey
                        keyName     //LPCTSTR lpSubKey
                        );
    return (WNERR)err;
}


WNERR
WnRegistryKey::DeleteTree(
    const TCHAR* subkeyName
    )
{
    WNASSERT(IsValid());
    WNASSERT(subkeyName!=NULL);
    WNERR err;

    //
    // We implement our own recursive delete of a tree of keys.
    // Windows offers two functions:
    // RegDeleteTree which is available on Vista+ only
    // SHDeleteKey which creates a dependency on shlwapi.dll and probably is not available on CE
    // So it's better to do our own...
    //

    // open top-level key which is a sub key of this key.
    WnRegistryKey tkey;
    err = tkey.OpenKey(GetHandle(), subkeyName, KEY_READ|KEY_WRITE);
    if ( !SUCC(err) ) {
        if (err == ERROR_FILE_NOT_FOUND) {
            WNTRACE(TRCINF,tprint(TEXT(__FUNCTION__) TEXT(": The subkey '%s' doesn't exist.\n"), subkeyName));
            return ERROR_SUCCESS;
        } else {
            WNTRACE(TRCERR,tprint(TEXT(__FUNCTION__) TEXT(": failed to open subkey '%s', err=0x%X\n"), subkeyName, err));
            return err;
        }
    }

    // buffer for sub key name (size limit for key names is 255 chars)
    TCHAR kname[256];

    // delete all sub keys of tkey
    for (;;) {
        // name of first sub key under top-level key
        ZeroMemory(kname, sizeof(kname));
        err = tkey.EnumSubKey(0, kname, TB_ARRAY_ELEMENTS(kname)-1 );
        if ( !SUCC(err) ) {
            if ( err != ERROR_NO_MORE_ITEMS ) {
                WNTRACE(TRCERR,tprint(TEXT(__FUNCTION__) TEXT(": EnumSubKey failed, err=0x%X\n"), err));
            }
            // no more sub keys or failure, done
            break;
        }
        // call this function recursively to delete this subkey and all of its descendants
        err = tkey.DeleteTree(kname);
        if ( !SUCC(err) ) {
            return err;
        }
    } //for

    // done with the top-level key
    tkey.Close();

    // delete the top-level key
    err = DeleteSubKey(subkeyName);
    if ( !SUCC(err) ) {
        WNTRACE(TRCERR,tprint(TEXT(__FUNCTION__) TEXT(": failed to delete subkey '%s', err=0x%X\n"), subkeyName, err));
        return err;
    }

    //WNTRACE(TRCEXTINF,tprint(TEXT(__FUNCTION__) TEXT(": deleted subkey '%s'\n"), subkeyName));

    return ERROR_SUCCESS;

} //DeleteTree



WNERR
WnRegistryKey::EnumValue(
    unsigned long index,
    TCHAR* valueName,
    unsigned int maxChars,
    unsigned long* valueType /*=NULL*/
    )
{
    WNASSERT(IsValid());

    LONG err = ::RegEnumValue(
                        mKeyHandle,           //HKEY hKey
                        index,                //DWORD dwIndex
                        valueName,            //LPTSTR lpValueName
                        (LPDWORD) &maxChars,  //LPDWORD lpcbValueName
                        NULL,                 //LPDWORD lpReserved
                        valueType,            //LPDWORD lpType
                        NULL,                 //LPBYTE lpData
                        NULL                  //LPDWORD lpcbData
                        );
    return (WNERR)err;
}


WNERR
WnRegistryKey::EnumSubKey(
    unsigned long index,
    TCHAR* keyName,
    unsigned int maxChars
    )
{
    WNASSERT(IsValid());

    unsigned long size = maxChars;
    LONG err = ::RegEnumKeyEx(
                        mKeyHandle,     //HKEY hKey
                        index,          //DWORD dwIndex
                        keyName,        //LPTSTR lpName
                        &size,          //LPDWORD lpcbName
                        NULL,           //LPDWORD lpReserved
                        NULL,           //LPTSTR lpClass
                        NULL,           //LPDWORD lpcbClass
                        NULL            //PFILETIME lpftLastWriteTime
                        );
    return (WNERR)err;
}

#ifdef LIBWN_NAMESPACE
}
#endif

#endif

/***************************** EOF **************************************/
