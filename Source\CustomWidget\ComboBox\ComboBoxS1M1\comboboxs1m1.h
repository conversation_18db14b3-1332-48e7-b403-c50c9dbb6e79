#ifndef COMBOBOXS1M1_H
#define COMBOBOXS1M1_H


#include <QWidget>
#include <QComboBox>
#include <QWheelEvent>
#include <QPushButton>
#include <QListWidget>
#include <QResizeEvent>
#include <QListWidgetItem>


class ComboBoxS1M1_ItemS1M1 : public QWidget
{
    Q_OBJECT
public:
    explicit ComboBoxS1M1_ItemS1M1(QWidget* parent=nullptr);
    ~ComboBoxS1M1_ItemS1M1() = default;
    ComboBoxS1M1_ItemS1M1& setItemName(QString text);
    ComboBoxS1M1_ItemS1M1& setButtonDeleteVisible(bool state=true);
protected:
    void resizeEvent(QResizeEvent* e) override;
private:
    QPushButton mButtonDelete;
private slots:
    void in_mButtonDelete_clicked();
signals:
    void deleteClicked(QString item);
};


class ComboBoxS1M1 : public QComboBox
{
    Q_OBJECT
public:
    explicit ComboBoxS1M1(QComboBox* parent=nullptr);
    ~ComboBoxS1M1();
    ComboBoxS1M1& setFont(QFont font);
    ComboBoxS1M1& setName(QString name);
    ComboBoxS1M1& setLanguage(QString language="Chinese");
    ComboBoxS1M1& setDefaultClicked();
    ComboBoxS1M1& setNewClicked();
    ComboBoxS1M1& setSaveAsClicked();
    ComboBoxS1M1& modifyItemList(QVector<QString> list, QString defaultItem);
protected:
    void resizeEvent(QResizeEvent* e) override;
    void wheelEvent(QWheelEvent* e) override;
    void focusOutEvent(QFocusEvent* e) override;
    void keyPressEvent(QKeyEvent* e) override;
    void hidePopup() override;
    void showPopup() override;
private:
    enum CreateNewItemType
    {
        TypeNew=0,
        TypeSaveAs
    };
    QFont mFont;
    QListWidget* mListWidget=nullptr;
    QListWidgetItem* mDefault=nullptr;
    QListWidgetItem* mNew=nullptr;
    QListWidgetItem* mSaveAs=nullptr;
    ComboBoxS1M1_ItemS1M1* mDefaultWidget=nullptr;
    ComboBoxS1M1_ItemS1M1* mNewWidget=nullptr;
    ComboBoxS1M1_ItemS1M1* mSaveAsWidget=nullptr;
    QVector<QString> mListWidgetItem;
    QString mCurrentItem="";
    QString mLanguage="Chinese";
    CreateNewItemType mCreateNewItemType=TypeNew;
    bool mPopupEnable=true;
    bool mNoticeEnable=false;
    bool mInitOK=false;
    void addItem(QString Item);
    QString verifyItem(QString Item);
    void removeItem(QString Item);
    void doDefault(QString Item);
    void doNew(QString Item);
    void doSaveAs(QString Item);
    void doEditFinished();
private slots:
    void in_mComboBox_currentTextChanged(QString text);
    void in_mListWidgetItemAll_deleteClicked(QString item);
signals:
    void attributeChanged(QString objectName, QString attribute, QString value);
};


#endif // COMBOBOXS1M1_H

