#include "globalfont.h"
#include "volumemeters2m1.h"


VolumeMeterS2M1::VolumeMeterS2M1(QWidget* parent)
    : QWidget(parent)
{
    mRectMeter.timerText.setSingleShot(true);
    mRectMeter.timerClip.setSingleShot(true);
    connect(&mRectMeter.timerText, SIGNAL(timeout()), this, SLOT(in_timerText_timeout()), Qt::UniqueConnection);
    connect(&mRectMeter.timerClip, SIGNAL(timeout()), this, SLOT(in_timerClip_timeout()), Qt::UniqueConnection);
    connect(&mTimerMeter, SIGNAL(timeout()), this, SLOT(in_mTimerMeter_timeout()), Qt::UniqueConnection);
}
VolumeMeterS2M1::~VolumeMeterS2M1()
{

}


// override
void VolumeMeterS2M1::resizeEvent(QResizeEvent* e)
{    
    Q_UNUSED(e);
    float hPixelPerRatio = size().height() / 100.0;
    int hSpace1 = hPixelPerRatio * mSpace1;
    int hMeter = hPixelPerRatio * mMeter;
    int hSpace2 = hPixelPerRatio * mSpace2;
    int hScale = hPixelPerRatio * mScale;
    int hText = hPixelPerRatio * mHText;
    int hRemain = size().height() - hSpace1 - hMeter - hSpace2 - hScale - hText;
    int yMeter = size().height() - (hRemain / 2 + hSpace1 + hMeter + hText);
    float pixelPerRatio = size().width() / 100.0;
    int wText = pixelPerRatio * mHText;
    int wSpace1 = pixelPerRatio * mHSpace1;
    int wClip = pixelPerRatio * mHClip;
    int wSpace2 = pixelPerRatio * mHSpace2;
    int wVolume = pixelPerRatio * mHVolume;
    int wSpace3 = pixelPerRatio * mHSpace3;
    int xClip = wVolume + 3*wSpace2;
    
    mRectMeter.volume.setRect(wSpace1, yMeter, wVolume, hMeter);
    mRectMeter.clip.setRect(xClip, yMeter, wClip, hMeter);
    mRectScale.setRect(wSpace1, height()-hScale, size().width(), hScale);
}
void VolumeMeterS2M1::paintEvent(QPaintEvent* e)
{
    Q_UNUSED(e);
    QPainter painter(this);
    painter.setRenderHints(QPainter::Antialiasing | QPainter::TextAntialiasing);
    drawBG(&painter);
    drawElement(&painter);
}
void VolumeMeterS2M1::mouseDoubleClickEvent(QMouseEvent* e)
{
    if(e->button() == Qt::LeftButton)
    {
        if(mRectMeter.clip.contains(e->pos()))
        {
            mRectMeter.clipStatus = false;
            update();
        }
    }
}
void VolumeMeterS2M1::drawBG(QPainter* painter)
{
    painter->save();
    painter->setPen(Qt::NoPen);
    painter->setBrush(mColorBG);
    painter->drawRect(rect());
    painter->restore();
}
void VolumeMeterS2M1::drawElement(QPainter* painter)
{
    float pixelPerScale;
    QPointF textPoint;
    QRect rectMeter;
    painter->save();    // font
    QColor textColor(161, 161, 161);
    mRectMeter.volumeMax >= -8 ? (textColor.setRgb(186,113,68)) : ((void) 0);
    mRectMeter.volumeMax == 0 ? (textColor.setRgb(246,72,71)) : ((void) 0);
    painter->setBrush(Qt::NoBrush);
    painter->setPen(Qt::NoPen);
    mRectMeter.clipStatus ? (painter->setBrush(QBrush(QColor(246, 72, 71)))) : (painter->setBrush(QBrush(QColor(60, 60, 60))));
    painter->drawRoundedRect(mRectMeter.clip, 
        qreal(mRectMeter.clip.height()) / 3.0,
        qreal(mRectMeter.clip.height()) / 3.0);
    // volume
    painter->setBrush(QBrush(QColor(60, 60, 60)));
    painter->drawRoundedRect(mRectMeter.volume,
        qreal(mRectMeter.volume.height()) / 4.0,
        qreal(mRectMeter.volume.height()) / 4.0);

    painter->setPen(Qt::NoPen);
    QLinearGradient gradient(mRectMeter.volume.topLeft(), mRectMeter.volume.topRight());
    gradient.setColorAt(0.0, QColor("#009641"));
    gradient.setColorAt(0.7, QColor("#0f9640"));
    gradient.setColorAt(0.85, QColor("#a77d43"));
    gradient.setColorAt(1.0, QColor("#f64847"));
    painter->setBrush(gradient); 
    pixelPerScale = (qreal)mRectMeter.volume.width() / 60.0;
    rectMeter = mRectMeter.volume;
    rectMeter.setX(mRectMeter.volume.x());
    rectMeter.setWidth(qRound(pixelPerScale * (mRectMeter.volumeValue + 60)));
    if(mRectMeter.volumeValue != -60)
    {
        painter->drawRoundedRect(rectMeter, rectMeter.height() / 4, rectMeter.height() / 4);
    }// scale
    int hScaleLine = qRound(qreal(mRectMeter.volume.height()) / 4.0);
    painter->setPen(QColor(161, 161, 161));
    painter->setBrush(Qt::NoBrush);
    
    QFont scaleFont = mFont;
    int scalePointSize = GLBFHandle.getSuitablePointSize(scaleFont, "-60", 
        QRect(0, 0, mRectScale.width(), mRectScale.height()));
    scalePointSize = qMax(1, scalePointSize);
    scaleFont.setPointSize(scalePointSize);
    painter->setFont(scaleFont);
    
    qreal xPos = mRectMeter.volume.x();
    int yTextPos = mRectScale.y() + qRound((mRectScale.height() + painter->fontMetrics().height()) / 2.0);
    textPoint.setY(yTextPos);

    xPos = mRectMeter.volume.x() + qRound(0 * pixelPerScale);
    if(!mScaleLineHidden) painter->drawLine(QPointF(xPos, mRectMeter.volume.bottom()), QPointF(xPos, mRectMeter.volume.bottom() +hScaleLine));
    textPoint.setX(qRound(xPos - painter->fontMetrics().horizontalAdvance("-60") / 2.0));
    painter->drawText(textPoint, "-60");

    xPos = mRectMeter.volume.x() + qRound(6 * pixelPerScale);
    if(!mScaleLineHidden) painter->drawLine(QPointF(xPos, mRectMeter.volume.bottom()), QPointF(xPos, mRectMeter.volume.bottom() +hScaleLine));
    textPoint.setX(qRound(xPos - painter->fontMetrics().horizontalAdvance("-36") / 2.0));
    painter->drawText(textPoint, "-36");

    xPos = mRectMeter.volume.x() + qRound(12 * pixelPerScale);
    if(!mScaleLineHidden) painter->drawLine(QPointF(xPos, mRectMeter.volume.bottom()), QPointF(xPos, mRectMeter.volume.bottom() +hScaleLine));
    textPoint.setX(qRound(xPos - painter->fontMetrics().horizontalAdvance("-24") / 2.0));
    painter->drawText(textPoint, "-24");

    xPos = mRectMeter.volume.x() + qRound(24 * pixelPerScale);
    if(!mScaleLineHidden) painter->drawLine(QPointF(xPos, mRectMeter.volume.bottom()), QPointF(xPos, mRectMeter.volume.bottom() +hScaleLine));
    textPoint.setX(qRound(xPos - painter->fontMetrics().horizontalAdvance("-12") / 2.0));
    painter->drawText(textPoint, "-12");

    xPos = mRectMeter.volume.x() + qRound(36 * pixelPerScale);
    if(!mScaleLineHidden) painter->drawLine(QPointF(xPos, mRectMeter.volume.bottom()), QPointF(xPos, mRectMeter.volume.bottom() +hScaleLine));
    textPoint.setX(qRound(xPos - painter->fontMetrics().horizontalAdvance("-6") / 2.0));
    painter->drawText(textPoint, "-6");

    xPos = mRectMeter.volume.x() + qRound(60 * pixelPerScale);
    if(!mScaleLineHidden) painter->drawLine(QPointF(xPos, mRectMeter.volume.bottom()), QPointF(xPos, mRectMeter.volume.bottom() +hScaleLine));
    textPoint.setX(qRound(xPos - painter->fontMetrics().horizontalAdvance("0") / 2.0));
    painter->drawText(textPoint, "0");
    painter->restore();
}


// slot
void VolumeMeterS2M1::in_timerText_timeout()
{
    mRectMeter.volumeMax = mRectMeter.volumeValue;
    update();
}
void VolumeMeterS2M1::in_timerClip_timeout()
{
    mRectMeter.clipStatus = false;
    update();
}
void VolumeMeterS2M1::in_mTimerMeter_timeout()
{
    if(mRectMeter.volumeValue > -60)
    {
        mRectMeter.volumeValue = -60 >= mRectMeter.volumeValue ? (-60) : (qMax(-60, mRectMeter.volumeValue - 4));
        update();
    }
    else
    {
        mTimerMeter.stop();
    }
}


// setter & getter
void VolumeMeterS2M1::setFont(QFont font)
{
    mFont = font;
    update();
}
void VolumeMeterS2M1::setColorBG(QColor color)
{
    mColorBG = color;
    update();
}
void VolumeMeterS2M1::setValue(int value)
{
    if(value > 1)
    {
        return;
    }
    if(mTimerMeter.isActive())
    {
        mTimerMeter.stop();
    }
    if(value != 1)
    {
        value = qMax(-600, value);
        if(value % 10)
        {
            value = value / 10 - 1;
        }
        else
        {
            value = value / 10;
        }
    }
    if(value == 1)
    {
        mRectMeter.clipStatus = true;
        value = 0;
        mRectMeter.timerClip.start(5000);
    }
    mRectMeter.volumeValue = value >= mRectMeter.volumeValue ? (value) : (qMax(-60, mRectMeter.volumeValue - 4));
    if(mRectMeter.volumeValue > mRectMeter.volumeMax)
    {
        mRectMeter.volumeMax = mRectMeter.volumeValue;
        mRectMeter.timerText.start(2000);
    }
    else
    {
        if(!mRectMeter.timerText.isActive())
        {
            mRectMeter.volumeMax = mRectMeter.volumeValue;
        }
    }
    update();
}
void VolumeMeterS2M1::setMeterClear()
{
    mTimerMeter.stop();
    mRectMeter.timerClip.stop();
    mRectMeter.timerText.stop();
    mRectMeter.volumeValue = -60;
    mRectMeter.volumeMax = -60;
    mRectMeter.clipStatus = false;
    update();
}
void VolumeMeterS2M1::setMeterSlip()
{
    mTimerMeter.start(55);
}
void VolumeMeterS2M1::setWidthRatio(int space1, int meter, int space2, int scale)
{
    mSpace1 = space1;
    mMeter = meter;
    mSpace2 = space2;
    mScale = scale;
    QResizeEvent e(size(), size());
    resizeEvent(&e);
    update();
}
void VolumeMeterS2M1::setHeightRatio(int text, int space1, int clip, int space2, int volume, int space3)
{
    mHText = text;
    mHSpace1 = space1;
    mHClip = clip;
    mHSpace2 = space2;
    mHVolume = volume;
    mHSpace3 = space3;
    QResizeEvent e(size(), size());
    resizeEvent(&e);
    update();
}
void VolumeMeterS2M1::setScaleLineHidden(bool hidden)
{
    mScaleLineHidden = hidden;
    update();
}

