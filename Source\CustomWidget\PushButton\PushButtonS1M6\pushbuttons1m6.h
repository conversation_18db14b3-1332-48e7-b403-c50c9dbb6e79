#ifndef PUSHBUTTONS1M6_H
#define PUSHBUTTONS1M6_H


#include <QWidget>
#include <QPushButton>
#include <QResizeEvent>


class PushButtonS1M6 : public QWidget
{
    Q_OBJECT
public:
    explicit PushButtonS1M6(QWidget* parent=nullptr);
    ~PushButtonS1M6();
    enum ButtonID
    {
        buttonSOLOLeft=0,
        buttonSOLORight,
        buttonMUTELeft,
        buttonMUTERight,
    };
    PushButtonS1M6& setFont(QFont font);
    PushButtonS1M6& setPushButtonWeightWidth(int weight);
    PushButtonS1M6& setPushButtonStateSOLOLeft(bool state);
    PushButtonS1M6& setPushButtonStateSOLORight(bool state);
    PushButtonS1M6& setPushButtonStateMUTELeft(bool state);
    PushButtonS1M6& setPushButtonStateMUTERight(bool state);
    PushButtonS1M6& setPushButtonClickedSOLOLeft(bool state);
    PushButtonS1M6& setPushButtonClickedSOLORight(bool state);
    PushButtonS1M6& setPushButtonClickedMUTELeft(bool state);
    PushButtonS1M6& setPushButtonClickedMUTERight(bool state);
    bool getPushButtonStateSOLOLeft();
    bool getPushButtonStateSOLORight();
    bool getPushButtonStateMUTELeft();
    bool getPushButtonStateMUTERight();
protected:
    void resizeEvent(QResizeEvent* e) override;
private:
    QFont mFont;
    bool mPushButtonStateSOLOLeft=false;
    bool mPushButtonStateSOLORight=false;
    bool mPushButtonStateMUTELeft=false;
    bool mPushButtonStateMUTERight=false;
    QPushButton mPushButtonSOLOLeft;
    QPushButton mPushButtonSOLORight;
    QPushButton mPushButtonMUTELeft;
    QPushButton mPushButtonMUTERight;
    int mWeightWidth=40;
private slots:
    void in_mPushButtonSOLOLeft_clicked();
    void in_mPushButtonSOLORight_clicked();
    void in_mPushButtonMUTELeft_clicked();
    void in_mPushButtonMUTERight_clicked();
signals:
    void buttonStateChanged(ButtonID button, bool state);
};


#endif // PUSHBUTTONS1M6_H

