#ifndef PUSHBUTTONS1M3_H
#define PUSHBUTTONS1M3_H


#include <QFont>
#include <QWidget>
#include <QPushButton>
#include <QResizeEvent>


class PushButtonS1M3 : public QWidget
{
    Q_OBJECT
public:
    explicit PushButtonS1M3(QWidget* parent=nullptr);
    ~PushButtonS1M3();
    enum ButtonID
    {
        button48V=0,
        buttonAUTO,
        buttonDucking
    };
    PushButtonS1M3& setFont(QFont font);
    PushButtonS1M3& setPushButtonState48V(bool state);
    PushButtonS1M3& setPushButtonStateAUTO(bool state);
    PushButtonS1M3& setPushButtonStateDucking(bool state);
    bool getPushButtonState48V();
    bool getPushButtonStateAUTO();
    bool getPushButtonStateDucking();
protected:
    void resizeEvent(QResizeEvent* e) override;
private:
    QFont mFont;
    bool mPushButtonState48V=false;
    bool mPushButtonStateAUTO=false;
    bool mPushButtonStateDucking=false;
    QPushButton mPushButton48V;
    QPushButton mPushButtonAUTO;
    QPushButton mPushButtonDucking;
    int mRadius=0;
private slots:
    void in_mPushButton48V_clicked();
    void in_mPushButtonAUTO_clicked();
    void in_mPushButtonDucking_clicked();
signals:
    void buttonStateChanged(ButtonID button, bool state);
};


#endif // PUSHBUTTONS1M3_H

