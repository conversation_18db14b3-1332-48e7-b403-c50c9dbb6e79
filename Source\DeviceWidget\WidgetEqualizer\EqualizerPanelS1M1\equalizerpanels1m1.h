#ifndef EQUALIZERPANELS1M1_H
#define EQUALIZERPANELS1M1_H

#include <QWidget>
#include "eqwidgetitemdata.h"

namespace Ui {
class EqualizerPanelS1M1;
}

class EqualizerTool;
class QPushButton;
class EqualizerPanelS1M1 : public QWidget
{
    Q_OBJECT
public:
    explicit EqualizerPanelS1M1(QWidget *parent = nullptr);
    ~EqualizerPanelS1M1();
    EqualizerPanelS1M1& setEqualizerBands(int count);
    EqualizerPanelS1M1& setName(QString name);
    EqualizerPanelS1M1& setFont(QFont font);
    void hideButtons();
    void setSizeFactor(double sizeFactor);
    void setEqualizerData(const QString& index, const QString& type, const QString& data);
    void setLanguage(QString language);
    void setTargetChecked(bool is);
    void setSourceFRChecked(bool is);
    void setEachFilterChecked(bool is);
    void setCombinedFilterChecked(bool is);
    void setFilteredFRChecked(bool is);
    void setCompensatedChecked(bool is);
    QString importTarget(QString path="");
    QString importSource(QString path="");
    void updaEachBandChart();

protected:
    void buttonChecked(QPushButton* button);
    void updateChart(const QVector<EqWidgetItemData>& data);

private:
    Ui::EqualizerPanelS1M1* ui;
    QFont mFont;
    QString mLanguage;
    QHash<QObject*,QHash<bool,QHash<QString,QString>>> mHashLanguages;
    QHash<QPushButton*,QColor> mHashButtonColor;
    EqualizerTool* mEqualizerTool;
    QMenu* mMenuImport;
    QAction* mActionTarget;
    QAction* mActionSourceFR;
    QAction* mActionImportFilters;
    QMenu* mMenuExport;
    QAction* mActionExportFilters;
    QAction* mActionExportFiltersCurve;

signals:
    void attributeChanged(QString objectName, QString attribute, QString value);
};


#endif // EQUALIZERPANELS1M1_H

