#include "equalizercontrollers1m1.h"
#include <QScrollBar>
#include <QTimer>
#include <QDebug>
#include <QLabel>
#include <QPushButton>
#include <QVector>
#include <QScrollArea>
#include <QShowEvent>
#include <QResizeEvent>
#include <qstyleoption.h>
#include <QPainter>
#include <QStyle>
#include "globalfont.h"
#include "comboboxs1m3.h"
#include "eqwidgetiem.h"

EqualizerControllerS1M1::EqualizerControllerS1M1(QWidget* parent)
    : QWidget(parent)
    , mSizeFactor(1.0)
    , mMinimumItemWidth(53)
    , mUpdateSize(false)
    , mLRMarginRatio(0)
    , mScrollArea(nullptr)
    , mScrollWidget(nullptr)
    , mTitleTypeLabel(nullptr)
    , mTitleGainLabel(nullptr)
    , mTitleFrequencyLabel(nullptr)
    , mTitleQLabel(nullptr)
{
    setupUI();
    setStyleSheet(
        "QWidget {"
        "    background-color: rgb(22,22,22);"
        "    border-radius:0px"
        "}"
        "QLabel {"
        "    color: rgb(161,161,161)"
        "}"
    );
    mScrollArea->setStyleSheet(
        "QScrollArea {"
        "   background-color: transparent;"
        "}"
        "QScrollBar::sub-line:horizontal {"
        "   width: 0px;"
        "}"
        "QScrollBar::add-line:horizontal {"
        "   width: 0px;"
        "}"
        "QScrollBar::add-page:horizontal, QScrollBar::sub-page:horizontal {"
        "background: none;""}"
    );
    mHashLanguages.insert(mTitleTypeLabel, {{"English", "Type"}, {"Chinese", "类型"}});
    mHashLanguages.insert(mTitleGainLabel, {{"English", "Gain"}, {"Chinese", "增益"}});
    mHashLanguages.insert(mTitleFrequencyLabel, {{"English", "Freq"}, {"Chinese", "频率"}});
    mHashLanguages.insert(mTitleQLabel, {{"English", "Q"}, {"Chinese", "Q值"}});
}

EqualizerControllerS1M1::~EqualizerControllerS1M1()
{

}

EqualizerControllerS1M1& EqualizerControllerS1M1::setName(QString name)
{
    setObjectName(name);
    return *this;
}
EqualizerControllerS1M1& EqualizerControllerS1M1::setFont(QFont font)
{
    mFont = font;
    resizeEvent(nullptr);
    return *this;
}

void EqualizerControllerS1M1::setupUI()
{
    mScrollArea = new QScrollArea(this);
    mScrollArea->setHorizontalScrollBarPolicy(Qt::ScrollBarAsNeeded);
    mScrollArea->setVerticalScrollBarPolicy(Qt::ScrollBarAlwaysOff);

    mScrollWidget = new QWidget();
    mScrollArea->setFrameStyle(QFrame::NoFrame);
    mScrollArea->setWidget(mScrollWidget);

    mTitleTypeLabel = new QLabel("Type", this);
    mTitleTypeLabel->setAlignment(Qt::AlignCenter);
    mTitleGainLabel = new QLabel("Gain", this);
    mTitleGainLabel->setAlignment(Qt::AlignCenter);
    mTitleFrequencyLabel = new QLabel("Freq", this);
    mTitleFrequencyLabel->setAlignment(Qt::AlignCenter);
    mTitleQLabel = new QLabel("Q", this);
    mTitleQLabel->setAlignment(Qt::AlignCenter);
}

void EqualizerControllerS1M1::setSizeFactor(double sizeFactor)
{
    mSizeFactor = sizeFactor;
    mUpdateSize = true;
    adjustFontAndSize();
    updateItemSize();
    setItemStretch(mSizeFactor);
}

void EqualizerControllerS1M1::setItemMinimumWidth(int width)
{
    mMinimumItemWidth = width;
}

void EqualizerControllerS1M1::setLanguage(QString language)
{
    for(auto element : mHashLanguages.keys()){
        if(auto it = qobject_cast<QLabel*>(element))
            it->setText(mHashLanguages.value(element).value(language));
    }
    for(auto item : mItems){
        item->setLanguage(language);
    }
}

void EqualizerControllerS1M1::setItemStretch(double sizeFactor)
{
    for (auto item : mItems) {
        item->setSizeFactor(sizeFactor);
    }
}

void EqualizerControllerS1M1::setItemCount(int count)
{
    if (count < 0) count = 0;
    if (count > 32) count = 32;

    int currentCount = mItems.size();

    if (count > currentCount) {
        for (int i = currentCount; i < count; ++i) {
            addItem();
        }
    } else if (count < currentCount) {
        for (int i = currentCount - 1; i >= count; --i) {
            removeItem(i);
        }
    }

    updateItemIndices();
    emit ItemCountChanged(count);
}

void EqualizerControllerS1M1::addItem()
{
    int index = mItems.size();
    EqWidgetIem* ItemWidget = new EqWidgetIem(index, mScrollWidget);
    ItemWidget->setFont(mFont);
    ItemWidget->setSizeFactor(mSizeFactor);

    connect(ItemWidget, &EqWidgetIem::dataChanged,this, [this](int index, const EqWidgetItemData& data) {
        emit itemDataChanged(QString::number(index+1), data);
    });
    connect(ItemWidget, &EqWidgetIem::dataChanged,this, [this](int index, const EqWidgetItemData& data) {
        auto changedAttributes = data.getChangedAttributes();
        for (const auto& attr : changedAttributes) {
            emit attributeChanged(objectName()+"@"+QString::number(index+1), attr.first, attr.second);
        }
    });

    mItems.append(ItemWidget);
}

void EqualizerControllerS1M1::removeItem(int index)
{
    if (mItems.isEmpty()) return;

    if (index < 0 || index >= mItems.size()) {
        index = mItems.size() - 1;
    }

    EqWidgetIem* ItemWidget = mItems.takeAt(index);
    ItemWidget->deleteLater();
}

void EqualizerControllerS1M1::removeAllItems()
{
    while (!mItems.isEmpty()) {
        removeItem(-1);
    }
}

void EqualizerControllerS1M1::updateItemIndices()
{
    for (int i = 0; i < mItems.size(); ++i) {
        mItems[i]->setIndex(i);
    }
}

void EqualizerControllerS1M1::setEqualizerData(const QString& index, const QString& type, const QString& data)
{
    int i = index.toInt();
    if (i < 1 || i > mItems.size()) return;
    mItems[i-1]->setData(type, data);
}

void EqualizerControllerS1M1::setEqualizerData(const QVector<EqWidgetItemData>& data)
{
    setItemCount(data.size());

    for (int i = 0; i < data.size() && i < mItems.size(); ++i) {
        mItems[i]->setData(data[i]);
    }
}

QVector<EqWidgetItemData> EqualizerControllerS1M1::getEqualizerData() const
{
    QVector<EqWidgetItemData> data;
    for (const auto& Item : mItems) {
        data.append(Item->getData());
    }
    return data;
}

void EqualizerControllerS1M1::resizeEvent(QResizeEvent* event)
{
    QWidget::resizeEvent(event);
    mScrollArea->resize(width() - mTitleTypeLabel->width() - 2*mMarginX, mScrollArea->height());
}

void EqualizerControllerS1M1::paintEvent(QPaintEvent* e)
{
    Q_UNUSED(e);
    QStyleOption opt;
    opt.initFrom(this);
    QPainter painter(this);
    style()->drawPrimitive(QStyle::PE_Widget, &opt, &painter, this);
}

void EqualizerControllerS1M1::adjustFontAndSize()
{
    int w = width();
    int h = height();
    float hPixelPerRatio = height() / 100.0;
    int titleWidth = w * 0.12;
    mMarginX = w * mLRMarginRatio;
    int scrollBarHeight = hPixelPerRatio * 6;
    int scrollBarRadius = hPixelPerRatio * 0.7;
    int scrollBarMargin = hPixelPerRatio * 2.4;
    int scrollWidgetH = h- scrollBarHeight;
    double perHeight = scrollWidgetH / 15.0;

    mTitleTypeLabel->setGeometry(mMarginX, scrollWidgetH*0.114943, titleWidth, scrollWidgetH*0.0689655);

    mTitleGainLabel->setGeometry(mMarginX, scrollWidgetH*0.270115, titleWidth, scrollWidgetH*0.189655);

    mTitleFrequencyLabel->setGeometry(mMarginX, scrollWidgetH*0.494253, titleWidth, scrollWidgetH*0.189655);

    mTitleQLabel->setGeometry(mMarginX, scrollWidgetH*0.718391, titleWidth, scrollWidgetH*0.189655);

    mScrollArea->setGeometry(titleWidth + mMarginX, 0,  w - titleWidth - 2*mMarginX, h);
    mScrollWidget->setFixedHeight(scrollWidgetH);

    QString style;
    style += QString("QScrollBar::handle:horizontal {"
                        "   background: #43CF7C;"
                        "   min-width: 20px;"
                        "   border-radius: %1px;"
                        "}"
                        "QScrollBar::handle:horizontal:hover {"
                        "   background: #43CF7C;"
                        "}"
                        "QScrollBar::handle:horizontal:pressed {"
                        "   background: #43CF7C;"
                        "}").arg(scrollBarRadius);
    style += QString("QScrollBar:horizontal {"
                     "   background: #2B2B2B;"
                     "   height: %1px;"
                     "   border-radius: %2px;"
                     "   margin-top: %3px;"
                     "   margin-bottom: %3px;"
                     "   margin-left: %3px;"
                     "   margin-right: %3px;"
                     "}").arg(scrollBarHeight).arg(scrollBarRadius).arg(scrollBarMargin);
    mScrollArea->horizontalScrollBar()->setStyleSheet(style);

    mFont.setPointSize(GLBFHandle.getSuitablePointSize(mFont, mTitleTypeLabel->height()));
    mTitleTypeLabel->setFont(mFont);
    mTitleGainLabel->setFont(mFont);
    mTitleFrequencyLabel->setFont(mFont);
    mTitleQLabel->setFont(mFont);
}

void EqualizerControllerS1M1::updateItemSize()
{
    int itemWidth = mMinimumItemWidth * mSizeFactor;
    int itemSpacing = width() * 0.005;
    int currentX = 0;
    for (int i = 0; i < mItems.size(); ++i) {
        EqWidgetIem* item = mItems[i];
        item->setGeometry(currentX, 0, itemWidth, mScrollWidget->height());
        currentX += itemWidth + itemSpacing;
    }
    mScrollWidget->setFixedWidth(currentX);
}