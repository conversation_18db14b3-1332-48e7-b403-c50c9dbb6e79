/************************************************************************
 *
 *  Module:       WnRegistryKey.h
 *
 *  Description:  Class that simplifies access to the registry
 *
 *  Runtime Env.: Win32
 *
 *  Author(s):
 *    <PERSON><PERSON>,  Udo<PERSON>@thesycon.de
 *    <PERSON>, <PERSON>@thesycon.de
 *
 *  Companies:
 *    Thesycon GmbH, Germany      http://www.thesycon.de
 *
 ************************************************************************/

#ifndef __WnRegistryKey_h__
#define __WnRegistryKey_h__

// optionally put everything into a namespace
#ifdef LIBWN_NAMESPACE
namespace LIBWN_NAMESPACE {
#endif

//
// WnRegistryKey
//
// Class that represents an open handle for a registry key.
//
class WnRegistryKey
{
public:
    // default constructor
    WnRegistryKey();
    // construct from an existing handle
    WnRegistryKey(HKEY keyHandle);

    // destructor
    ~WnRegistryKey();


// interface
public:

    //
    // cast to the handle type used by Win32 API
    //
    operator HKEY() const
        { return GetHandle(); }

    //
    // get the attached handle, may return NULL
    //
    HKEY
    GetHandle() const
            { return mKeyHandle; }


    //
    // attach a handle
    // closes existing handle, if any
    //
    void
    AttachHandle(
        HKEY keyHandle
        );

    //
    // detach handle
    // returns current handle
    //
    HKEY
    DetachHandle();


    //
    // returns true if the key handle is valid, false otherwise
    //
    bool
    IsValid() const
        { return (mKeyHandle!=NULL); }


    //
    // open an existing registry key
    // returns ERROR_SUCCESS if successful, an error code otherwise
    //
    // The path provided in keyName may contain more than one level, e.g. "Software\\MyGroup\\MyApplication".
    // It is possible to have two different instances of WnRegistryKey providing access to the same registry key -
    // here the division of the paths between the parameters parentKey and keyName of Open/Create is irrelevant:
    // the same registry key is being accessed using e.g. parentKey = HKEY_CURRENT_USER, keyName ="Software\\MyGroup\\MyApplication"
    // as e.g. parentKey is the Software subkey of HKEY_CURRENT_USER, keyName ="MyGroup\\MyApplication"
    //
    // predefined keys for parentKey are:
    //   HKEY_CLASSES_ROOT
    //   HKEY_CURRENT_CONFIG
    //   HKEY_CURRENT_USER
    //   HKEY_LOCAL_MACHINE
    //   HKEY_USERS
    //
    WNERR
    OpenKey(
        HKEY parentKey,       // previously opened key or predefined root key (HKEY_xxx)
        const TCHAR* keyName, // name of key to open
        REGSAM desiredAccess  // bit mask: KEY_READ | KEY_WRITE etc.
        );


    //
    // create a registry key; if the key already exists, open it
    // returns ERROR_SUCCESS if successful, an error code otherwise
    //
    // Creates a new key or, if the key already exists, opens an existing one.
    // The path provided in keyName may contain more than one level, e.g. "Software\\MyGroup\\MyApplication".
    // If any key in the path, e.g., "MyGroup" in the above example, does not yet exist,
    // this will be created as well its subordinate key "MyApplication".
    //
    // predefined keys for parentKey are:
    //   HKEY_CLASSES_ROOT
    //   HKEY_CURRENT_CONFIG
    //   HKEY_CURRENT_USER
    //   HKEY_LOCAL_MACHINE
    //   HKEY_USERS
    //
    WNERR
    CreateKey(
        HKEY parentKey,       // previously opened key or predefined root key (HKEY_xxx)
        const TCHAR* keyName, // name of key to create/open
        REGSAM desiredAccess  // bit mask: KEY_READ | KEY_WRITE etc.
        );


#ifndef UNDER_CE

    WNERR
    CreateDevRegKey(
        HDEVINFO deviceInfoSet,
        SP_DEVINFO_DATA* deviceInfoData,
        DWORD keyType,
        DWORD scope = DICS_FLAG_GLOBAL,
        DWORD hwProfile = 0,
        HINF infHandle = NULL,
        PCTSTR infSectionName = NULL
        );

    // Windows:
    // Open a device-specific registry key
    // wrapper for SetupDiOpenDevRegKey
    // returns ERROR_SUCCESS if successful, an error code otherwise
    //
    WNERR
    OpenDevRegKey(
        HDEVINFO deviceInfoSet,           // handle, e.g. returned by WnDeviceInterfaceEnumerator::GetDeviceInfoSet()
        SP_DEVINFO_DATA* deviceInfoData,  // reference to device instance, e.g. returned by WnDeviceInterfaceEnumerator::GetDeviceInfoData()
        DWORD keyType,                    // DIREG_DEV for hw key, DIREG_DRV for sw key
        REGSAM desiredAccess,             // bit mask: KEY_READ | KEY_WRITE etc.
        DWORD scope = DICS_FLAG_GLOBAL,   // DICS_FLAG_GLOBAL or DICS_FLAG_CONFIGSPECIFIC
        DWORD hwProfile = 0
        );

#else
    // CE:
    // Open a device-specific registry key
    // wrapper for SetupDiOpenDevRegKey
    // returns ERROR_SUCCESS if successful, an error code otherwise
    //
    WNERR
    OpenDevRegKey(
        LPCTSTR ActiveKey
        );
#endif


    //
    // close the registry key
    //
    WNERR
    Close();
        // return value of this function is likely to be ignored
        //lint -esym(534, WnRegistryKey::Close)


    // query the type of a value
    // returns ERROR_SUCCESS if successful, an error code otherwise
    //
    WNERR
    QueryValueType(
        const TCHAR* valueName,   // name of value entry to query, null-terminate string
        unsigned long* valueType  // variable that receives the value type
        );


    // query a DWORD value (REG_DWORD)
    // returns ERROR_SUCCESS if successful, an error code otherwise
    // ERROR_BAD_FORMAT means that the value queried was not of type REG_DWORD.
    //
    // NOTE: value will not be set if the function fails.
    //
    WNERR
    QueryDword(
        const TCHAR* valueName, // name of value entry to query, null-terminate string
        DWORD& value            // variable that receives the DWORD value
        );

    //
    // set a DWORD value (REG_DWORD)
    // returns ERROR_SUCCESS if successful, an error code otherwise
    //
    WNERR
    SetDword(
        const TCHAR* valueName, // name of value entry to set, null-terminated string
        DWORD value             // DWORD value to set
        );

    //
    // query a DWORD value (REG_DWORD)
    // If the value entry does not exist, create it using the given default value and
    // return defaultValue at value.
    // returns ERROR_SUCCESS if successful, an error code otherwise
    //
    WNERR
    GetDwordValue(
        const TCHAR* valueName,
        DWORD& value,
        DWORD defaultValue
        );


    //
    // query REG_SZ or REG_EXPAND_SZ string value
    // returns ERROR_SUCCESS if successful, an error code otherwise
    // ERROR_INVALID_PARAMETER means that parameter 3 (maxChars) was zero, so no characters could be returned.
    // ERROR_BAD_FORMAT means that the type of the value queried was incorrect.
    //
    WNERR
    QueryString(
        const TCHAR* valueName, // name of value entry to query, null-terminated string
        TCHAR* value,           // pointer to caller-provided string buffer that receives the null-terminated string
        unsigned int maxChars   // max number of characters that may be returned at value,
                                    // includes the terminating null
        );

    //
    // set a REG_SZ or REG_EXPAND_SZ string value
    // returns ERROR_SUCCESS if successful, an error code otherwise
    // ERROR_BAD_FORMAT means that parameter 3 (Type) was incorrect
    //
    WNERR
    SetString(
        const TCHAR* valueName, // name of value entry to set, null-terminated string
        const TCHAR* value,     // string value to set, null-terminated string
        DWORD type = REG_SZ     // type of string value (REG_SZ, REG_EXPAND_SZ)
        );

    //
    // query a REG_SZ or REG_EXPAND_SZ string value
    // If the value entry does not exist, create it using the given default value and
    // return defaultValue at value.
    // returns ERROR_SUCCESS if successful, an error code otherwise
    //
    WNERR
    GetStringValue(
        const TCHAR* valueName,     // name of value entry to query, null-terminated string
        TCHAR* value,               // pointer to caller-provided string buffer that receives the null-terminated string
        unsigned int maxChars,      // max number of characters that may be returned at value, includes the terminating null
        const TCHAR* defaultValue,  // default value to set, null-terminated string
        ULONG type = REG_SZ         // REG_SZ, REG_EXPAND_SZ
        );


    //
    // query REG_MULTI_SZ string value (string list)
    // returns ERROR_SUCCESS if successful, an error code otherwise
    // ERROR_INVALID_PARAMETER means that parameter 3 (maxChars) was zero, so no characters could be returned.
    // ERROR_BAD_FORMAT means that the type of the value queried was incorrect.
    //
    WNERR
    QueryMultiString(
        const TCHAR* valueName, // name of value entry to query, null-terminated string
        TCHAR* value,           // pointer to caller-provided string buffer that receives the double null terminated string list
        unsigned int maxChars   // max number of characters that may be returned at value,
                                    // includes the terminating double null
        );

    //
    // set a REG_MULTI_SZ string value (string list)
    // returns ERROR_SUCCESS if successful, an error code otherwise
    // ERROR_BAD_FORMAT means that parameter 3 (Type) was incorrect
    //
    WNERR
    SetMultiString(
        const TCHAR* valueName, // name of value entry to set, null-terminated string
        const TCHAR* value      // multi string, list of null-terminated strings, terminated by empty string
        );

    //
    // query a REG_MULTI_SZ string value (string list)
    // If the value entry does not exist, create it using the given default value and
    // return defaultValue at value.
    // returns ERROR_SUCCESS if successful, an error code otherwise
    //
    WNERR
    GetMultiStringValue(
        const TCHAR* valueName,   // name of value entry to query, null-terminated string
        TCHAR* value,             // pointer to caller-provided string buffer that receives the double null terminated string list
        unsigned int maxChars,    // max number of characters that may be returned at value, includes the terminating double null
        const TCHAR* defaultValue // default value to set, double null terminated string
        );



    //
    // query binary data (REG_BINARY)
    // returns ERROR_SUCCESS if successful, an error code otherwise
    //
    WNERR
    QueryBinaryData(
        const TCHAR* valueName, // name of value entry to query, null-terminate string
        unsigned char* value,   // pointer to caller-provided buffer that receives the data
        unsigned int maxBytes   // max number of bytes that may be returned at value,
        );

    //
    // set binary data (REG_BINARY)
    // returns ERROR_SUCCESS if successful, an error code otherwise
    //
    WNERR
    SetBinaryData(
        const TCHAR* valueName,      // name of value entry to set, null-terminated string
        const unsigned char* value,  // pointer to caller-provided data
        unsigned int numberOfBytes   // size of data, in bytes
        );



    //
    // delete a value contained in this registry key
    // returns ERROR_SUCCESS if successful, an error code otherwise
    //
    WNERR
    DeleteValue(
        const TCHAR* valueName  // name of value entry to delete
        );


    //
    // Delete a sub key of this registry key, and all values the sub key contains.
    // returns ERROR_SUCCESS if successful, an error code otherwise
    //
    // If the pathname stored under keyName contains more than one level,
    // e.g. "Software\\MyGroup\\MyApplication\\MySetting",
    // only the last key in the level ("MySetting") will be deleted.
    // The sub key to be deleted must not have child subkeys.
    // To delete sub keys recursively, use DeleteTree.
    //
    WNERR
    DeleteSubKey(
        const TCHAR* keyName   // name of sub key to delete
        );


    //
    // Delete the specified sub key and all its descendants.
    // All values and sub keys of the specified key will be deleted recursively.
    // Returns ERROR_SUCCESS if successful or if the sub key doesn't exist,
    // an error code otherwise.
    //
    WNERR
    DeleteTree(
        const TCHAR* subkeyName   // name of the subkey that represents the top-level key
                                      // of the tree that is to be deleted
        );


    //
    // enumerate value entries of this registry key
    // returns ERROR_SUCCESS if successful, an error code otherwise
    // ERROR_NO_MORE_ITEMS means there are no items left to enumerate.
    //
    WNERR
    EnumValue(
        unsigned long index,            // zero-based index
        TCHAR* valueName,               // pointer to caller-provided buffer that receives the null-terminated value name string
        unsigned int maxChars,          // max number of characters that may be returned at valueName, includes the terminating null
        unsigned long* valueType = NULL // pointer to a caller-provided variable that receives the value type (REG_xxx)
        );

    //
    // enumerate sub keys of this registry key
    // returns ERROR_SUCCESS if successful, an error code otherwise
    // ERROR_NO_MORE_ITEMS means there are no items left to enumerate.
    //
    WNERR
    EnumSubKey(
        unsigned long index,  // zero-based index
        TCHAR* keyName,       // pointer to caller-provided buffer that receives the null-terminated key name string
        unsigned int maxChars // max number of characters that may be returned at keyName, includes the terminating null
        );


// implementation
protected:
    // the key handle, NULL if invalid
    HKEY mKeyHandle;

// make copy constructor and assignment operator unavailable
PRIVATE_COPY_CONSTRUCTOR(WnRegistryKey)
PRIVATE_ASSIGNMENT_OPERATOR(WnRegistryKey)

}; //class WnRegistryKey

#ifdef LIBWN_NAMESPACE
}
#endif

#endif // __WnRegistryKey_h__

/*************************** EOF **************************************/

