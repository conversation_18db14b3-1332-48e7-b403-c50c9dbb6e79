chcp 65001
setlocal enabledelayedexpansion
@echo off
cls

set "Deployer=D:\QT\6.9.0\msvc2022_64\bin\windeployqt.exe"
set "Packager=C:\Program Files (x86)\Inno Setup 6\ISCC.exe"
set "Signer=.\wosigncodecmd.exe"
set "Compressor=D:\7ZIP\7-Zip\7z.exe"
set "Source=.\..\..\Build\Release"
set "Target=.\..\..\Build"

for %%f in ("%Source%\*.exe") do (
    set "Application=%%~nf"
    goto :InputSeries
)
echo.
echo 未找到可执行文件，需要先编译工程
exit /b
:InputSeries
set /p Series="Series: "
if not exist ".\Series!Series!\Extras" (
    echo "需要配置Series!Series!的打包环境"
    goto InputSeries
)
cls
set /p Version="Version: "
for %%A in (%Version:.= %) do (
    set "Revision=%%A"
)
set "IsBeta=1"
set "BetaExtend= Beta"
if "!Revision!"=="0" (
    set "IsBeta=0"
    set "BetaExtend="
)
cls
set /p SignPassword="SignPassword(先插入密钥): "
cls
set "Source=!Source!\!Application!.exe"
if not exist "!Target!\!Version!\!Version!" (
    echo 开始打包
    mkdir "!Target!\!Version!\!Version!"
    mkdir "!Target!\!Version!\!Version!Test"
    copy "!Source!" "!Target!\!Version!\!Version!"
    "!Deployer!" "!Target!\!Version!\!Version!\!Application!.exe"
    robocopy ".\Extras" "!Target!\!Version!\!Version!"
    robocopy "!Target!\!Version!\!Version!" "!Target!\!Version!\!Version!Test" /MIR
    "!Compressor!" a -tzip "!Target!\!Version!\TPCC_Series!Series!_Win_!Version!.zip" "!Target!\!Version!\!Version!\*"
    explorer "!Target!\!Version!"
    start "" "!Target!\!Version!\!Version!Test\!Application!.exe"
    "!Packager!" /DMyAppVersion=!Version! /DMyAppIsBeta=!IsBeta! ".\Series!Series!\Script.iss"
    if not "!SignPassword!"=="" (
        echo.
        echo.
        "!Signer!" sign /hide /tp f82f61b31896b08be91e10c0239f94b9a737bbac /p !SignPassword! /dig sha256 /t http://timestamp.digicert.com /file "!Target!\!Version!\!Application! !Version!!BetaExtend! Setup.exe"
    )
) else (
    echo "版本: !Version!已存在"
)
echo.
echo.
echo 操作完成
echo.
endlocal
