#include "globalfont.h"
#include "pushbuttons1m9.h"


PushButtonS1M9::PushButtonS1M9(QWidget* parent)
    : QWidget(parent)
{
    mPushButton48V.setParent(this);
    mPushButtonMUTE.setParent(this);
    QString style;
    style = "QPushButton {"
            "   border-radius: 3px;"
            "   color: rgb(161, 161, 161);"
            "   background-color: rgb(60, 60, 60);"
            "}"
            "QPushButton:hover {"
            "   border: 2px solid gray;"
            "   border-radius: 3px;"
            "}";
    mPushButton48V.setStyleSheet(style);
    mPushButtonMUTE.setStyleSheet(style);
    mPushButton48V.setText("48V");
    mPushButtonMUTE.setText("MUTE");
    connect(&mPushButton48V, SIGNAL(clicked()), this, SLOT(in_mPushButton48V_clicked()), Qt::UniqueConnection);
    connect(&mPushButtonMUTE, SIGNAL(clicked()), this, SLOT(in_mPushButtonMUTE_clicked()), Qt::UniqueConnection);
}
PushButtonS1M9::~PushButtonS1M9()
{

}


// override
void PushButtonS1M9::resizeEvent(QResizeEvent* e)
{
    Q_UNUSED(e);
    // W
    float wPixelPerRatio=size().width() / 100.0;
    int wPushButton=wPixelPerRatio * mWeightWidth;
    int xPushButton=(size().width() - wPushButton) / 2;
    // H
    float hPixelPerRatio=size().height() / 100.0;
    int hSpace1=hPixelPerRatio * 10;
    int hSpace2=hPixelPerRatio * 15;
    int hSpace3=hPixelPerRatio * 10;
    int hPushButton=(size().height() - hSpace1 - hSpace2 - hSpace3) / 2;
    mPushButton48V.setGeometry(xPushButton, hSpace1, wPushButton, hPushButton);
    mPushButtonMUTE.setGeometry(xPushButton, hSpace1 + hPushButton + hSpace2, wPushButton, hPushButton);
    mFont.setPointSize(GLBFHandle.getSuitablePointSize(mFont, mPushButton48V.text(), mPushButton48V.rect()));
    mPushButton48V.setFont(mFont);
    mPushButtonMUTE.setFont(mFont);
}


// slot
void PushButtonS1M9::in_mPushButton48V_clicked()
{
    QString style;
    mPushButtonState48V = !mPushButtonState48V;
    if(mPushButtonState48V)
    {
        style = "QPushButton {"
                "   border-radius: 3px;"
                "   color: rgb(229, 229, 229);"
                "   background-color: rgb(222, 55, 55);"
                "}"
                "QPushButton:hover {"
                "   border: 2px solid gray;"
                "   border-radius: 3px;"
                "}";
    }
    else
    {
        style = "QPushButton {"
                "   border-radius: 3px;"
                "   color: rgb(161, 161, 161);"
                "   background-color: rgb(60, 60, 60);"
                "}"
                "QPushButton:hover {"
                "   border: 2px solid gray;"
                "   border-radius: 3px;"
                "}";
    }
    mPushButton48V.setStyleSheet(style);
    emit buttonStateChanged(button48V, mPushButtonState48V);
}
void PushButtonS1M9::in_mPushButtonMUTE_clicked()
{
    QString style;
    mPushButtonStateMUTE = !mPushButtonStateMUTE;
    if(mPushButtonStateMUTE)
    {
        style = "QPushButton {"
                "   border-radius: 3px;"
                "   color: rgb(229, 229, 229);"
                "   background-color: rgb(107, 5, 5);"
                "}"
                "QPushButton:hover {"
                "   border: 2px solid gray;"
                "   border-radius: 3px;"
                "}";
    }
    else
    {
        style = "QPushButton {"
                "   border-radius: 3px;"
                "   color: rgb(161, 161, 161);"
                "   background-color: rgb(60, 60, 60);"
                "}"
                "QPushButton:hover {"
                "   border: 2px solid gray;"
                "   border-radius: 3px;"
                "}";
    }
    mPushButtonMUTE.setStyleSheet(style);
    emit buttonStateChanged(buttonMUTE, mPushButtonStateMUTE);
}


// setter & getter
PushButtonS1M9& PushButtonS1M9::setFont(QFont font)
{
    mFont = font;
    QResizeEvent e(size(), size());
    resizeEvent(&e);
    update();
    return *this;
}
PushButtonS1M9& PushButtonS1M9::setPushButtonWeightWidth(int weight)
{
    mWeightWidth = weight;
    QResizeEvent e(size(), size());
    resizeEvent(&e);
    update();
    return *this;
}
PushButtonS1M9& PushButtonS1M9::setPushButtonState48V(bool state)
{
    QString style;
    mPushButtonState48V = state;
    if(mPushButtonState48V)
    {
        style = "QPushButton {"
                "   border-radius: 3px;"
                "   color: rgb(229, 229, 229);"
                "   background-color: rgb(222, 55, 55);"
                "}"
                "QPushButton:hover {"
                "   border: 2px solid gray;"
                "   border-radius: 3px;"
                "}";
    }
    else
    {
        style = "QPushButton {"
                "   border-radius: 3px;"
                "   color: rgb(161, 161, 161);"
                "   background-color: rgb(60, 60, 60);"
                "}"
                "QPushButton:hover {"
                "   border: 2px solid gray;"
                "   border-radius: 3px;"
                "}";
    }
    mPushButton48V.setStyleSheet(style);
    return *this;
}
PushButtonS1M9& PushButtonS1M9::setPushButtonStateMUTE(bool state)
{
    QString style;
    mPushButtonStateMUTE = state;
    if(mPushButtonStateMUTE)
    {
        style = "QPushButton {"
                "   border-radius: 3px;"
                "   color: rgb(229, 229, 229);"
                "   background-color: rgb(107, 5, 5);"
                "}"
                "QPushButton:hover {"
                "   border: 2px solid gray;"
                "   border-radius: 3px;"
                "}";
    }
    else
    {
        style = "QPushButton {"
                "   border-radius: 3px;"
                "   color: rgb(161, 161, 161);"
                "   background-color: rgb(60, 60, 60);"
                "}"
                "QPushButton:hover {"
                "   border: 2px solid gray;"
                "   border-radius: 3px;"
                "}";
    }
    mPushButtonMUTE.setStyleSheet(style);
    return *this;
}
PushButtonS1M9& PushButtonS1M9::setPushButtonClicked48V(bool state)
{
    mPushButtonState48V = !state;
    in_mPushButton48V_clicked();
    return *this;
}
PushButtonS1M9& PushButtonS1M9::setPushButtonClickedMUTE(bool state)
{
    mPushButtonStateMUTE = !state;
    in_mPushButtonMUTE_clicked();
    return *this;
}
bool PushButtonS1M9::getPushButtonState48V()
{
    return mPushButtonState48V;
}
bool PushButtonS1M9::getPushButtonStateMUTE()
{
    return mPushButtonStateMUTE;
}

