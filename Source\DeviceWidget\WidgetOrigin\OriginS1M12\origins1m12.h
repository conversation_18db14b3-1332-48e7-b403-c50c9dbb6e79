#ifndef ORIGINS1M12_H
#define ORIGINS1M12_H


#include <QFont>
#include <QEvent>
#include <QTimer>
#include <QObject>
#include <QWidget>
#include <QResizeEvent>

#include "originbase.h"
#include "workspace.h"
#include "appsettings.h"


namespace Ui {
class OriginS1M12;
}


class OriginS1M12 : public OriginBase, public WorkspaceObserver, public AppSettingsObserver
{
    Q_OBJECT
public:
    explicit OriginS1M12(QWidget* parent=nullptr, QString name="");
    ~OriginS1M12();
    OriginS1M12& setName(QString name);
    OriginS1M12& setFont(QFont font);
    OriginS1M12& setVolumeMeterLeft(int value);
    OriginS1M12& setVolumeMeterLeftClear();
    OriginS1M12& setVolumeMeterLeftSlip();
    OriginS1M12& setVolumeMeterRight(int value);
    OriginS1M12& setVolumeMeterRightClear();
    OriginS1M12& setVolumeMeterRightSlip();
    OriginS1M12& setGain(float value);
    OriginS1M12& setGainLock(bool state=true);
    OriginS1M12& setMuteAffectGain(bool state=true);
    OriginS1M12& setGainAffectMute(bool state=true);
    OriginS1M12& setGainRange(int valueStart, int valueEnd05, int valueEnd10, int valueEnd20);
    OriginS1M12& setGainDefault(float value);
    OriginS1M12& setGainWidgetDisable(float value);
    OriginS1M12& setChannelNameEditable(bool state=true);
    OriginS1M12& setValueGAIN(float value);
    OriginS1M12& setValueMUTE(bool state=true);
    OriginS1M12& setOverlay(bool state=true);
protected:
    bool eventFilter(QObject* obj, QEvent* e) override;
    void resizeEvent(QResizeEvent* e) override;
    void updateAttribute() override;
    void loadSettings() override;
    void AppSettingsChanged(QString objectName, QString attribute, QString value) override;
private:
    Ui::OriginS1M12* ui;
    QTimer mTimer;
    QFont mFont;
    int mPreMUTE=-2147483648;
    float mPreGAIN=-2147483648;
    float mDisableGAIN=-88;
    bool mMuteAffectGain=false;
    bool mGainAffectMute=false;
    void save(QAnyStringView key, const QVariant& value);
private slots:
    void in_mTimer_timeout();
    void in_widgetDial_valueChanged(float value);
    void in_widgetPushButtonGroup2_stateChanged(QString button, QString state);
    void on_lineEdit_textChanged(const QString& arg1);
    void on_lineEdit_editingFinished();
    void on_pushButtonClose_clicked();
};


#endif // ORIGINS1M12_H

