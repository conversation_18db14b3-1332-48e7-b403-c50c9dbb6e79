#ifndef VSLIDERS1M1_H
#define VSLIDERS1M1_H


#include <QFont>
#include <QEvent>
#include <QSlider>
#include <QObject>
#include <QWidget>
#include <QLineEdit>
#include <QResizeEvent>


class VSliderS1M1 : public QWidget
{
    Q_OBJECT
public:
    explicit VSliderS1M1(QWidget *parent=nullptr);
    ~VSliderS1M1();
    VSliderS1M1& setFont(QFont font);
    VSliderS1M1& setValue(int value);
    VSliderS1M1& setStep(int value);
    VSliderS1M1& setDefault(int value);
    VSliderS1M1& setRange(int minValue, int maxValue);
    VSliderS1M1& setHeightRatio(int text, int space, int slider);
    int getValue() { return mValue; }
    int getDefault() { return mValueDefault; }
    int getMin() { return mSlider.minimum(); }
    int getMax() { return mSlider.maximum(); }
    VSliderS1M1& showInfinity(bool state=true);
    VSliderS1M1& showInfinitesimal(bool state=true);
protected:
    bool eventFilter(QObject* obj, QEvent* e) override;
    void resizeEvent(QResizeEvent* e) override;
private:
    QLineEdit mLineEdit;
    QSlider mSlider;
    QFont mFont;
    bool mShowInfinity=false;
    bool mShowInfinitesimal=false;
    bool mEmitOpen=false;
    int mValue=0;
    int mValueDefault=0;
    int mHText=8;
    int mHSpace=2;
    int mHSlider=90;
private slots:
    void in_mLineEdit_editingFinished();
    void in_mSlider_valueChanged(int value);
signals:
    void valueChanged(int value);
};


#endif // VSLIDERS1M1_H

