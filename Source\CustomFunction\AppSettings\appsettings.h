#ifndef APPSETTINGS_H
#define APPSETTINGS_H


#include <QString>
#include <QVector>
#include <QVariant>
#include <QSettings>
#include <QMainWindow>
#include <QAnyStringView>


class AppSettingsObserver
{
public:
    AppSettingsObserver(const QString& name) : mObserverName(name) { }
    virtual ~AppSettingsObserver() = default;
    void setObserverName(QString name) { mObserverName = name; }
    QString getObserverName() { return mObserverName; }
    virtual QVariant value(QAnyStringView key);
    virtual void AppSettingsChanged(QString objectName, QString attribute, QString value) = 0;
private:
    QString mObserverName;
    friend class AppSettingsSubject;
};


class AppSettingsSubject : public QObject
{
    Q_OBJECT
public:
    static AppSettingsSubject& instance() { return mInstance; }
    bool init();
    void assignMainWindow(QMainWindow* mainwindow) { mMainWindow = mainwindow; }
    QMainWindow* getMainWindow() { return mMainWindow; }
    AppSettingsSubject& addObserver(AppSettingsObserver* observer);
    AppSettingsSubject& addObserverList(QVector<AppSettingsObserver*> observerList);
    AppSettingsSubject& removeObserverOne(AppSettingsObserver* observer);
    AppSettingsSubject& removeObserverAll();
    AppSettingsSubject& listObserver();
    AppSettingsSubject& changeLanguage(QString language="Chinese");
    AppSettingsSubject& changeScaleFactor(QString factor);
private:
    static AppSettingsSubject mInstance;
    QMainWindow* mMainWindow=nullptr;
    QVector<AppSettingsObserver*> mObservers;
    AppSettingsSubject();
    AppSettingsSubject(const AppSettingsSubject&) = delete;
    ~AppSettingsSubject() { }
    AppSettingsSubject& operator=(const AppSettingsSubject&) = delete;
signals:
    void attributeChanged(QString objectName, QString attribute, QString value);
};


#define APPSHandle AppSettingsSubject::instance()


#endif // APPSETTINGS_H

