/***********************************************************************
*  Wrapper for 64bit support functions.
*
*  Thesycon GmbH, Germany
*  http://www.thesycon.de
*
************************************************************************/

#ifndef __WnWow64_h__
#define __WnWow64_h__

// optionally put everything into a namespace
#ifdef LIBWN_NAMESPACE
namespace LIBWN_NAMESPACE {
#endif

//
// WnWow64FsRedirection
//
class WnWow64FsRedirection
{
public:
    // ctor
    WnWow64FsRedirection();

    //
    // Disable file system redirection under WOW64 for the calling thread.
    //
    // To restore the old state call 'RevertWow64FsRedirection' on the same class instance.
    //
    // Note: The function should only be called if the process is running
    //       under WOW64. To check this call 'IsWow64Process' before. If
    //       the function is called under 32-bit Windows an error is returned.
    //
    // return:
    //    ERROR_SUCCESS if successful, an error code otherwise
    //
    WNERR
    DisableWow64FsRedirection();

    //
    // Restore the state of file system redirection for the calling thread
    // that has been set before 'DisableWow64FsRedirection' has been called.
    //
    // Note: The function should only be called if the process is running
    //       under WOW64. To check this call 'IsWow64Process' before. If
    //       the function is called under 32-bit Windows an error is returned.
    //
    // return:
    //    ERROR_SUCCESS if successful, an error code otherwise
    //
    WNERR
    RevertWow64FsRedirection();


    // ------------
    // Data Members
protected:

    void* mOldRedirVal;

}; // class WnWow64FsRedirection

//---global functions ------------------------------------------------------------------------------------- 

//
// check whether the process is running under WOW64
//
// parameters:
//    is    Caller-provided parameter that returns true if the
//          process is running under WOW64, false if not.
//          If true is returned, possible reasons are:
//          - the process is a 32-bit process running under 64-bit Windows.
//          If false is returned, possible reasons are:
//          - 32-bit Windows is running or
//          - the process is a 64-bit process running under 64-bit Windows.
//
WNERR
WnIsWow64Process(
    bool& is
);

#ifdef LIBWN_NAMESPACE
}
#endif

#endif // __WnWow64_h__

/***************************** EOF **************************************/
