<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>EffectS1M3</class>
 <widget class="QWidget" name="EffectS1M3">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>206</width>
    <height>474</height>
   </rect>
  </property>
  <property name="sizePolicy">
   <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
    <horstretch>0</horstretch>
    <verstretch>0</verstretch>
   </sizepolicy>
  </property>
  <property name="minimumSize">
   <size>
    <width>100</width>
    <height>274</height>
   </size>
  </property>
  <property name="maximumSize">
   <size>
    <width>16777215</width>
    <height>16777215</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <widget class="QLabel" name="labelNoiseClass">
   <property name="geometry">
    <rect>
     <x>40</x>
     <y>110</y>
     <width>91</width>
     <height>41</height>
    </rect>
   </property>
   <property name="text">
    <string>Level</string>
   </property>
   <property name="alignment">
    <set>Qt::AlignmentFlag::AlignCenter</set>
   </property>
  </widget>
  <widget class="QWidget" name="ncWidget" native="true">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>160</y>
     <width>181</width>
     <height>191</height>
    </rect>
   </property>
   <property name="styleSheet">
    <string notr="true"/>
   </property>
   <layout class="QGridLayout" name="gridLayout" rowstretch="8,14,6,14,8" columnstretch="11,38,11">
    <property name="leftMargin">
     <number>0</number>
    </property>
    <property name="topMargin">
     <number>0</number>
    </property>
    <property name="rightMargin">
     <number>0</number>
    </property>
    <property name="bottomMargin">
     <number>0</number>
    </property>
    <property name="spacing">
     <number>0</number>
    </property>
    <item row="0" column="1">
     <spacer name="verticalSpacer_2">
      <property name="orientation">
       <enum>Qt::Orientation::Vertical</enum>
      </property>
      <property name="sizeHint" stdset="0">
       <size>
        <width>0</width>
        <height>0</height>
       </size>
      </property>
     </spacer>
    </item>
    <item row="1" column="1">
     <widget class="QPushButton" name="pushButtonNC1">
      <property name="sizePolicy">
       <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="minimumSize">
       <size>
        <width>0</width>
        <height>0</height>
       </size>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="checkable">
       <bool>false</bool>
      </property>
     </widget>
    </item>
    <item row="2" column="0">
     <spacer name="horizontalSpacer">
      <property name="orientation">
       <enum>Qt::Orientation::Horizontal</enum>
      </property>
      <property name="sizeHint" stdset="0">
       <size>
        <width>0</width>
        <height>0</height>
       </size>
      </property>
     </spacer>
    </item>
    <item row="2" column="1">
     <spacer name="verticalSpacer">
      <property name="orientation">
       <enum>Qt::Orientation::Vertical</enum>
      </property>
      <property name="sizeHint" stdset="0">
       <size>
        <width>0</width>
        <height>0</height>
       </size>
      </property>
     </spacer>
    </item>
    <item row="2" column="2">
     <spacer name="horizontalSpacer_2">
      <property name="orientation">
       <enum>Qt::Orientation::Horizontal</enum>
      </property>
      <property name="sizeHint" stdset="0">
       <size>
        <width>0</width>
        <height>0</height>
       </size>
      </property>
     </spacer>
    </item>
    <item row="3" column="1">
     <widget class="QPushButton" name="pushButtonNC2">
      <property name="sizePolicy">
       <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="minimumSize">
       <size>
        <width>0</width>
        <height>0</height>
       </size>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="checkable">
       <bool>false</bool>
      </property>
     </widget>
    </item>
    <item row="4" column="1">
     <spacer name="verticalSpacer_3">
      <property name="orientation">
       <enum>Qt::Orientation::Vertical</enum>
      </property>
      <property name="sizeHint" stdset="0">
       <size>
        <width>0</width>
        <height>0</height>
       </size>
      </property>
     </spacer>
    </item>
   </layout>
  </widget>
  <widget class="QLineEdit" name="lineEditNoiseReduction">
   <property name="geometry">
    <rect>
     <x>30</x>
     <y>20</y>
     <width>121</width>
     <height>21</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true"/>
   </property>
   <property name="text">
    <string>Noise Reduction</string>
   </property>
   <property name="alignment">
    <set>Qt::AlignmentFlag::AlignCenter</set>
   </property>
  </widget>
  <widget class="DialS1M5" name="dialNoiseReduction" native="true">
   <property name="geometry">
    <rect>
     <x>50</x>
     <y>50</y>
     <width>90</width>
     <height>41</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="minimumSize">
    <size>
     <width>0</width>
     <height>0</height>
    </size>
   </property>
  </widget>
  <widget class="QWidget" name="ncBypassWidget" native="true">
   <property name="geometry">
    <rect>
     <x>50</x>
     <y>390</y>
     <width>101</width>
     <height>61</height>
    </rect>
   </property>
   <layout class="QGridLayout" name="gridLayout_2" rowstretch="11,14,10" columnstretch="11,38,11">
    <property name="leftMargin">
     <number>0</number>
    </property>
    <property name="topMargin">
     <number>0</number>
    </property>
    <property name="rightMargin">
     <number>0</number>
    </property>
    <property name="bottomMargin">
     <number>0</number>
    </property>
    <property name="spacing">
     <number>0</number>
    </property>
    <item row="0" column="1">
     <spacer name="verticalSpacer_4">
      <property name="orientation">
       <enum>Qt::Orientation::Vertical</enum>
      </property>
      <property name="sizeHint" stdset="0">
       <size>
        <width>1</width>
        <height>1</height>
       </size>
      </property>
     </spacer>
    </item>
    <item row="1" column="0">
     <spacer name="horizontalSpacer_3">
      <property name="orientation">
       <enum>Qt::Orientation::Horizontal</enum>
      </property>
      <property name="sizeHint" stdset="0">
       <size>
        <width>1</width>
        <height>1</height>
       </size>
      </property>
     </spacer>
    </item>
    <item row="1" column="1">
     <widget class="QPushButton" name="pushButtonBypassNoise">
      <property name="sizePolicy">
       <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="checkable">
       <bool>true</bool>
      </property>
     </widget>
    </item>
    <item row="1" column="2">
     <spacer name="horizontalSpacer_4">
      <property name="orientation">
       <enum>Qt::Orientation::Horizontal</enum>
      </property>
      <property name="sizeHint" stdset="0">
       <size>
        <width>1</width>
        <height>1</height>
       </size>
      </property>
     </spacer>
    </item>
    <item row="2" column="1">
     <spacer name="verticalSpacer_5">
      <property name="orientation">
       <enum>Qt::Orientation::Vertical</enum>
      </property>
      <property name="sizeHint" stdset="0">
       <size>
        <width>1</width>
        <height>1</height>
       </size>
      </property>
     </spacer>
    </item>
   </layout>
  </widget>
 </widget>
 <customwidgets>
  <customwidget>
   <class>DialS1M5</class>
   <extends>QWidget</extends>
   <header location="global">dials1m5.h</header>
   <container>1</container>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
