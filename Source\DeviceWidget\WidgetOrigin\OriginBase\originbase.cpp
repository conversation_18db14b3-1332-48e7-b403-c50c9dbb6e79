#include "originbase.h"


// setter & getter
OriginBase& OriginBase::setChannelName(QString name)
{
    mChannelName = name;
    return *this;
}
OriginBase& OriginBase::setWidgetEnable(bool state)
{
    mEnable = state;
    return *this;
}
OriginBase& OriginBase::setWidgetEnableWithUpdate(bool state)
{
    mEnable = state;
    if(mEmitAction)
    {
        emit attributeChanged(this->objectName(), "Save_Enable", QString::number(mEnable));
    }
    updateAttribute();
    return *this;
}
OriginBase& OriginBase::setWidgetMovable(bool state)
{
    mMovable = state;
    return *this;
}
OriginBase& OriginBase::setWidgetReady(bool state)
{
    mReady = state;
    return *this;
}
OriginBase& OriginBase::setWidgetEmitAction(bool state)
{
    mEmitAction = state;
    return *this;
}

