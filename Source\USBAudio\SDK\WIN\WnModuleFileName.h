/************************************************************************
 *
 *  Module:       WnModuleFileName.h
 *
 *  Description:  Wrapper for a module filename
 *
 *  Runtime Env.: Win32
 *
 *  Author(s):
 *    <PERSON><PERSON>,  Udo<PERSON>@thesycon.de
 *    <PERSON>, <PERSON>@thesycon.de
 *
 *  Companies:
 *    Thesycon GmbH, Germany      http://www.thesycon.de
 *
 ************************************************************************/

#ifndef __WnModuleFileName_h__
#define __WnModuleFileName_h__

// optionally put everything into a namespace
#ifdef LIBWN_NAMESPACE
namespace LIBWN_NAMESPACE {
#endif

// set defaults (required for CE)
#ifndef _MAX_PATH
#define _MAX_PATH   260 /* max. length of full pathname */
#endif
#ifndef _MAX_DRIVE
#define _MAX_DRIVE  3   /* max. length of drive component */
#endif
#ifndef _MAX_DIR
#define _MAX_DIR    256 /* max. length of path component */
#endif
#ifndef _MAX_FNAME
#define _MAX_FNAME  256 /* max. length of file name component */
#endif
#ifndef _MAX_EXT
#define _MAX_EXT    256 /* max. length of extension component */
#endif


//
// WnModuleFileName
//
// Represents a module filename
//
class WnModuleFileName
{
public:
    // ctor
    WnModuleFileName();

    // returns true if path components are set
    bool
    IsValid();

    //
    // sets members to empty strings
    //
    void
    Clear();

    //
    // Retrieves the fully-qualified path for the file that contains the specified module.
    // The module must have been loaded by the current process.
    //
    // If this parameter is NULL, it is initialized with the path
    // of the executable file of the current process.
    //
    WNERR
    InitWithModuleHandle(HMODULE hModule = NULL);

    //
    // Init with a fully-qualified path
    //
    WNERR
    InitWithModuleName(const TCHAR* moduleName);



    // returns full path and name, e.g. "c:\dbg\Thesycon\my_dll.dll"
    const TCHAR*
    GetModuleName() const
        { return mModuleName; }


    // returns drive letter including ':', e.g. "c:"
    const TCHAR*
    GetDrive() const
        { return mDrive; }

    // returns directory including trailing slash, e.g. "\dbg\Thesycon\"
    const TCHAR*
    GetDir() const
        { return mDir; }

    // returns filename without extension,  e.g. "my_dll"
    const TCHAR*
    GetBaseName() const
        { return mBaseName; }

    // returns extension with '.', e.g. ".dll"
    const TCHAR*
    GetExtension() const
        { return mExtension; }

protected:

    void
    InitPathComponents();

    // ------------
    // Data Members
protected:

    // fully-qualified path
    TCHAR mModuleName[_MAX_PATH];
    // drive
    TCHAR mDrive[_MAX_DRIVE];
    // path
    TCHAR mDir[_MAX_DIR];
    // filename without extension
    TCHAR mBaseName[_MAX_FNAME];
    // extension with '.'
    TCHAR mExtension[_MAX_EXT];

}; // class WnModuleFileName

#ifdef LIBWN_NAMESPACE
}
#endif

#endif // __WnModuleFileName_h__

/***************************** EOF **************************************/
