/************************************************************************
 *  Module:       WnUserModeCrashDump.cpp
 *  Description:  Creates a dump if the user mode module crashes.
 *
 *  Runtime Env.: 32-/64-bit Windows
 *  Author(s):    Rene Moeller
 *  Company:      Thesycon GmbH, Germany      http://www.thesycon.de
 ************************************************************************/

#include "libwn_min_global.h"

// Module is empty if .h file was not included (category turned off).
#ifdef __WnUserModeCrashDump_h__

#include <sys/timeb.h>
#include <time.h>
#include <shlobj.h>

#pragma warning(push)
#pragma warning(disable:4091) //'typedef ': ignored on left of 'xxx' when no variable is declared
#include <dbghelp.h>
#pragma warning(pop)


// optionally put everything into a namespace
#ifdef LIBWN_NAMESPACE
namespace LIBWN_NAMESPACE {
#endif

//function pointer, taken from dbghelp.h
typedef
BOOL
WINAPI
F_MiniDumpWriteDump(
        HANDLE hProcess,
        DWORD ProcessId,
        HANDLE hFile,
        MINIDUMP_TYPE DumpType,
        PMINIDUMP_EXCEPTION_INFORMATION ExceptionParam,
        PMINIDUMP_USER_STREAM_INFORMATION UserStreamParam,
        PMINIDUMP_CALLBACK_INFORMATION CallbackParam
        );



// singleton
WnUserModeCrashDump WnUserModeCrashDump::sWnUserModeCrashDump;

// dtor
WnUserModeCrashDump::WnUserModeCrashDump()
{
    TB_ZERO_ARRAY(mPrefix);
    TB_ZERO_ARRAY(mFilePath);
    mFilePathRootId = -1;
    mIsInitialized = false;
}

// dtor
WnUserModeCrashDump::~WnUserModeCrashDump()
{
}


//static
void
WnUserModeCrashDump::Init(
    const TCHAR* prefix,
    const TCHAR* filePath,
    int filePathRootId
    )
{
    if ( sWnUserModeCrashDump.mIsInitialized ) {
        // already initialized
        WNTRACE(TRCINF,tprint(__FUNCTION__": already intialized\n"));
        return;
    }

    if (NULL == prefix) {
        // defaults to exe base name
        WnModuleFileName modFileName;
        WNERR err = modFileName.InitWithModuleHandle(NULL);
        if ( !SUCC(err) ) {
            TbStringNCopyToArray(sWnUserModeCrashDump.mPrefix, TEXT("failed"));
        } else {
            TbStringNCopyToArray(sWnUserModeCrashDump.mPrefix, modFileName.GetBaseName() );
        }
    } else {
        TbStringNCopyToArray(sWnUserModeCrashDump.mPrefix, prefix);
    }

    if (NULL == filePath) {
        TbStringNCopyToArray(sWnUserModeCrashDump.mFilePath, TEXT("%TEMP%"));
    } else {
        TbStringNCopyToArray(sWnUserModeCrashDump.mFilePath, filePath);
    }

    sWnUserModeCrashDump.mFilePathRootId = filePathRootId;

    // install our exception handler, this will replace any existing handler
    ::SetUnhandledExceptionFilter( TopLevelExceptionHandler );

    sWnUserModeCrashDump.mIsInitialized = true;
}


//static
LONG
WnUserModeCrashDump::TopLevelExceptionHandler(
    struct _EXCEPTION_POINTERS* exceptionPointers
    )
{
    //return value
    LONG retval = EXCEPTION_CONTINUE_SEARCH;

    //load the debug helper DLL
    WnLibrary dbgHelpDll;
    WNERR err = dbgHelpDll.Load(_T("DBGHELP.DLL"));
    if ( !SUCC(err) ) {
        //error, skip dump
        WNTRACE(TRCERR,tprint(__FUNCTION__": unabled to load DBGHELP.DLL\n"));
        return retval;
    }

    //Get the pointer to the required dump function.
    //Note: This function is not provided for Windows 2000. I.e. under Windows 2000 no dump is written.
    F_MiniDumpWriteDump* WriteDump = (F_MiniDumpWriteDump*)dbgHelpDll.GetProcAddress("MiniDumpWriteDump");
    if (WriteDump == NULL ) {
        //error, skip dump
        WNTRACE(TRCERR,tprint(__FUNCTION__": MiniDumpWriteDump function not available\n"));
        return retval;
    }

    //get current system time
    struct _timeb timeBuffer;
    errno_t result = _ftime_s( &timeBuffer );
    if ( result != 0 ) {
        //error, skip dump
        WNTRACE(TRCERR,tprint(__FUNCTION__": _ftime_s failed\n"));
        return retval;
    }

    //convert it to a usable structure
    struct tm localDateTime;
    ZeroMemory( &localDateTime, sizeof(localDateTime) );
    result = localtime_s( &localDateTime, &timeBuffer.time );
    if ( result != 0 ) {
        //error, skip dump
        WNTRACE(TRCERR,tprint(__FUNCTION__": localtime_s failed\n"));
        return retval;
    }

    //create the path of the dump file
    TCHAR filePath[ _MAX_PATH ] = { 0 };
    const TCHAR* dumpFilePath = sWnUserModeCrashDump.mFilePath;
    int dumpFilePathRootId = sWnUserModeCrashDump.mFilePathRootId;
    if ( dumpFilePathRootId == -1 && (dumpFilePath == NULL || _tcslen(dumpFilePath) == 0) ) {
        //get path and name of the current application
        WnModuleFileName modFileName;
        err = modFileName.InitWithModuleHandle(NULL);
        if ( !SUCC(err) ) {
            //error, skip dump
            return retval;
        }

        //create the current path
        result = _tmakepath_s( filePath, TB_ARRAY_ELEMENTS(filePath), modFileName.GetDrive(), modFileName.GetDir(), NULL, NULL );
        if (result != 0) {
            //error, skip dump
            WNTRACE(TRCERR,tprint(__FUNCTION__": _tmakepath_s failed\n"));
            return retval;
        }

    } else {
        if ( dumpFilePathRootId != -1 ) {
            // note: this creates a dependency from shell32.dll
            if ( !SHGetSpecialFolderPath(
                          NULL,                 //HWND hwndOwner,
                          filePath,             //LPTSTR lpszPath,
                          dumpFilePathRootId,   //int nFolder,
                          TRUE                  //BOOL fCreate
                          ) ) {
                //error, skip dump
                WNTRACE(TRCERR,tprint(__FUNCTION__": SHGetSpecialFolderPath failed\n"));
                return retval;
            }
            if ( dumpFilePath != NULL && _tcslen(dumpFilePath) > 0 ) {
                if (filePath[_tcslen(filePath) - 1] != _T('\\')) {
                    result =_tcsncat_s( filePath, TB_ARRAY_ELEMENTS( filePath ), _T("\\"), _tcslen(_T("\\")) );
                    if (result != 0) {
                        //error, skip dump
                        return retval;
                    }
                }
            }
        }

        if ( dumpFilePath != NULL && _tcslen(dumpFilePath) > 0 ) {
            result =_tcsncat_s( filePath, TB_ARRAY_ELEMENTS( filePath ), dumpFilePath, _tcslen(dumpFilePath) );
            if (result != 0) {
                //error, skip dump
                return retval;
            }
        }
    }

    //get the expanded file path string
    TCHAR expandedFilePath[ _MAX_PATH ] = { 0 };
    if ( ExpandEnvironmentStrings(
                filePath,         //LPCTSTR lpSrc,
                expandedFilePath, //LPTSTR lpDst,
                TB_ARRAY_ELEMENTS(expandedFilePath) //DWORD nSize
                ) == 0 ) {
        //error, skip dump
        WNTRACE(TRCERR,tprint(__FUNCTION__": ExpandEnvironmentStrings failed\n"));
        return retval;
    }

    //get the length of the path
    size_t pathLen = _tcslen(expandedFilePath);
    if (pathLen <= 0) {
        //invalid path, cleanup and skip the dump
        return retval;
    }

    //create the file path if required
    TCHAR pathToCreate[ _MAX_PATH ] = { 0 };
    TCHAR* searchStr = expandedFilePath;
    TCHAR* foundPtr = NULL;
    for (;;) {
        //find the next backslash
        foundPtr = _tcschr( searchStr, '\\' );
        if (foundPtr == NULL) {
            if (searchStr == expandedFilePath) {
                //The path is invalid, because it doesn't contain any backslash.
                //Cleanup and skip the dump.
                return retval;
            } else {
                //Set the pointer of the terminating NULL.
                foundPtr = &expandedFilePath[pathLen];
            }
        }

        //extend the path to create with the next directory
        _tcsncat_s( pathToCreate, TB_ARRAY_ELEMENTS( pathToCreate ), searchStr, foundPtr - searchStr);

        //Double backslash or starting backslash found?
        if ((foundPtr - searchStr) > 1) {

            //NO: create the directory
            //Note: We ignore any error because some returned errors codes are ambiguous,
            //i.e. they are used for expected 'error' cases as well as for real errors. In
            //some cases such error codes are returned for the first parts of a path and
            //the missing directories later in this path are created anyway correctly, i.e.
            //the creation of the full path succeeds (e.g. a network path). In other cases
            //the creation of the full path fails. This is not critical because the following
            //'CreateFile' will detect a missing path and will return an error.
            CreateDirectory(
                pathToCreate,   //_in_opt LPCTSTR lpPathName,
                NULL            //__in LPSECURITY_ATTRIBUTES lpSecurityAttributes
                );
        }

        if (foundPtr == &expandedFilePath[pathLen]) {
            //end of the path reached
            break;
        } else {
            //extend the path to create with the found backslash
            _tcsncat_s( pathToCreate, TB_ARRAY_ELEMENTS( pathToCreate ), foundPtr, 1);
            //set the starting point for the rest of the path to examine
            searchStr = ++foundPtr;
        }
    }

    //create the path and name of the dump file
    TCHAR dumpFilePathAndName[ _MAX_PATH ] = { 0 };
    _sntprintf_s( dumpFilePathAndName, TB_ARRAY_ELEMENTS(dumpFilePathAndName), _TRUNCATE, TEXT("%s\\%s_%d%02d%02d%02d%02d%02d.dmp"),
                      expandedFilePath, sWnUserModeCrashDump.mPrefix, (localDateTime.tm_year + 1900), (localDateTime.tm_mon + 1),
                      localDateTime.tm_mday, localDateTime.tm_hour, localDateTime.tm_min, localDateTime.tm_sec);

    //create the dump file
    HANDLE fileHandle = ::CreateFile(
                                dumpFilePathAndName,    //LPCTSTR lpFileName,
                                GENERIC_WRITE,          //DWORD dwDesiredAccess,
                                FILE_SHARE_WRITE,       //DWORD dwShareMode,
                                NULL,                   //LPSECURITY_ATTRIBUTES lpSecurityAttributes,
                                CREATE_ALWAYS,          //DWORD dwCreationDisposition,
                                FILE_ATTRIBUTE_NORMAL,  //DWORD dwFlagsAndAttributes,
                                NULL                    //HANDLE hTemplateFile
                                );
    if ( fileHandle == INVALID_HANDLE_VALUE ) {
        WNTRACE(TRCERR,tprint(__FUNCTION__": CreateFile failed, err=0x%X\n", ::GetLastError()));
        //error, skip dump
        return retval;
    }

    //describe the exception that caused the dump to be generated
    MINIDUMP_EXCEPTION_INFORMATION exceptionInfo;
    ZeroMemory(&exceptionInfo, sizeof(MINIDUMP_EXCEPTION_INFORMATION));
    exceptionInfo.ThreadId = ::GetCurrentThreadId();
    exceptionInfo.ExceptionPointers = exceptionPointers;
    exceptionInfo.ClientPointers = FALSE;

    //create the dump
    if ( !WriteDump(
                ::GetCurrentProcess(),      //HANDLE hProcess,
                ::GetCurrentProcessId(),    //DWORD ProcessId,
                fileHandle,                 //HANDLE hFile,
                MiniDumpWithFullMemory,     //MINIDUMP_TYPE DumpType,
                &exceptionInfo,             //PMINIDUMP_EXCEPTION_INFORMATION ExceptionParam,
                NULL,                       //PMINIDUMP_USER_STREAM_INFORMATION UserStreamParam,
                NULL                        //PMINIDUMP_CALLBACK_INFORMATION CallbackParam
                ) ) {
        WNTRACE(TRCERR,tprint(__FUNCTION__": WriteDump failed, err=0x%X\n", ::GetLastError()));
        //cleanup
        ::CloseHandle( fileHandle );
        //error, skip dump
        return retval;
    }

    //close file handle
    ::CloseHandle( fileHandle );

    WNTRACE(TRCWRN,tprint( TEXT(__FUNCTION__) TEXT(": Crash dump written to %s\n"), dumpFilePathAndName));

    return retval;
}

#ifdef LIBWN_NAMESPACE
}
#endif

#endif

/************************************ EOF *******************************/
