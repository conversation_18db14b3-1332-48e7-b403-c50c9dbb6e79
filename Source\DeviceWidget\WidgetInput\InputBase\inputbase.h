#ifndef INPUTBASE_H
#define INPUTBASE_H


#include <QWidget>

#include "solo.h"


class InputBase : public Solo
{
    Q_OBJECT
public:
    explicit InputBase(QWidget* parent=nullptr);
    virtual ~InputBase() = default;
    InputBase& handleFieldSoloStateChanged(int state);
    InputBase& setChannelName(QString name);
    InputBase& setWidgetEnable(bool state=true);
    InputBase& setWidgetEnableWithUpdate(bool state=true);
    InputBase& setWidgetMovable(bool state=true);
    InputBase& setWidgetReady(bool state=true);
    InputBase& setWidgetEmitAction(bool state=true);
    QString getChannelName() { return mChannelName; }
    bool isWidgetEnable() { return mEnable; }
    bool isWidgetMovable() { return mMovable; }
    bool isWidgetReady() { return mReady; }
    bool isWidgetEmitAction() { return mEmitAction; }
protected:
    virtual void updateAttribute() = 0;
private:
    QString mChannelName="";
    bool mEnable=false;
    bool mMovable=false;
    bool mReady=false;
    bool mEmitAction=false;
private slots:
    void in_widgetBase_soloStateChanged(QString objectName, bool state);
signals:
    void attributeChanged(QString objectName, QString attribute, QString value);
};


#endif // INPUTBASE_H

