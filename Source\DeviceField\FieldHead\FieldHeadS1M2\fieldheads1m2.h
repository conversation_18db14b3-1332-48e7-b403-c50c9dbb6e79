#ifndef FIELDHEADS1M2_H
#define FIELDHEADS1M2_H


#include "appsettings.h"
#include "fieldheadbase2.h"


class FieldHeadS1M2 : public FieldHeadBase2, public AppSettingsObserver
{
    Q_OBJECT
public:
    explicit FieldHeadS1M2(QWidget* parent=nullptr, QString name="");
    ~FieldHeadS1M2();
    FieldHeadS1M2& setName(QString name);
protected:
    void AppSettingsChanged(QString objectName, QString attribute, QString value) override;
private slots:
    void in_widgetBase_attributeChanged(QString objectName, QString attribute, QString value);
signals:
    void attributeChanged(QString objectName, QString attribute, QString value);
};


#endif // FIELDHEADS1M2_H

