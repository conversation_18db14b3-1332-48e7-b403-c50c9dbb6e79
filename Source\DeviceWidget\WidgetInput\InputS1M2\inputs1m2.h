#ifndef INPUTS1M2_H
#define INPUTS1M2_H


#include <QFont>
#include <QEvent>
#include <QTimer>
#include <QObject>
#include <QWidget>
#include <QResizeEvent>

#include "inputbase.h"
#include "workspace.h"
#include "appsettings.h"
#include "autogains1m1.h"


namespace Ui {
class InputS1M2;
}


class InputS1M2 : public InputBase, public WorkspaceObserver, public AppSettingsObserver
{
    Q_OBJECT
public:
    explicit InputS1M2(QWidget* parent=nullptr, QString name="");
    ~InputS1M2();
    InputS1M2& setName(QString name);
    InputS1M2& setFont(QFont font);
    InputS1M2& setVolumeMeter(int value);
    InputS1M2& setVolumeMeterClear();
    InputS1M2& setVolumeMeterSlip();
    InputS1M2& setGain(float value);
    InputS1M2& setGainLock(bool state=true);
    InputS1M2& setMuteAffectGain(bool state=true);
    InputS1M2& setGainAffectMute(bool state=true);
    InputS1M2& setGainRange(float min, float max);
    InputS1M2& setGainDefault(float value);
    InputS1M2& setGainWidgetDisable(float value);
    InputS1M2& setBalanceDefault(float value);
    InputS1M2& setChannelNameEditable(bool state=true);
    InputS1M2& setValue48V(bool state=true);
    InputS1M2& setValueDucking(bool state=true);
    InputS1M2& setValueEQ(bool state=true);
    InputS1M2& setValueGAIN(float value);
    InputS1M2& setValueMUTE(bool state=true);
    InputS1M2& setOverlay(bool state=true);
    InputS1M2& setAutoGainChannelString(QHash<QString, QString>&& string);
protected:
    bool eventFilter(QObject* obj, QEvent* e) override;
    void resizeEvent(QResizeEvent* e) override;
    void updateAttribute() override;
    void setSoloState(bool state) override;
    void setSoloStateLeft(bool state) override;
    void setSoloStateRight(bool state) override;
    void setMuteState(bool state) override;
    void setMuteStateLeft(bool state) override;
    void setMuteStateRight(bool state) override;
    void setSoloClicked(bool state) override;
    void setSoloClickedLeft(bool state) override;
    void setSoloClickedRight(bool state) override;
    void setMuteClicked(bool state) override;
    void setMuteClickedLeft(bool state) override;
    void setMuteClickedRight(bool state) override;
    bool getSoloState() override;
    bool getSoloStateLeft() override;
    bool getSoloStateRight() override;
    bool getMuteState() override;
    bool getMuteStateLeft() override;
    bool getMuteStateRight() override;
    void loadSettings() override;
    void AppSettingsChanged(QString objectName, QString attribute, QString value) override;
private:
    Ui::InputS1M2* ui;
    AutoGainS1M1 mAutoGain;
    QTimer mTimer;
    QFont mFont;
    int mBalanceDefault=100;
    int mPre48V=-2147483648;
    int mPreMUTE=-2147483648;
    float mPreGAIN=-2147483648;
    float mDisableGAIN=-88;
    bool mMuteAffectGain=false;
    bool mGainAffectMute=false;
    void save(QAnyStringView key, const QVariant& value);
private slots:
    void in_mAutoGain_attributeChanged(QString attribute, QString value);
    void in_mTimer_timeout();
    void in_widgetPushButtonGroup1_stateChanged(QString button, QString state);
    void in_widgetPushButtonGroup2_stateChanged(QString button, QString state);
    void in_widgetDial_valueChanged(float value);
    void in_widgetHSlider_valueChanged(float value);
    void on_lineEdit_textChanged(const QString& arg1);
    void on_lineEdit_editingFinished();
    void on_pushButtonClose_clicked();
};


#endif // INPUTS1M2_H

