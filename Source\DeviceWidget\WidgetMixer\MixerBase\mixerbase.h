#ifndef MIXERBASE_H
#define MIXERBASE_H


#include <QWidget>

#include "solo.h"


class MixerBase : public Solo
{
    Q_OBJECT
public:
    explicit MixerBase(QWidget* parent=nullptr);
    virtual ~MixerBase() = default;
    MixerBase& assignMixer(QString* mixer);
    MixerBase& assignMixerList(QVector<QString>* mixerList);
    MixerBase& assignOriginSoloState(QHash<QString, int>* originSoloState);
    MixerBase& assignOriginVisibleList(QHash<QString, QString>* originVisibleList);
    MixerBase& handleFieldSoloStateChanged(int state);
    MixerBase& setChannelName(QString name);
    MixerBase& setWidgetEnable(bool state=true);
    MixerBase& setWidgetEnableWithUpdate(bool state=true);
    MixerBase& setWidgetMovable(bool state=true);
    MixerBase& setWidgetReady(bool state=true);
    MixerBase& setWidgetEmitAction(bool state=true);
    QString getMixer() { return *mMixer; }
    QVector<QString> getMixerList() { return *mMixerList; }
    int getOriginSoloState(QString mixer);
    QString getOriginVisibleList(QString mixer);
    QString getChannelName() { return mChannelName; }
    bool isWidgetEnable() { return mEnable; }
    bool isWidgetMovable() { return mMovable; }
    bool isWidgetReady() { return mReady; }
    bool isWidgetEmitAction() { return mEmitAction; }
    virtual void handleFieldMixerChanged(QString mixer) = 0;
protected:
    virtual void updateAttribute() = 0;
private:
    QString* mMixer=nullptr;
    QVector<QString>* mMixerList=nullptr;
    QHash<QString, int>* mOriginSoloState=nullptr;
    QHash<QString, QString>* mOriginVisibleList=nullptr;
    QString mChannelName="";
    bool mEnable=false;
    bool mMovable=false;
    bool mReady=false;
    bool mEmitAction=false;
private slots:
    void in_widgetBase_soloStateChanged(QString objectName, bool state);
signals:
    void attributeChanged(QString objectName, QString attribute, QString value);
};


#endif // MIXERBASE_H

