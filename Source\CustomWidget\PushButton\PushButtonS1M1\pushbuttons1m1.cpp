#include "globalfont.h"
#include "pushbuttons1m1.h"


PushButtonS1M1::PushButtonS1M1(QWidget* parent)
    : QWidget(parent)
{
    mPushButtonMic1.setParent(this);
    mPushButtonMic35.setParent(this);
    mPushButtonMicHP.setParent(this);
    mPushButton48V.setParent(this);
    mPushButtonAUTO.setParent(this);
    mPushButtonDucking.setParent(this);
    mLabelMic1.setParent(this);
    mLabelMic35.setParent(this);
    mLabelMicHP.setParent(this);
    QString style;
    style = "QLabel {"
            "   background-color: transparent;"
            "   image: url(:/Icon/VerticalRoundedRectBlack.png);"
            "}";
    mLabelMic1.setStyleSheet(style);
    mLabelMic35.setStyleSheet(style);
    mLabelMicHP.setStyleSheet(style);
    style = "QPushButton {"
            "   text-align: left;"
            "   color: rgb(161, 161, 161);"
            "   background-color: transparent;"
            "}"
            "QPushButton:hover {"
            "   color: rgb(255, 255, 255);"
            "}";
    mPushButtonMic1.setStyleSheet(style);
    mPushButtonMic35.setStyleSheet(style);
    mPushButtonMicHP.setStyleSheet(style);
    style = "QPushButton {"
            "   border-radius: 3px;"
            "   color: rgb(161, 161, 161);"
            "   background-color: rgb(60, 60, 60);"
            "}"
            "QPushButton:hover {"
            "   border: 2px solid gray;"
            "   border-radius: 3px;"
            "}";
    mPushButton48V.setStyleSheet(style);
    mPushButtonAUTO.setStyleSheet(style);
    mPushButtonDucking.setStyleSheet(style);
    mPushButtonMic1.setText("Mic 1");
    mPushButtonMic35.setText("Mic-3.5");
    mPushButtonMicHP.setText("Mic-HP");
    mPushButton48V.setText("48V");
    mPushButtonAUTO.setText("AUTO");
    mPushButtonDucking.setText("Ducking");
    connect(&mPushButtonMic1, SIGNAL(clicked()), this, SLOT(in_mPushButtonMic1_clicked()), Qt::UniqueConnection);
    connect(&mPushButtonMic35, SIGNAL(clicked()), this, SLOT(in_mPushButtonMic35_clicked()), Qt::UniqueConnection);
    connect(&mPushButtonMicHP, SIGNAL(clicked()), this, SLOT(in_mPushButtonMicHP_clicked()), Qt::UniqueConnection);
    connect(&mPushButton48V, SIGNAL(clicked()), this, SLOT(in_mPushButton48V_clicked()), Qt::UniqueConnection);
    connect(&mPushButtonAUTO, SIGNAL(clicked()), this, SLOT(in_mPushButtonAUTO_clicked()), Qt::UniqueConnection);
    connect(&mPushButtonDucking, SIGNAL(clicked()), this, SLOT(in_mPushButtonDucking_clicked()), Qt::UniqueConnection);
}
PushButtonS1M1::~PushButtonS1M1()
{

}


// override
void PushButtonS1M1::resizeEvent(QResizeEvent* e)
{
    Q_UNUSED(e);
    // W
    float wPixelPerRatio=size().width() / 100.0;
    int wLabel=wPixelPerRatio * 6;
    int wSpace1=wPixelPerRatio * 6;
    int wPushButton=size().width();
    int xLabel=0;
    int xPushButton=xLabel + wLabel + wSpace1;
    // H
    float hPixelPerRatio=size().height() / 100.0;
    int hSpace1=hPixelPerRatio * 15;
    int hSpace2=hPixelPerRatio * 3;
    int hSpace3=hPixelPerRatio * 10;
    int hSpace4=hPixelPerRatio * 5;
    int hPushButton=(size().height() - hSpace1 - hSpace2 * 3 - hSpace3 - hSpace4) / 6;
    mLabelMic1.setGeometry(xLabel + wPixelPerRatio * 10, hSpace1, wLabel, hPushButton);
    mPushButtonMic1.setGeometry(xPushButton + wPixelPerRatio * 10, hSpace1, wPushButton, hPushButton);
    mLabelMic35.setGeometry(xLabel + wPixelPerRatio * 10, hSpace1 + hSpace2 + hPushButton, wLabel, hPushButton);
    mPushButtonMic35.setGeometry(xPushButton + wPixelPerRatio * 10, hSpace1 + hPushButton + hSpace2, wPushButton, hPushButton);
    mLabelMicHP.setGeometry(xLabel + wPixelPerRatio * 10, hSpace1 + hSpace2 * 2 + hPushButton * 2, wLabel, hPushButton);
    mPushButtonMicHP.setGeometry(xPushButton + wPixelPerRatio * 10, hSpace1 + hSpace2 * 2 + hPushButton * 2, wPushButton, hPushButton);
    mPushButton48V.setGeometry(xLabel, hSpace1 + hSpace2 * 2 + hSpace3 + hPushButton * 3, wPushButton, hPushButton);
    mPushButtonAUTO.setGeometry(xLabel, hSpace1 + hSpace2 * 3 + hSpace3 + hPushButton * 4, wPushButton, hPushButton);
    mPushButtonDucking.setGeometry(xLabel, hSpace1 + hSpace2 * 4 + hSpace3 + hPushButton * 5, wPushButton, hPushButton);
    mFont.setPointSize(GLBFHandle.getSuitablePointSize(mFont, mPushButtonMic35.height()) - 1);
    mPushButtonMic1.setFont(mFont);
    mPushButtonMic35.setFont(mFont);
    mPushButtonMicHP.setFont(mFont);
    mFont.setPointSize(GLBFHandle.getSuitablePointSize(mFont, mPushButtonDucking.text(), mPushButtonDucking.rect()) - 1);
    mPushButton48V.setFont(mFont);
    mPushButtonAUTO.setFont(mFont);
    mPushButtonDucking.setFont(mFont);
    mRadius = hPushButton / 2 * 0.8;
    setPushButtonState48V(mPushButtonState48V);
    setPushButtonStateAUTO(mPushButtonStateAUTO);
    setPushButtonStateDucking(mPushButtonStateDucking);
}


// slot
void PushButtonS1M1::in_mPushButtonMic1_clicked()
{
    QString style;
    if(!mPushButtonStateMic1)
    {
        mPushButtonStateMic1 = true;
        mPushButtonStateMic35 = false;
        mPushButtonStateMicHP = false;
        style = "QLabel {"
                "   background-color: transparent;"
                "   image: url(:/Icon/VerticalRoundedRectGreen.png);"
                "}";
        mLabelMic1.setStyleSheet(style);
        style = "QLabel {"
                "   background-color: transparent;"
                "   image: url(:/Icon/VerticalRoundedRectBlack.png);"
                "}";
        mLabelMic35.setStyleSheet(style);
        mLabelMicHP.setStyleSheet(style);
        style = "QPushButton {"
                "   text-align: left;"
                "   color: rgb(255, 255, 255);"
                "   background-color: transparent;"
                "}"
                "QPushButton:hover {"
                "   color: rgb(255, 255, 255);"
                "}";
        mPushButtonMic1.setStyleSheet(style);
        style = "QPushButton {"
                "   text-align: left;"
                "   color: rgb(161, 161, 161);"
                "   background-color: transparent;"
                "}"
                "QPushButton:hover {"
                "   color: rgb(255, 255, 255);"
                "}";
        mPushButtonMic35.setStyleSheet(style);
        mPushButtonMicHP.setStyleSheet(style);
        emit buttonStateChanged(buttonMic1, true);
    }
}
void PushButtonS1M1::in_mPushButtonMic35_clicked()
{
    QString style;
    if(!mPushButtonStateMic35)
    {
        mPushButtonStateMic35 = true;
        mPushButtonStateMic1 = false;
        mPushButtonStateMicHP = false;
        style = "QLabel {"
                "   background-color: transparent;"
                "   image: url(:/Icon/VerticalRoundedRectGreen.png);"
                "}";
        mLabelMic35.setStyleSheet(style);
        style = "QLabel {"
                "   background-color: transparent;"
                "   image: url(:/Icon/VerticalRoundedRectBlack.png);"
                "}";
        mLabelMic1.setStyleSheet(style);
        mLabelMicHP.setStyleSheet(style);
        style = "QPushButton {"
                "   text-align: left;"
                "   color: rgb(255, 255, 255);"
                "   background-color: transparent;"
                "}"
                "QPushButton:hover {"
                "   color: rgb(255, 255, 255);"
                "}";
        mPushButtonMic35.setStyleSheet(style);
        style = "QPushButton {"
                "   text-align: left;"
                "   color: rgb(161, 161, 161);"
                "   background-color: transparent;"
                "}"
                "QPushButton:hover {"
                "   color: rgb(255, 255, 255);"
                "}";
        mPushButtonMic1.setStyleSheet(style);
        mPushButtonMicHP.setStyleSheet(style);
        emit buttonStateChanged(buttonMic35, true);
    }
}
void PushButtonS1M1::in_mPushButtonMicHP_clicked()
{
    QString style;
    if(!mPushButtonStateMicHP)
    {
        mPushButtonStateMicHP = true;
        mPushButtonStateMic1 = false;
        mPushButtonStateMic35 = false;
        style = "QLabel {"
                "   background-color: transparent;"
                "   image: url(:/Icon/VerticalRoundedRectGreen.png);"
                "}";
        mLabelMicHP.setStyleSheet(style);
        style = "QLabel {"
                "   background-color: transparent;"
                "   image: url(:/Icon/VerticalRoundedRectBlack.png);"
                "}";
        mLabelMic1.setStyleSheet(style);
        mLabelMic35.setStyleSheet(style);
        style = "QPushButton {"
                "   text-align: left;"
                "   color: rgb(255, 255, 255);"
                "   background-color: transparent;"
                "}"
                "QPushButton:hover {"
                "   color: rgb(255, 255, 255);"
                "}";
        mPushButtonMicHP.setStyleSheet(style);
        style = "QPushButton {"
                "   text-align: left;"
                "   color: rgb(161, 161, 161);"
                "   background-color: transparent;"
                "}"
                "QPushButton:hover {"
                "   color: rgb(255, 255, 255);"
                "}";
        mPushButtonMic1.setStyleSheet(style);
        mPushButtonMic35.setStyleSheet(style);
        emit buttonStateChanged(buttonMicHP, true);
    }
}
void PushButtonS1M1::in_mPushButton48V_clicked()
{
    QString style;
    mPushButtonState48V = !mPushButtonState48V;
    if(mPushButtonState48V)
    {
        style = QString("QPushButton {"
                "   border-radius: %1px;"
                "   color: rgb(229, 229, 229);"
                "   background-color: rgb(234, 78, 80);"
                "}"
                "QPushButton:hover {"
                "   border: 2px solid gray;"
                "   border-radius: %1px;"
                "}").arg(mRadius);
    }
    else
    {
        style = QString("QPushButton {"
                "   border-radius: %1px;"
                "   color: rgb(161, 161, 161);"
                "   background-color: rgb(60, 60, 60);"
                "}"
                "QPushButton:hover {"
                "   border: 2px solid gray;"
                "   border-radius: %1px;"
                "}").arg(mRadius);
    }
    mPushButton48V.setStyleSheet(style);
    emit buttonStateChanged(button48V, mPushButtonState48V);
}
void PushButtonS1M1::in_mPushButtonAUTO_clicked()
{
    QString style;
    mPushButtonStateAUTO = !mPushButtonStateAUTO;
    if(mPushButtonStateAUTO)
    {
        style = QString("QPushButton {"
                "   border-radius: %1px;"
                "   color: rgb(229, 229, 229);"
                "   background-color: rgb(129, 171, 84);"
                "}"
                "QPushButton:hover {"
                "   border: 2px solid gray;"
                "   border-radius: %1px;"
                "}").arg(mRadius);
    }
    else
    {
        style = QString("QPushButton {"
                "   border-radius: %1px;"
                "   color: rgb(161, 161, 161);"
                "   background-color: rgb(60, 60, 60);"
                "}"
                "QPushButton:hover {"
                "   border: 2px solid gray;"
                "   border-radius: %1px;"
                "}").arg(mRadius);
    }
    mPushButtonAUTO.setStyleSheet(style);
    emit buttonStateChanged(buttonAUTO, mPushButtonStateAUTO);
}
void PushButtonS1M1::in_mPushButtonDucking_clicked()
{
    QString style;
    mPushButtonStateDucking = !mPushButtonStateDucking;
    if(mPushButtonStateDucking)
    {
        style = QString("QPushButton {"
                "   border-radius: %1px;"
                "   color: rgb(229, 229, 229);"
                "   background-color: rgb(97, 173, 194);"
                "}"
                "QPushButton:hover {"
                "   border: 2px solid gray;"
                "   border-radius: %1px;"
                "}").arg(mRadius);
    }
    else
    {
        style = QString("QPushButton {"
                "   border-radius: %1px;"
                "   color: rgb(161, 161, 161);"
                "   background-color: rgb(60, 60, 60);"
                "}"
                "QPushButton:hover {"
                "   border: 2px solid gray;"
                "   border-radius: %1px;"
                "}").arg(mRadius);
    }
    mPushButtonDucking.setStyleSheet(style);
    emit buttonStateChanged(buttonDucking, mPushButtonStateDucking);
}


// setter & getter
PushButtonS1M1& PushButtonS1M1::setFont(QFont font)
{
    mFont = font;
    QResizeEvent e(size(), size());
    resizeEvent(&e);
    update();
    return *this;
}
PushButtonS1M1& PushButtonS1M1::setPushButtonStateMic1(bool state)
{
    Q_UNUSED(state);
    QString style;
    mPushButtonStateMic1 = true;
    mPushButtonStateMic35 = false;
    mPushButtonStateMicHP = false;
    style = "QLabel {"
            "   background-color: transparent;"
            "   image: url(:/Icon/VerticalRoundedRectGreen.png);"
            "}";
    mLabelMic1.setStyleSheet(style);
    style = "QLabel {"
            "   background-color: transparent;"
            "   image: url(:/Icon/VerticalRoundedRectBlack.png);"
            "}";
    mLabelMic35.setStyleSheet(style);
    mLabelMicHP.setStyleSheet(style);
    style = "QPushButton {"
            "   text-align: left;"
            "   color: rgb(255, 255, 255);"
            "   background-color: transparent;"
            "}"
            "QPushButton:hover {"
            "   color: rgb(255, 255, 255);"
            "}";
    mPushButtonMic1.setStyleSheet(style);
    style = "QPushButton {"
            "   text-align: left;"
            "   color: rgb(161, 161, 161);"
            "   background-color: transparent;"
            "}"
            "QPushButton:hover {"
            "   color: rgb(255, 255, 255);"
            "}";
    mPushButtonMic35.setStyleSheet(style);
    mPushButtonMicHP.setStyleSheet(style);
    return *this;
}
PushButtonS1M1& PushButtonS1M1::setPushButtonStateMic35(bool state)
{
    Q_UNUSED(state);
    QString style;
    mPushButtonStateMic35 = true;
    mPushButtonStateMic1 = false;
    mPushButtonStateMicHP = false;
    style = "QLabel {"
            "   background-color: transparent;"
            "   image: url(:/Icon/VerticalRoundedRectGreen.png);"
            "}";
    mLabelMic35.setStyleSheet(style);
    style = "QLabel {"
            "   background-color: transparent;"
            "   image: url(:/Icon/VerticalRoundedRectBlack.png);"
            "}";
    mLabelMic1.setStyleSheet(style);
    mLabelMicHP.setStyleSheet(style);
    style = "QPushButton {"
            "   text-align: left;"
            "   color: rgb(255, 255, 255);"
            "   background-color: transparent;"
            "}"
            "QPushButton:hover {"
            "   color: rgb(255, 255, 255);"
            "}";
    mPushButtonMic35.setStyleSheet(style);
    style = "QPushButton {"
            "   text-align: left;"
            "   color: rgb(161, 161, 161);"
            "   background-color: transparent;"
            "}"
            "QPushButton:hover {"
            "   color: rgb(255, 255, 255);"
            "}";
    mPushButtonMic1.setStyleSheet(style);
    mPushButtonMicHP.setStyleSheet(style);
    return *this;
}
PushButtonS1M1& PushButtonS1M1::setPushButtonStateMicHP(bool state)
{
    Q_UNUSED(state);
    QString style;
    mPushButtonStateMicHP = true;
    mPushButtonStateMic1 = false;
    mPushButtonStateMic35 = false;
    style = "QLabel {"
            "   background-color: transparent;"
            "   image: url(:/Icon/VerticalRoundedRectGreen.png);"
            "}";
    mLabelMicHP.setStyleSheet(style);
    style = "QLabel {"
            "   background-color: transparent;"
            "   image: url(:/Icon/VerticalRoundedRectBlack.png);"
            "}";
    mLabelMic1.setStyleSheet(style);
    mLabelMic35.setStyleSheet(style);
    style = "QPushButton {"
            "   text-align: left;"
            "   color: rgb(255, 255, 255);"
            "   background-color: transparent;"
            "}"
            "QPushButton:hover {"
            "   color: rgb(255, 255, 255);"
            "}";
    mPushButtonMicHP.setStyleSheet(style);
    style = "QPushButton {"
            "   text-align: left;"
            "   color: rgb(161, 161, 161);"
            "   background-color: transparent;"
            "}"
            "QPushButton:hover {"
            "   color: rgb(255, 255, 255);"
            "}";
    mPushButtonMic1.setStyleSheet(style);
    mPushButtonMic35.setStyleSheet(style);
    return *this;
}
PushButtonS1M1& PushButtonS1M1::setPushButtonState48V(bool state)
{
    QString style;
    mPushButtonState48V = state;
    if(mPushButtonState48V)
    {
        style = QString("QPushButton {"
                "   border-radius: %1px;"
                "   color: rgb(229, 229, 229);"
                "   background-color: rgb(234, 78, 80);"
                "}"
                "QPushButton:hover {"
                "   border: 2px solid gray;"
                "   border-radius: %1px;"
                "}").arg(mRadius);
    }
    else
    {
        style = QString("QPushButton {"
                "   border-radius: %1px;"
                "   color: rgb(161, 161, 161);"
                "   background-color: rgb(60, 60, 60);"
                "}"
                "QPushButton:hover {"
                "   border: 2px solid gray;"
                "   border-radius: %1px;"
                "}").arg(mRadius);
    }
    mPushButton48V.setStyleSheet(style);
    return *this;
}
PushButtonS1M1& PushButtonS1M1::setPushButtonStateAUTO(bool state)
{
    QString style;
    mPushButtonStateAUTO = state;
    if(mPushButtonStateAUTO)
    {
        style = QString("QPushButton {"
                "   border-radius: %1px;"
                "   color: rgb(229, 229, 229);"
                "   background-color: rgb(129, 171, 84);"
                "}"
                "QPushButton:hover {"
                "   border: 2px solid gray;"
                "   border-radius: %1px;"
                "}").arg(mRadius);
    }
    else
    {
        style = QString("QPushButton {"
                "   border-radius: %1px;"
                "   color: rgb(161, 161, 161);"
                "   background-color: rgb(60, 60, 60);"
                "}"
                "QPushButton:hover {"
                "   border: 2px solid gray;"
                "   border-radius: %1px;"
                "}").arg(mRadius);
    }
    mPushButtonAUTO.setStyleSheet(style);
    return *this;
}
PushButtonS1M1& PushButtonS1M1::setPushButtonStateDucking(bool state)
{
    QString style;
    mPushButtonStateDucking = state;
    if(mPushButtonStateDucking)
    {
        style = QString("QPushButton {"
                "   border-radius: %1px;"
                "   color: rgb(229, 229, 229);"
                "   background-color: rgb(97, 173, 194);"
                "}"
                "QPushButton:hover {"
                "   border: 2px solid gray;"
                "   border-radius: %1px;"
                "}").arg(mRadius);
    }
    else
    {
        style = QString("QPushButton {"
                "   border-radius: %1px;"
                "   color: rgb(161, 161, 161);"
                "   background-color: rgb(60, 60, 60);"
                "}"
                "QPushButton:hover {"
                "   border: 2px solid gray;"
                "   border-radius: %1px;"
                "}").arg(mRadius);
    }
    mPushButtonDucking.setStyleSheet(style);
    return *this;
}
bool PushButtonS1M1::getPushButtonStateMic1()
{
    return mPushButtonStateMic1;
}
bool PushButtonS1M1::getPushButtonStateMic35()
{
    return mPushButtonStateMic35;
}
bool PushButtonS1M1::getPushButtonStateMicHP()
{
    return mPushButtonStateMicHP;
}
bool PushButtonS1M1::getPushButtonState48V()
{
    return mPushButtonState48V;
}
bool PushButtonS1M1::getPushButtonStateAUTO()
{
    return mPushButtonStateAUTO;
}
bool PushButtonS1M1::getPushButtonStateDucking()
{
    return mPushButtonStateDucking;
}

