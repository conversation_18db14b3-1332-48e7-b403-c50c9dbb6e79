#ifndef PUSHBUTTONS1M15_H
#define PUSHBUTTONS1M15_H

#include <QPushButton>

class QGraphicsDropShadowEffect;
class PushButtonS1M15 : public QPushButton
{
    Q_OBJECT
public:
    explicit PushButtonS1M15(QWidget* parent=nullptr);
    ~PushButtonS1M15(){}
    void setShadowVisible(bool isVisible);
    void setShadowColor(QColor color);
    void setShadowRadius(int radius);
    void setShadowOffset(QPoint offset);

private:
    QGraphicsDropShadowEffect* m_shadowEffect;
};


#endif // PUSHBUTTONS1M15_H

