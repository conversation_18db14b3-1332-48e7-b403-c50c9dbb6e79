/************************************************************************

    Description:
        TLDFU device enumerator
        Encapsulation of a TLDfuEnumerationHandle

    Author(s):
        <PERSON><PERSON><PERSON> Huck
        
    Thesycon Software Solutions GmbH & Co. KG, Germany, www.thesycon.de

************************************************************************/

#ifndef __TLDfuEnumerator_h__
#define __TLDfuEnumerator_h__


class TLDfuEnumerator
{
public:

    //
    // Constructor
    //
    TLDfuEnumerator()
            {
                // empty
            }


    //
    // Destructor
    //
    ~TLDfuEnumerator()
            {
                // make sure current handle is closed (ignore possibly error)
                CloseEnumeration();
            }

    // disable copy and move operations
    TLDfuEnumerator(const TLDfuEnumerator&) = delete;
    TLDfuEnumerator& operator=(const TLDfuEnumerator&) = delete;
    TLDfuEnumerator(TLDfuEnumerator&&) = delete;
    TLDfuEnumerator& operator=(TLDfuEnumerator&&) = delete;

/////////////////////////////////////////
// Interface
//
public:

    TLSTATUS
    EnumerateUsbDfuDevices(
        const char* deviceFilterDescription,
        unsigned int& deviceCount,
        unsigned int retryCount = 0,
        unsigned int retryDelay = 0,
        unsigned int flags = 0
        )
            {
                // make sure current handle is closed (ignore possible error)
                CloseEnumeration();
            
                return TLDFU_EnumerateUsbDfuDevices(
                                deviceFilterDescription,
                                &mHandle,
                                &deviceCount,
                                retryCount,
                                retryDelay,
                                flags
                                );
            }


    TLSTATUS
    CloseEnumeration()
            {
                TLSTATUS st = TLSTATUS_SUCCESS;
                
                if ( TLDFU_INVALID_HANDLE != mHandle ) {
                    st = TLDFU_CloseEnumeration(mHandle);
                    mHandle = TLDFU_INVALID_HANDLE;
                }

                return st;
            }


    TLSTATUS
    CompareDeviceInstance(
        unsigned int deviceIndex,
        TLDfuDeviceHandle deviceHandle
        )
            {
                return TLDFU_CompareDeviceInstance(mHandle, deviceIndex, deviceHandle);
            }


    //
    // Access to the encapsulated handle.
    //
    TLDfuEnumerationHandle
    Handle() const
            {
                return mHandle;
            }


/////////////////////////////////////////
// Implementation
//
protected:



////////////////////////////////////////
// Data
//
protected:

    // handle of the enumerator
    TLDfuEnumerationHandle mHandle {TLDFU_INVALID_HANDLE};
};


#endif 

/*** EOF ***/
