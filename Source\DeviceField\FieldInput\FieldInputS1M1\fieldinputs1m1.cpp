#include "fieldinputs1m1.h"


FieldInputS1M1::FieldInputS1M1(QWidget* parent, QString name)
    : FieldInputBase1(parent)
    , WorkspaceObserver(name)
    , AppSettingsObserver(name)
{
    connect(this, &FieldInputBase1::attributeChanged, this, &FieldInputS1M1::in_widgetBase_attributeChanged, Qt::UniqueConnection);
}
FieldInputS1M1::~FieldInputS1M1()
{

}


// override
void FieldInputS1M1::loadSettings()
{
    WorkspaceObserver::getSettings()->beginGroup(objectName());
    bool flag=WorkspaceObserver::getSettings()->contains(objectName());
    WorkspaceObserver::getSettings()->endGroup();
    if(!flag)
    {
        WorkspaceObserver::setValue(objectName(), true);
        WorkspaceObserver::setValue("SoloState", 0);
        WorkspaceObserver::setValue("Visible", mVisibleListDefault);
    }
    mSoloState = WorkspaceObserver::value("SoloState").toInt();
    QVariantList visibleList=WorkspaceObserver::value("Visible").toList();
    QVector<QString> list;
    for(auto element : visibleList)
    {
        list.append(element.toString());
    }
    setVisibleList(list);
    emit attributeChanged("Input_All", "Save_SoloState", QString::number(mSoloState));
}
void FieldInputS1M1::AppSettingsChanged(QString objectName, QString attribute, QString value)
{
    Q_UNUSED(objectName);
    if(attribute == "Language")
    {
        if(value == "English")
        {
            setFieldTitle("Input");
        }
        else if(value == "Chinese")
        {
            setFieldTitle("输入");
        }
    }
}


// slot
void FieldInputS1M1::in_widgetBase_attributeChanged(QString objectName, QString attribute, QString value)
{
    if(attribute == "Visible")
    {
        if(value.toInt())
        {
            QVariantList visibleList=WorkspaceObserver::value("Visible").toList();
            visibleList.append(QVariant(objectName));
            WorkspaceObserver::setValue("Visible", visibleList);
        }
        else
        {
            QVariantList visibleList=WorkspaceObserver::value("Visible").toList();
            visibleList.removeOne(QVariant(objectName));
            WorkspaceObserver::setValue("Visible", visibleList);
        }
    }
}
void FieldInputS1M1::in_widgetList_attributeChanged(QString objectName, QString attribute, QString value)
{
    Q_UNUSED(objectName);
    if(attribute == "Solo")
    {
        if(value.toInt())
        {
            mSoloState++;
            if(mSoloState == 1)
            {
                for(auto widget : mWidgetList)
                {
                    widget->handleFieldSoloStateChanged(mSoloState);
                }
            }
        }
        else
        {
            mSoloState--;
            if(mSoloState == 0)
            {
                for(auto widget : mWidgetList)
                {
                    widget->handleFieldSoloStateChanged(mSoloState);
                }
            }
        }
        WorkspaceObserver::setValue("SoloState", mSoloState);
        emit attributeChanged("Input_All", "Save_SoloState", QString::number(mSoloState));
    }
}


// setter & getter
FieldInputS1M1& FieldInputS1M1::setName(QString name)
{
    setObjectName(name);
    WorkspaceObserver::setObserverName(name);
    AppSettingsObserver::setObserverName(name);
    return *this;
}
FieldInputS1M1& FieldInputS1M1::modifyWidgetList(QVector<InputBase*> list)
{
    mWidgetList = list;
    for(auto element : list)
    {
        element->setWidgetMovable(false).assignSoloState(&mSoloState);
        connect(element, SIGNAL(attributeChanged(QString, QString, QString)), this, SLOT(in_widgetList_attributeChanged(QString, QString, QString)), Qt::UniqueConnection);
    }
    FieldInputBase1::modifyWidgetList(list);
    return *this;
}
FieldInputS1M1& FieldInputS1M1::setVisibleListDefault(QVector<InputBase*> list)
{
    for(auto widget : list)
    {
        mVisibleListDefault.append(QVariant(widget->getChannelName()));
    }
    return *this;
}

