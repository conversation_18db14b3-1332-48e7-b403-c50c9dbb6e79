/************************************************************************
 *
 *  Module:       WnStringUtils.h
 *
 *  Description:  String Utility Functions
 *
 *  Runtime Env.: Win32
 *
 *  Author(s):
 *    Udo <PERSON>,  <EMAIL>
 *
 *  Companies:
 *    Thesycon GmbH, Germany      http://www.thesycon.de
 *
 ************************************************************************/

#ifndef __WnStringUtils_h__
#define __WnStringUtils_h__

// optionally put everything into a namespace
#ifdef LIBWN_NAMESPACE
namespace LIBWN_NAMESPACE {
#endif

//
// Below is a set of sprintf-style functions.
// Each of these functions takes a format string and arguments,
// then formats and writes characters to the memory pointed to by buffer
// and *always* appends a terminating null.
// There are two flavors:
//   sprintf-style  using a variable number of arguments (elipsis)
//   vsprintf-style using a pointer to an argument list (va_list)
//
// The format string is printf-compatible, see printf documentation.
// Each function is implemented in a char and wide character variant using function overloading.
//
// Each of the functions returns:
// ERROR_SUCCESS -- string written to buffer, null appended, no truncation occurred
// ERROR_BUFFER_OVERFLOW -- buffer too small, string was truncated
// ERROR_INVALID_PARAMETER -- an invalid parameter was passed.
//

// char version
WNERR
WnStringPrintf(
    char* buffer,
    unsigned int maxChars,  // max chars to be written to buffer, including terminating null
    const char* format,
    ...
    );

// wide char version
WNERR
WnStringPrintf(
    wchar_t* buffer,
    unsigned int maxChars,  // max chars to be written to buffer, including terminating null
    const wchar_t* format,
    ...
    );
// return value of this function is likely to be ignored
//lint -esym(534, WnStringPrintf)


// char version
WNERR
WnStringVPrintf(
    char* buffer,
    unsigned int maxChars,  // max chars to be written to buffer, including terminating null
    const char* format,
    va_list argList
    );

// wide char version
WNERR
WnStringVPrintf(
    wchar_t* buffer,
    unsigned int maxChars,  // max chars to be written to buffer, including terminating null
    const wchar_t* format,
    va_list argList
    );
// return value of this function is likely to be ignored
//lint -esym(534, WnStringVPrintf)

#ifdef LIBWN_NAMESPACE
}
#endif

#endif //__WnStringUtils_h__

/********************************* EOF *********************************/
