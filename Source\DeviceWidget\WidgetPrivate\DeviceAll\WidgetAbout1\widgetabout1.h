#ifndef WidgetAbout1_H
#define WidgetAbout1_H

#include <QWidget>
#include "appsettings.h"

namespace Ui {
class WidgetAbout1;
}

class WidgetAbout1 : public QWidget, public AppSettingsObserver
{
    Q_OBJECT

public:
    typedef enum{
        UpdateStatus_None,
        UpdateStatus_Available,
        UpdateStatus_Error
    }UpdateStatus;

public:
    explicit WidgetAbout1(QWidget *parent = nullptr, const QString &name = "");
    ~WidgetAbout1();
    void setName(const QString &name);
    void setFont(const QFont& font);
    void changeLanguage(QString language);
    void setFirmwareUpdateStatus(UpdateStatus status);
    void setSoftwareUpdateStatus(UpdateStatus status);
    void setDeviceName(const QString &name);
    void setHardwareVersion(const QString &version);
    void setFirmwareText(const QString & text);
    void setSoftwareText(const QString & text);
    int itemCount()const;

protected:
    void resizeEvent(QResizeEvent *event) override;
    void showEvent(QShowEvent *event) override;
    void AppSettingsChanged(QString objectName, QString attribute, QString value) override;
    void setFirmwareInfo();
    void setSoftwareInfo();

signals:
    void attributeChanged(const QString &objectName, const QString &attribute, const QString &value);

private:
    Ui::WidgetAbout1 *ui;
    QFont m_font;
    UpdateStatus m_softwareUpdateStatus = UpdateStatus_None;
    UpdateStatus m_firmwareUpdateStatus = UpdateStatus_None;
    QString m_language = "English";
};

#endif // WidgetAbout1_H
