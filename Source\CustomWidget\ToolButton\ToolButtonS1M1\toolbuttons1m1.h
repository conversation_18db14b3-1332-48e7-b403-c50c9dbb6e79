#ifndef TOOLBUTTONS1M1_H
#define TOOLBUTTONS1M1_H


#include <QFont>
#include <QMenu>
#include <QColor>
#include <QWidget>
#include <QString>
#include <QAction>
#include <QToolButton>
#include <QResizeEvent>


class ToolButtonS1M1 : public QWidget
{
    Q_OBJECT
public:
    explicit ToolButtonS1M1(QWidget* parent=nullptr);
    ~ToolButtonS1M1();
    ToolButtonS1M1& setFont(QFont font);
    ToolButtonS1M1& addMenu(QString menuName);
    ToolButtonS1M1& addAction(QString menuName, QString actionName);
    ToolButtonS1M1& setColor(QColor color);
    ToolButtonS1M1& setActionTriggered(QString actionName);
protected:
    void resizeEvent(QResizeEvent* e) override;
private:
    QMenu mMenu;
    QToolButton mToolButton;
    QFont mFont;
    QString mLongestAction="";
    QString mColor="rgb(216, 216, 216)";
private slots:
    void in_mMenu_triggered(QAction* action);
signals:
    void actionChanged(QString actionName);
};


#endif // TOOLBUTTONS1M1_H

