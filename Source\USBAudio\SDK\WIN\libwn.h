/************************************************************************
 *
 *  Module:       libwn.h
 *  Description:
 *     library main include file, to be included by client apps
 *
 *  Author(s):
 *    Udo <PERSON>hardt
 *
 *  Companies:
 *    Thesycon GmbH, Germany      http://www.thesycon.de
 *
 ************************************************************************/

#ifndef __libwn_h__
#define __libwn_h__

// lib config
#define LIBWN_CAT_Base                1
#define LIBWN_CAT_BaseEx              1
#define LIBWN_CAT_TraceLogFileBuffered    0
#define LIBWN_CAT_Thread              1
#define LIBWN_CAT_UserModeCrashDump   1
#define LIBWN_CAT_UiLanguage          1

#include "win_targetver.h"

#ifndef _WIN32_WINNT
#error _WIN32_WINNT must be defined in win_targetver.h
#endif


#if defined(_lint)
// lint requires this
#include <typeinfo>
#endif


// Windows main header file
#include <windows.h>
#if !defined(UNDER_CE)
#include <VersionHelpers.h>
#endif
// basic OLE stuff, required for GUID handling
#include <objbase.h>
// IOCTL stuff
#include <winioctl.h>

#if !defined(UNDER_CE)
// Windows: additional includes from Windows SDK
#include <setupapi.h>
#include <dbt.h>
// tell the linker to link with import libs
#pragma comment(lib, "setupapi.lib")
#else
// CE: additional system includes
#include <devload.h>
#endif


// C/C++ runtime
#include <new>
#include <stdio.h>
#include <stdlib.h>
#include <tchar.h>
#include <string>

// libbase and libtb
// generic utilities, including stdlib and STL based features
#include "libtb.h"

#ifndef TB_DEBUG
#error TB_DEBUG is not defined.
#endif


// status codes
#include "tstatus_codes.h"

// basic types
#include "WnTypes.h"


// forward references
class WnModuleFileName;


#if LIBWN_CAT_Base
// Base modules that don't use tracing
#include "WnCriticalSection.h"
#include "WnModuleFileName.h"
// tracing
#include "WnTraceLogFile.h"
#if LIBWN_CAT_TraceLogFileBuffered
#include "WnTraceLogFileBuffered.h"
#endif
#include "WnTraceLogContext.h"
#include "WnTraceLogSettings.h"
#include "WnTrace.h"
// Base modules that use tracing
#include "WnHandle.h"
#include "WnEvent.h"
#include "WnStringUtils.h"
#include "WnRegistryKey.h"
#include "WnLibrary.h"
#endif //LIBWN_CAT_Base

#if LIBWN_CAT_BaseEx
#include "WnWow64.h"
#endif

#if LIBWN_CAT_Thread
#include "WnThread.h"
#endif

#if LIBWN_CAT_UserModeCrashDump && !defined(UNDER_CE)
#include "WnUserModeCrashDump.h"
#endif

#if LIBWN_CAT_UiLanguage
class WnUiLanguageText;

typedef std::wstring WString;
using UiLanguageTextHandle = std::shared_ptr<WnUiLanguageText>;
#include <vector>
#include <fstream>
#include "WnUiLanguage.h"
typedef std::vector< WnUiLanguage > WnUiLanguageVector;
#include "WnUiLanguageText.h"
typedef std::vector< UiLanguageTextHandle > WnUiLanguageTextVector;
#include "WnUiLanguageTextGroup.h"
typedef std::vector< WnUiLanguageTextGroup > WnUiLanguageTextGroupVector;
#include "WnUiLanguageFile.h"
#include "WnUiLanguageMgr.h"
#endif


// USB + Audio spec
#include "tusb_spec.h"
#include "tusb_cls_audio.h"
#include "tusb_cls_audio20.h"


#endif  // __libwn_h__

/*************************** EOF **************************************/
