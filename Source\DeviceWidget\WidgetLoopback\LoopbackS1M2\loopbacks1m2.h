#ifndef LOOPBACKS1M2_H
#define LOOPBACKS1M2_H


#include <QFont>
#include <QEvent>
#include <QTimer>
#include <QObject>
#include <QWidget>
#include <QResizeEvent>

#include "workspace.h"
#include "appsettings.h"
#include "loopbackbase.h"


namespace Ui {
class LoopbackS1M2;
}


class LoopbackS1M2 : public LoopbackBase, public WorkspaceObserver, public AppSettingsObserver
{
    Q_OBJECT
public:
    explicit LoopbackS1M2(QWidget* parent=nullptr, QString name="");
    ~LoopbackS1M2();
    LoopbackS1M2& setName(QString name);
    LoopbackS1M2& setFont(QFont font);
    LoopbackS1M2& setVolumeMeterLeft(int value);
    LoopbackS1M2& setVolumeMeterLeftClear();
    LoopbackS1M2& setVolumeMeterLeftSlip();
    LoopbackS1M2& setVolumeMeterRight(int value);
    LoopbackS1M2& setVolumeMeterRightClear();
    LoopbackS1M2& setVolumeMeterRightSlip();
    LoopbackS1M2& setGain(float value);
    LoopbackS1M2& setGainLock(bool state=true);
    LoopbackS1M2& setMuteAffectGain(bool state=true);
    LoopbackS1M2& setGainAffectMute(bool state=true);
    LoopbackS1M2& setGainRange(float min, float max);
    LoopbackS1M2& setGainDefault(float value);
    LoopbackS1M2& setGainWidgetDisable(float value);
    LoopbackS1M2& setAudioSourceDefault(QString audioSourceDefault);
    LoopbackS1M2& setAudioSourceColor(QColor color);
    LoopbackS1M2& setChannelNameEditable(bool state=true);
    LoopbackS1M2& addAudioSource(QString audioClass, QVector<QString>& audioSourceList);
    QString getAudioSource();
protected:
    bool eventFilter(QObject* obj, QEvent* e) override;
    void resizeEvent(QResizeEvent* e) override;
    void updateAttribute() override;
    void loadSettings() override;
    void AppSettingsChanged(QString objectName, QString attribute, QString value) override;
private:
    Ui::LoopbackS1M2* ui;
    QTimer mTimer;
    QFont mFont;
    QString mAudioSourceDefault="";
    QString mPreAudioSource="";
    int mPreMUTE=-2147483648;
    int mPreMUTELeft=-2147483648;
    int mPreMUTERight=-2147483648;
    float mPreGAINLeft=-2147483648;
    float mPreGAINRight=-2147483648;
    float mDisableGAIN=-88;
    bool mMuteAffectGain=false;
    bool mGainAffectMute=false;
    bool mLinkDefaultState=true;
    bool mLinkState=false;
    void save(QAnyStringView key, const QVariant& value);
private slots:
    void in_mTimer_timeout();
    void in_widgetLinkedVSlider_valueChanged(int value);
    void in_widgetUnlinkVSliderLeft_valueChanged(int value);
    void in_widgetUnlinkVSliderRight_valueChanged(int value);
    void in_widgetToolButton_actionChanged(QString actionName);
    void in_widgetPushButtonGroup1_stateChanged(QString button, QString state);
    void in_widgetPushButtonGroup2_stateChanged(QString button, QString state);
    void in_widgetPushButtonGroup3_stateChanged(QString button, QString state);
    void on_lineEdit_textChanged(const QString& arg1);
    void on_lineEdit_editingFinished();
    void on_pushButtonClose_clicked();
    void on_pushButtonLink_clicked();
};


#endif // LOOPBACKS1M2_H

