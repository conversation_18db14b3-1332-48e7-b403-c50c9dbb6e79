#ifndef USBHIDAPI_H
#define USBHIDAPI_H


#include "hidapi.h"


class USBHIDAPI
{
public:
    static USBHIDAPI& instance() { return mInstance; }
    void init();
    void exit();
    int read(unsigned char* data, int length);
    int send(unsigned char* data, int length);
    bool openDevice(unsigned int deviceVendorID, unsigned int deviceProductID);
    void closeDevice();
private:
    static USBHIDAPI mInstance;
    hid_device *mHIDDevice=NULL;
    USBHIDAPI() = default;
    USBHIDAPI(const USBHIDAPI&) = delete;
    ~USBHIDAPI() { }
    USBHIDAPI& operator=(const USBHIDAPI&) = delete;
};


#define USBHHandle USBHIDAPI::instance()


#endif // USBHIDAPI_H

