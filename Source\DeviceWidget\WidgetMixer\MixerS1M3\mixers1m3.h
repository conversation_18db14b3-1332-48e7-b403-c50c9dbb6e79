#ifndef MIXERS1M3_H
#define MIXERS1M3_H


#include <QFont>
#include <QEvent>
#include <QTimer>
#include <QObject>
#include <QWidget>
#include <QResizeEvent>

#include "mixerbase.h"
#include "workspace.h"
#include "appsettings.h"


namespace Ui {
class MixerS1M3;
}


class MixerS1M3 : public MixerBase, public WorkspaceObserver, public AppSettingsObserver
{
    Q_OBJECT
public:
    explicit MixerS1M3(QWidget* parent=nullptr, QString name="");
    ~MixerS1M3();
    MixerS1M3& setName(QString name);
    MixerS1M3& setFont(QFont font);
    MixerS1M3& setBalanceL(int value, bool needUpdate=true);
    MixerS1M3& setBalanceR(int value, bool needUpdate=true);
    MixerS1M3& setVolumeMeterLeft(int value);
    MixerS1M3& setVolumeMeterLeftClear();
    MixerS1M3& setVolumeMeterLeftSlip();
    MixerS1M3& setVolumeMeterRight(int value);
    MixerS1M3& setVolumeMeterRightClear();
    MixerS1M3& setVolumeMeterRightSlip();
    MixerS1M3& setChannelNameEditable(bool state=true);
protected:
    bool eventFilter(QObject* obj, QEvent* e) override;
    void resizeEvent(QResizeEvent* e) override;
    void handleFieldMixerChanged(QString mixer) override;
    void updateAttribute() override;
    void setSoloState(bool state) override;
    void setSoloStateLeft(bool state) override;
    void setSoloStateRight(bool state) override;
    void setMuteState(bool state) override;
    void setMuteStateLeft(bool state) override;
    void setMuteStateRight(bool state) override;
    void setSoloClicked(bool state) override;
    void setSoloClickedLeft(bool state) override;
    void setSoloClickedRight(bool state) override;
    void setMuteClicked(bool state) override;
    void setMuteClickedLeft(bool state) override;
    void setMuteClickedRight(bool state) override;
    bool getSoloState() override;
    bool getSoloStateLeft() override;
    bool getSoloStateRight() override;
    bool getMuteState() override;
    bool getMuteStateLeft() override;
    bool getMuteStateRight() override;
    void loadSettings() override;
    void AppSettingsChanged(QString objectName, QString attribute, QString value) override;
private:
    Ui::MixerS1M3* ui;
    QTimer mTimer;
    QFont mFont;
    int mBalanceL=0;
    int mBalanceR=0;
    int mVolumeMeterLeft=-900;
    int mVolumeMeterRight=-900;
    int mPreGainMixerLeftChannelLeft=-2147483648;
    int mPreGainMixerLeftChannelRight=-2147483648;
    int mPreGainMixerRightChannelLeft=-2147483648;
    int mPreGainMixerRightChannelRight=-2147483648;
    void save(QAnyStringView key, const QVariant& value);
    void doBalanceChanged();
private slots:
    void in_mTimer_timeout();
    void in_widgetLinkedVSlider_valueChanged(int value);
    void in_widgetPushButtonGroup1_stateChanged(QString button, QString state);
    void on_lineEdit_textChanged(const QString& arg1);
    void on_lineEdit_editingFinished();
    void on_pushButtonClose_clicked();
};


#endif // MIXERS1M3_H

