#ifndef M62_PrivateWidget5_H
#define M62_PrivateWidget5_H

#include <QWidget>
#include "workspace.h"
#include "appsettings.h"

class MenuS1M1;
class M62_PrivateWidget5 : public QWidget, public WorkspaceObserver, public AppSettingsObserver
{
    Q_OBJECT

public:
    explicit M62_PrivateWidget5(QWidget *parent = nullptr, const QString& name = {});
    ~M62_PrivateWidget5();
    M62_PrivateWidget5& setName(const QString& name);
    M62_PrivateWidget5& setFont(const QFont& font);

protected:
    void paintEvent(QPaintEvent* e)override;
    void resizeEvent(QResizeEvent* e)override;
    void loadSettings() override;
    void AppSettingsChanged(QString objectName, QString attribute, QString value) override;;
    void setAllChildFont(QWidget* widget, const QFont& font);

private:
    void initSigConnect();

signals:
    void attributeChanged(QString objectName, QString attribute, QString value);
    
private:
    QWidget* mWidget;
    MenuS1M1* mMenuPresets = nullptr;
};

#endif // M62_PrivateWidget5_H
