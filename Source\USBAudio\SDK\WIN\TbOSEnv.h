/*******************************************************************************
 *
 *  Module:         TbOSEnv.h
 *  Description:    operating system specific stuff
 *
 *  Runtime Env.:   any
 *  Author(s):      Udo Eberhardt
 *  Company:        Thesycon GmbH, Ilmenau
 *  Copyright:      (c) 2010 Thesycon Systemsoftware and Consulting GmbH
 *
 ******************************************************************************/

#ifndef __TbOSEnv_h__
#define __TbOSEnv_h__

// optionally put everything into a namespace
#ifdef LIBTB_NAMESPACE
namespace LIBTB_NAMESPACE {
#endif

//
// This class serves as a namespace that provides
// operating system support functions.
// Functions of this class are to be implemented outside of the library.
//
class TbOSEnv
{
public:
    // atomic integer ops
    // mapped to libbase AL functions

    typedef T_INT32 AtomicIntType;

    // atomic increment, returns the incremented value
    static
    inline
    AtomicIntType
    AtomicIncrement(AtomicIntType& cnt)
        {
            return TbAtomicIncrementInt32(&cnt);
        }

    // atomic decrement, returns the decremented value
    static
    inline
    AtomicIntType
    AtomicDecrement(AtomicIntType& cnt)
        {
            return TbAtomicDecrementInt32(&cnt);
        }

}; //TbOSEnv



//
// This mix-in-style base class provides basic trace functions and assertions.
// Any generic class that needs trace support can derive
// from TbDebug to import this functionality into the scope of the class.
//
class TbDebug
{
public:

    //
    // trace channels
    //
    enum TraceChannel
    {
        TrcError = 0,
        TrcInfo,
        TrcVerbose,
        TrcFunction,
        TrcDump
    };


    //
    // print formatted
    // printf-style standard formatters are supported.
    //
#if TB_DEBUG
    static
    void
    DebugPrint(
        TraceChannel chan,
        const char* format,
        ...
        );
#else
    static
    void
    DebugPrint(
        TraceChannel /*chan*/,
        const char* /*format*/,
        ...
        )
            {}
#endif


    //
    // print formatted
    // printf-style standard formatters are supported.
    //
#if TB_DEBUG
    // this function is to implemented outside of the library
    static
    void
    DebugVPrintf(
        TraceChannel chan,
        const char* format,
        va_list args
        );
#else
    static
    void
    DebugVPrintf(
        TraceChannel /*chan*/,
        const char* /*format*/,
        va_list /*args*/
        )
            {}
#endif


    //
    // print a hex dump
    //
#if TB_DEBUG
    // this function is to implemented outside of the library
    static
    void
    DebugDumpBytes(
        TraceChannel chan,
        const void* ptr,
        unsigned int byteCount
        );
#else
    static
    void
    DebugDumpBytes(
        TraceChannel /*chan*/,
        const void* /*ptr*/,
        unsigned int /*byteCount*/
        )
            {}
#endif


    //
    // Assert implementation
    //
#if TB_DEBUG
    static
    void
    Assert(
        int condition,
        const char* conditionstr,
        const char* filename,
        unsigned int linenb
        )
            {
                if ( !condition) {
                    AssertFailed(conditionstr, filename, linenb);
                }
            }
#else
    static
    void
    Assert(
        int /*condition*/,
        const char* /*conditionstr*/,
        const char* /*filename*/,
        unsigned int /*linenb*/
        )
            {}
#endif

    // this function is to implemented outside of the library
    static
    void
    AssertFailed(
        const char* conditionstr,
        const char* filename,
        unsigned int linenb
        );


}; //TbDebug



#if defined(_lint)
// map to __assert to make lint aware of the special meaning of this function
#define TBASSERT(condition)   __assert(condition)

#elif defined(_PREFAST_)
// map to NT kernel ASSERT to make Prefast aware of assert semantics
#define TBASSERT(condition)   NT_ASSERT(condition)

#else

#if TB_DEBUG
// map macro to external assert implementation
#define TBASSERT(condition)   TbDebug::Assert(condition, #condition, __FILE__, __LINE__)
#else
// empty macro
#define TBASSERT(condition)
#endif

#endif


    //
    // Memory tags to be passed to new.
    // Note: The standard says that enums are distinct types and can be used
    // to distinguish overloaded function declarations.
    //
    // usage examples:
    //   T* p = new (TbMemTypePaged) T;
    //   T* p = new (TbMemTypeNonPaged) T;
    //
    enum TbMemType
    {
        TbMemTypeDontCare = 0, // used in user mode only
        TbMemTypePaged,       // kernel mode
        TbMemTypeNonPaged     // kernel mode
    };

#ifdef LIBTB_NAMESPACE
}
#endif


#ifdef LIBTB_NAMESPACE
#define TB_MEM_TYPE LIBTB_NAMESPACE::TbMemType
#else
#define TB_MEM_TYPE TbMemType
#endif


//
// libtb code uses the following special new operator which must be implemented elsewhere.
// new returns NULL if memory allocation failed.
// The content of the returned memory is undefined, the memory is NOT zero-initialized.
//
void* LIBTB_NEW_DELETE_DECL operator new   (size_t size, TB_MEM_TYPE memType) throw();
void* LIBTB_NEW_DELETE_DECL operator new[] (size_t size, TB_MEM_TYPE memType) throw();

// we need to define a matching delete operator that will be called if a constructor throws an exception
void LIBTB_NEW_DELETE_DECL operator delete   (void* p, TB_MEM_TYPE memType) throw();
void LIBTB_NEW_DELETE_DECL operator delete[] (void* p, TB_MEM_TYPE memType) throw();


//
// libtb code uses a special new placement operator which must be implemented elsewhere.
// TB_MEM_TYPE is a dummy used to select the right function.
//
void* LIBTB_NEW_DELETE_DECL operator new   (size_t size, void* p, TB_MEM_TYPE memTypeDummy) throw();
void* LIBTB_NEW_DELETE_DECL operator new[] (size_t size, void* p, TB_MEM_TYPE memTypeDummy) throw();

// we need to define a matching delete operator that will be called if a constructor throws an exception
void LIBTB_NEW_DELETE_DECL operator delete   (void* p, void* dummy, TB_MEM_TYPE memTypeDummy) throw();
void LIBTB_NEW_DELETE_DECL operator delete[] (void* p, void* dummy, TB_MEM_TYPE memTypeDummy) throw();


//
// libtb code uses the standard delete operator which must be implemented elsewhere.
// Note: delete is null pointer safe.
//
void LIBTB_NEW_DELETE_DECL operator delete   (void* p) throw();
void LIBTB_NEW_DELETE_DECL operator delete[] (void* p) throw();


#endif // __TbOSEnv_h__

/******************************** EOF ***********************************/
