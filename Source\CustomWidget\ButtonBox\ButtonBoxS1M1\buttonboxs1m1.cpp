#include "globalfont.h"
#include "buttonboxs1m1.h"
#include "ui_buttonboxs1m1.h"


ButtonBoxS1M1::ButtonBoxS1M1(QWidget *parent)
    : QWidget(parent)
    , ui(new Ui::ButtonBoxS1M1)
{
    ui->setupUi(this);
    resize(minimumWidth(), minimumHeight());
    QString style;
    style = "QFrame {"
            "   background-color: #161616;"
            "   border-radius: 8px;"
            "}";
    ui->frame->setStyleSheet(style);
    style = "QPushButton {"
            "   color: rgb(161, 161, 161);"
            "   background-color: transparent;"
            "}"
            "QPushButton:hover {"
            "   color: rgb(255, 255, 255);"
            "}";
    mButtonAddition.setParent(this);
    mButtonAddition.setStyleSheet(style);
    mButtonAddition.setFocusPolicy(Qt::NoFocus);
    mButtonAddition.setText("+");
    mTimer.setSingleShot(true);
    mTimer.setInterval(1000);
    connect(&mTimer, SIGNAL(timeout()), this, SLOT(in_mTimer_timeout()), Qt::UniqueConnection);
    connect(&mButtonAddition, SIGNAL(clicked()), this, SLOT(in_mButtonAddition_clicked()), Qt::UniqueConnection);
    installEventFilter(this);
}
ButtonBoxS1M1::~ButtonBoxS1M1()
{
    for(auto element : mButtonList)
    {
        delete element;
    }
    delete ui;
}


// override
bool ButtonBoxS1M1::eventFilter(QObject* obj, QEvent* e)
{
    if(obj == this && mButtonAddition.isHidden())
    {
        if(e->type() == QEvent::Enter)
        {
            mTimer.stop();
            return true;
        }
        else if(e->type() == QEvent::Leave)
        {
            mTimer.start();
            return true;
        }
    }
    return QWidget::eventFilter(obj, e);
}
void ButtonBoxS1M1::resizeEvent(QResizeEvent* e)
{
    Q_UNUSED(e);
    // W
    float wPixelPerRatio=size().width() / 100.0;
    int wPushButton=wPixelPerRatio * mWeightWidth;
    int xPushButton=(size().width() - wPushButton) / 2;
    // H
    int hPushButton=wPushButton / 100.0 * mWeightHeight;
    int hSpace=hPushButton / 8;
    int y=(size().height() - mVisibleCount * hPushButton - (mVisibleCount - 1) * hSpace) / 2;
    mButtonAddition.setGeometry(0, 0, size().width(), size().height());
    mFont.setPointSize(GLBFHandle.getSuitablePointSize(mFont, "+", size().width() / 4));
    mButtonAddition.setFont(mFont);
    mFont.setPointSize(GLBFHandle.getSuitablePointSize(mFont, mLongestButton, QRect(0, 0, wPushButton, hPushButton)));
    for(auto element : mButtonList)
    {
        if(element->visible)
        {
            element->button.setGeometry(xPushButton, y, wPushButton, hPushButton);
            element->button.setFont(mFont);
            y += hPushButton;
            y += hSpace;
        }
        else
        {
            continue;
        }
    }
}


// slot
void ButtonBoxS1M1::in_mButtonAddition_clicked()
{
    if(mVisibleCount)
    {
        mButtonAddition.setHidden(true);
        for(auto element : mButtonList)
        {
            if(element->visible)
            {
                element->button.setHidden(false);
            }
        }
    }
}
void ButtonBoxS1M1::in_mTimer_timeout()
{
    mButtonAddition.setHidden(false);
    for(auto element : mButtonList)
    {
        if(element->visible)
        {
            element->button.setHidden(true);
        }
    }
}
void ButtonBoxS1M1::in_mButtonListAll_clicked()
{
    QPushButton* button=qobject_cast<QPushButton*>(sender());
    for(auto element : mButtonList)
    {
        if(element->button.objectName() == button->objectName())
        {
            element->visible = false;
            element->button.setHidden(true);
            mVisibleCount--;
            break;
        }
    }
    emit attributeChanged(button->objectName(), "", "");
    QResizeEvent e(size(), size());
    resizeEvent(&e);
    update();
}


// setter & getter
ButtonBoxS1M1& ButtonBoxS1M1::setFont(QFont font)
{
    mFont = font;
    QResizeEvent e(size(), size());
    resizeEvent(&e);
    update();
    return *this;
}
ButtonBoxS1M1& ButtonBoxS1M1::modifyButtonList(QVector<QString> list)
{
    mVisibleCount = 0;
    mLongestButton = "";
    for(auto element : mButtonList)
    {
        delete element;
    }
    mButtonList.clear();
    QString style;
    for(auto element : list)
    {
        Button* newButton=new Button();
        newButton->visible = false;
        newButton->button.setParent(this);
        newButton->button.setObjectName(element);
        newButton->button.setFocusPolicy(Qt::NoFocus);
        newButton->button.setText(element);
        newButton->button.setHidden(true);
        style = "QPushButton {"
                "   color: rgb(161, 161, 161);"
                "   background-color: transparent;"
                "}"
                "QPushButton:hover {"
                "   color: rgb(67, 207, 124);"
                "}";
        newButton->button.setStyleSheet(style);
        connect(&newButton->button, SIGNAL(clicked()), this, SLOT(in_mButtonListAll_clicked()), Qt::UniqueConnection);
        mButtonList.append(newButton);
        mLongestButton = GLBFHandle.getLongerInPointSize(mLongestButton, element);
    }
    return *this;
}
ButtonBoxS1M1& ButtonBoxS1M1::setVisibleList(QVector<QString> list)
{
    mVisibleCount = 0;
    for(auto element : mButtonList)
    {
        if(list.contains(element->button.objectName()))
        {
            element->visible = true;
            mVisibleCount++;
        }
        else
        {
            element->visible = false;
            element->button.setHidden(true);
        }
    }
    QResizeEvent e(size(), size());
    resizeEvent(&e);
    update();
    return *this;
}
ButtonBoxS1M1& ButtonBoxS1M1::setButtonVisible(QString button)
{
    for(auto element : mButtonList)
    {
        if(button == element->button.objectName())
        {
            element->visible = true;
            mVisibleCount++;
        }
    }
    QResizeEvent e(size(), size());
    resizeEvent(&e);
    update();
    return *this;
}
ButtonBoxS1M1& ButtonBoxS1M1::setButtonWeight(int weightWidth, int weightHeight)
{
    mWeightWidth = weightWidth;
    mWeightHeight = weightHeight;
    QResizeEvent e(size(), size());
    resizeEvent(&e);
    update();
    return *this;
}

