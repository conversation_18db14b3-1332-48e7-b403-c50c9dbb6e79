#include "globalfont.h"
#include "usbaudioapi.h"
#include "updaterfactory.h"
#include "deviceconnector.h"
#include "singleinstancemanager.h"
#include "deviceconnectorviews1m1.h"
#include "mainwindow_m62.h"


DeviceConnector::DeviceConnector()
{
    mConnectViewCreators = {
        {"Series-M", [](DeviceConnector* parent)
            {
                SGIMHandle.setTrayIcon(":/Icon/SeriesM.ico");
                parent->setWindowIcon(QIcon(":/Icon/SeriesM.ico"));
                parent->setWindowButtons(FramelessWindow::Close);
                parent->mBaseSize = {450, 330};
                parent->setMovable(true);
                parent->setResizable(false);
                return new DeviceConnectorViewS1M1(parent);
            }
        },
        // ...
    };
    mMainWindowCreators = {
        {"M62", [](DeviceConnector* parent)
            {
                MainWindow_Base* mainwindow=new MainWindow_M62(parent);
                mainwindow->setWindowIcon(QIcon(":/Icon/SeriesM.ico"));
                mainwindow->setWindowTitle("M Control Center");
                return mainwindow;
            }
        },
        // ...
    };
    mTimerConnector.setInterval(500);
    connect(&mTimerConnector, &QTimer::timeout, this, &DeviceConnector::in_mTimerConnector_timeout, Qt::UniqueConnection);
}
DeviceConnector::~DeviceConnector()
{
    delete mMainWindow;
    delete mConnectView;
}


// override
void DeviceConnector::done(int result)
{
    if(result == getCloseButtonReturnCode()) hide();
}


// slot
void DeviceConnector::in_mTimerConnector_timeout()
{
    QVector<QString> deviceList;
    if(USBAHandle.isDeviceDuplicate(deviceList))
    {
        QSet<QString> intersection=QSet<QString>(deviceList.constBegin(), deviceList.constEnd()) & QSet<QString>(mSeriesDeviceList.constBegin(), mSeriesDeviceList.constEnd());
        if(!intersection.isEmpty())
        {
            mConnectView->showPage(4);
            return;
        }
    }
    deviceList = USBAHandle.getNameOfAllDevice();
    QSet<QString> intersection=QSet<QString>(deviceList.constBegin(), deviceList.constEnd()) & QSet<QString>(mSeriesDeviceList.constBegin(), mSeriesDeviceList.constEnd());
    if(!intersection.isEmpty())
    {
        if(intersection.count() == 1)
        {
            in_widgetConnectView_attributeChanged("", "SelectDevice", *intersection.constBegin());
        }
        else
        {

        }
    }
}
void DeviceConnector::in_widgetConnectView_attributeChanged(QString objectName, QString attribute, QString value)
{
    Q_UNUSED(objectName);
    if(attribute == "SelectDevice")
    {
        mTimerConnector.stop();
        if(mMainWindowCreators.contains(value))
        {
            mMainWindow = mMainWindowCreators[value](nullptr);
            mConnectView->showPage(1);
            connect(mMainWindow, &MainWindow_Base::attributeChanged, this, &DeviceConnector::in_widgetMainWindow_attributeChanged, Qt::UniqueConnection);
            mMainWindow->startDeviceAuthentication();
        }
        else
        {
            qWarning() << "DeviceConnector::in_widgetConnectView_attributeChanged // device" << value << "is not registered in mMainWindowCreators";
        }
    }
    else if(attribute == "Update")
    {
        if(value == "Cancel")
        {
            mConnectView->showPage(0);
            mTimerConnector.start();
            in_mTimerConnector_timeout();
        }
        else if(value == "RetryFetch")
        {
            mConnectView->showPage(5);
            connect(UPDATER_INSTANCE(Software), &UpdaterBase::sigFetchUpdateDataFinish, this, [this](bool result){
                if(result)
                {
                    QString version;
#ifdef Q_OS_WIN
                    version = UPDATER_INSTANCE(Software)->getUpdateData().value("Software").toObject().value("Win").toObject().value("Version").toString();
#endif
#ifdef Q_OS_MACOS
                    version = UPDATER_INSTANCE(Software)->getUpdateData().value("Software").toObject().value("Mac").toObject().value("Version").toString();
#endif
                    if(version > APP_VERSION)
                    {
                        mConnectView->modifyVersion(APP_VERSION, version);
                        mConnectView->showPage(6);
                    }
                    else
                    {
                        mConnectView->showPage(0);
                    }
                }
                else
                {
                    mConnectView->showPage(7);
                }
            });
            UPDATER_INSTANCE(Software)->fetchUpdateDataAsync();
        }
        else if(value == "UpdateSoftware" || value == "RetryDownloadSoftware")
        {
            mConnectView->showPage(8);
            mConnectView->modifyUpdateMessage("");
            mConnectView->modifyUpdateProgress(0);
            mConnectView->modifyUpdateProgress("");
#ifdef Q_OS_WIN
            UPDATER_INSTANCE(Software)->update(UPDATER_INSTANCE(Software)->getUpdateData().value("Software").toObject().value("Win").toObject().value("URL").toString());
#endif
#ifdef Q_OS_MACOS
            UPDATER_INSTANCE(Software)->update(UPDATER_INSTANCE(Software)->getUpdateData().value("Software").toObject().value("Mac").toObject().value("URL").toString());
#endif
        }
        else if(value == "RetryDownloadFirmware")
        {
            mConnectView->showPage(8);
            mConnectView->modifyUpdateMessage("");
            mConnectView->modifyUpdateProgress(0);
            mConnectView->modifyUpdateProgress("");
            UPDATER_INSTANCE(Firmware)->update(UPDATER_INSTANCE(Firmware)->getUpdateData().value("Device").toObject().value(mDFUDevice).toObject().value("URL").toString());
        }
        else if(value == "AutoCheckON")
        {
            QSettings().setValue("AutoCheckForUpdates", true);
        }
        else if(value == "AutoCheckOFF")
        {
            QSettings().setValue("AutoCheckForUpdates", false);
        }
    }
}
void DeviceConnector::in_widgetMainWindow_attributeChanged(QString objectName, QString attribute, QString value)
{
    if(attribute == "Disconnected")
    {
        UPDATER_INSTANCE(Firmware)->setCurVersion("");
        mConnectView->showPage(0);
        resize(mBaseSize.width() * QSettings().value("ScaleFactor").toFloat(), mBaseSize.height() * QSettings().value("ScaleFactor").toFloat());
        show();
        mTimerConnector.start();
        delete mMainWindow;
        mMainWindow = nullptr;
    }
    else if(attribute == "AuthResult")
    {
        if(value == "Success")
        {
            hide();
        }
        else if(value == "Failed")
        {
            mConnectView->showPage(3);
            QTimer::singleShot(2000, [this](){
                mConnectView->showPage(0);
                mTimerConnector.start();
                in_mTimerConnector_timeout();
            });
            delete mMainWindow;
            mMainWindow = nullptr;
        }
    }
    else if(attribute == "UpdateSoftware")
    {
        resize(mBaseSize.width() * QSettings().value("ScaleFactor").toFloat(), mBaseSize.height() * QSettings().value("ScaleFactor").toFloat());
        show();
        delete mMainWindow;
        mMainWindow = nullptr;
        in_widgetConnectView_attributeChanged("", "Update", "UpdateSoftware");
    }
    else if(attribute == "UpdateFirmware")
    {
        UPDATER_INSTANCE(Firmware)->setCurVersion("");
        mDFUDevice = objectName;
        resize(mBaseSize.width() * QSettings().value("ScaleFactor").toFloat(), mBaseSize.height() * QSettings().value("ScaleFactor").toFloat());
        show();
        delete mMainWindow;
        mMainWindow = nullptr;
        if(value.isEmpty())
        {
            mConnectView->showPage(8);
            mConnectView->modifyUpdateMessage("");
            mConnectView->modifyUpdateProgress(0);
            mConnectView->modifyUpdateProgress("");
            UPDATER_INSTANCE(Firmware)->update(UPDATER_INSTANCE(Firmware)->getUpdateData().value("Device").toObject().value(mDFUDevice).toObject().value("URL").toString());
        }
        else
        {
            mConnectView->showPage(8);
            mConnectView->modifyUpdateMessage("");
            mConnectView->modifyUpdateProgress(0);
            mConnectView->modifyUpdateProgress("");
            UPDATER_INSTANCE(Firmware)->doUpdate(value);
        }
    }
}
void DeviceConnector::in_Updater_UpdatingSoftware(QString key, QString value)
{
    static QString fileSize="0B", speed="0B/s";
    if(key == "DownloadState")
    {
        if(value == "Start")
        {
            if(QSettings().value("Language").toString() == "English") mConnectView->modifyUpdateMessage("Downloading  0%");
            else if(QSettings().value("Language").toString() == "Chinese") mConnectView->modifyUpdateMessage("下载中  0%");
            mConnectView->modifyUpdateProgress(0);
            mConnectView->modifyUpdateProgress("");
            fileSize = "0B";
            speed = "0B/s";
        }
        else if(value == "Success")
        {
            if(QSettings().value("Language").toString() == "English") mConnectView->modifyUpdateMessage("Download Success");
            else if(QSettings().value("Language").toString() == "Chinese") mConnectView->modifyUpdateMessage("完成下载");
        }
        else if(value == "Failed")
        {
            mConnectView->showPage(9);
        }
    }
    else if(key == "DownloadProgress")
    {
        mConnectView->modifyUpdateProgress(value.toInt());
        if(QSettings().value("Language").toString() == "English") mConnectView->modifyUpdateMessage(QString("Downloading  %1%").arg(value.toInt()));
        else if(QSettings().value("Language").toString() == "Chinese") mConnectView->modifyUpdateMessage(QString("下载中  %1%").arg(value.toInt()));
    }
    else if(key == "DownloadFileSize")
    {
        fileSize = value;
    }
    else if(key == "DownloadSpeed")
    {
        speed = value;
    }
    else if(key == "DownloadCompleted")
    {
        mConnectView->modifyUpdateProgress(value + " / " + fileSize + "  ( " + speed + " )");
    }
}
void DeviceConnector::in_Updater_UpdatingFirmware(QString key, QString value)
{
    static QString fileSize="0B", speed="0B/s";
    // Download
    if(key == "DownloadState")
    {
        if(value == "Start")
        {
            if(QSettings().value("Language").toString() == "English") mConnectView->modifyUpdateMessage("Downloading  0%");
            else if(QSettings().value("Language").toString() == "Chinese") mConnectView->modifyUpdateMessage("下载中  0%");
            mConnectView->modifyUpdateProgress(0);
            mConnectView->modifyUpdateProgress("");
            fileSize = "0B";
            speed = "0B/s";
        }
        else if(value == "Success")
        {
            if(QSettings().value("Language").toString() == "English") mConnectView->modifyUpdateMessage("Download Success");
            else if(QSettings().value("Language").toString() == "Chinese") mConnectView->modifyUpdateMessage("完成下载");
        }
        else if(value == "Failed")
        {
            mConnectView->showPage(10);
        }
    }
    else if(key == "DownloadProgress")
    {
        mConnectView->modifyUpdateProgress(value.toInt());
        if(QSettings().value("Language").toString() == "English") mConnectView->modifyUpdateMessage(QString("Downloading  %1%").arg(value.toInt()));
        else if(QSettings().value("Language").toString() == "Chinese") mConnectView->modifyUpdateMessage(QString("下载中  %1%").arg(value.toInt()));
    }
    else if(key == "DownloadFileSize")
    {
        fileSize = value;
    }
    else if(key == "DownloadSpeed")
    {
        speed = value;
    }
    else if(key == "DownloadCompleted")
    {
        mConnectView->modifyUpdateProgress(value + " / " + fileSize + "  ( " + speed + " )");
    }
    // Update
    else if(key == "UpdateState")
    {
        if(value == "Start")
        {
            if(QSettings().value("Language").toString() == "English") mConnectView->modifyUpdateMessage("Updating  0%");
            else if(QSettings().value("Language").toString() == "Chinese") mConnectView->modifyUpdateMessage("更新中  0%");
            mConnectView->modifyUpdateProgress(0);
            mConnectView->modifyUpdateProgress("");
            fileSize = "0B";
            speed = "0B/s";
        }
        else if(value == "Success")
        {
            if(QSettings().value("Language").toString() == "English") mConnectView->modifyUpdateMessage("Complete update");
            else if(QSettings().value("Language").toString() == "Chinese") mConnectView->modifyUpdateMessage("完成更新");
            QTimer::singleShot(1000, [this](){
                mConnectView->showPage(12);
            });
        }
        else if(value == "Failed")
        {
            mConnectView->showPage(11);
        }
    }
    else if(key == "UpdateProgress")
    {
        mConnectView->modifyUpdateProgress(value.toInt());
        if(QSettings().value("Language").toString() == "English") mConnectView->modifyUpdateMessage(QString("Updating  %1%").arg(value.toInt()));
        else if(QSettings().value("Language").toString() == "Chinese") mConnectView->modifyUpdateMessage(QString("更新中  %1%").arg(value.toInt()));
    }
    else if(key == "UpdateFileSize")
    {
        fileSize = value;
    }
    else if(key == "UpdateSpeed")
    {
        speed = value;
    }
    else if(key == "UpdateCompleted")
    {
        mConnectView->modifyUpdateProgress(value + " / " + fileSize + "  ( " + speed + " )");
    }
}


// setter & getter
void DeviceConnector::showSeries(QString series, QHash<QString, QPair<QString, QString>> deviceList)
{
    if(mConnectViewCreators.contains(series))
    {
        QVector<QPair<QString, QString>> deviceInfoList;
        for(auto element : deviceList.asKeyValueRange())
        {
            mSeriesDeviceList.append(element.first);
            deviceInfoList.append(element.second);
        }
        USBAHandle.modifyDevicePIDList(deviceInfoList);
        mConnectView = mConnectViewCreators[series](this);
        mConnectView->setFont(GLBFHandle.font());
        mConnectView->setLanguage(QSettings().value("Language").toString());
        setFont(GLBFHandle.font());
        setCentralWidget(mConnectView);
        connect(mConnectView, &DeviceConnectorViewBase::attributeChanged, this, &DeviceConnector::in_widgetConnectView_attributeChanged, Qt::UniqueConnection);
        connect(&APPSHandle, &AppSettingsSubject::attributeChanged, this, [this](QString objectName, QString attribute, QString value){
            Q_UNUSED(objectName);
            if(attribute == "ModifyLanguage")
            {
                mConnectView->setLanguage(value);
            }
        });
        resize(mBaseSize.width() * QSettings().value("ScaleFactor").toFloat(), mBaseSize.height() * QSettings().value("ScaleFactor").toFloat());
        show();
        if(USBAHandle.checkDriver() && USBAHandle.checkApiVersion())
        {
            mConnectView->showPage(0);
        }
        else
        {
            mConnectView->showPage(2);
        }
        // UpdaterSoftware
        connect(UPDATER_INSTANCE(Software), &UpdaterBase::sigProgress, this, &DeviceConnector::in_Updater_UpdatingSoftware);
        // UpdaterFirmware
        UpdaterFactory::setMethod(UpdaterFactory::UpdaterMethod::Method1);
        connect(UPDATER_INSTANCE(Firmware), &UpdaterBase::sigProgress, this, &DeviceConnector::in_Updater_UpdatingFirmware);
        UpdaterFactory::setMethod(UpdaterFactory::UpdaterMethod::Method2);
        connect(UPDATER_INSTANCE(Firmware), &UpdaterBase::sigProgress, this, &DeviceConnector::in_Updater_UpdatingFirmware);
        if(QSettings().value("AutoCheckForUpdates").toBool())
        {
            mConnectView->showPage(5);
            if(UPDATER_INSTANCE(Software)->fetchUpdateData())
            {
                QString version;
#ifdef Q_OS_WIN
                version = UPDATER_INSTANCE(Software)->getUpdateData().value("Software").toObject().value("Win").toObject().value("Version").toString();
#endif
#ifdef Q_OS_MACOS
                version = UPDATER_INSTANCE(Software)->getUpdateData().value("Software").toObject().value("Mac").toObject().value("Version").toString();
#endif
                if(version > APP_VERSION)
                {
                    mConnectView->modifyVersion(APP_VERSION, version);
                    mConnectView->showPage(6);
                }
                else
                {
                    mConnectView->showPage(0);
                }
            }
            else
            {
                mConnectView->showPage(7);
            }
        }
        else
        {
#ifdef Q_OS_MACOS
            QTimer::singleShot(1,[](){
#endif
                UPDATER_INSTANCE(Software)->fetchUpdateDataAsync();
#ifdef Q_OS_MACOS
            });
#endif
        }
        if(mConnectView->currentPage() == 0)
        {
            mTimerConnector.start();
            in_mTimerConnector_timeout();
        }
    }
    else
    {
        qWarning() << "DeviceConnector::showSeries // series" << series << "is not registered in mConnectViewCreators";
    }
}

