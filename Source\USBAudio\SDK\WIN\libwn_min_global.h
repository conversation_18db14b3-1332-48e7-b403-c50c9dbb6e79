/************************************************************************
 *  Module:       libwn_min_global.h
 *  Description:  global include file for all library modules
 *
 *  Runtime Env.: 32-, 64-bit Windows
 *  Author(s):
 *  Company:      Thesycon GmbH, Germany      http://www.thesycon.de
 ************************************************************************/

#ifndef __libwn_min_global_h__
#define __libwn_min_global_h__

#include "libwn.h"


#endif  //__libwn_min_global_h__

/*************************** EOF **************************************/
