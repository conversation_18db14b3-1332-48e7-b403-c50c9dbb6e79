#include "globalfont.h"
#include "pushbuttongroups1m9.h"
#include "ui_pushbuttongroups1m9.h"


PushButtonGroupS1M9::PushButtonGroupS1M9(QWidget *parent)
    : QWidget(parent)
    , ui(new Ui::PushButtonGroupS1M9)
{
    ui->setupUi(this);
    ui->PushButtonMic1->setCheckable(true);
    ui->PushButtonMic35->setCheckable(true);
    ui->PushButtonMicHP->setCheckable(true);
    ui->PushButtonMic1->setAttribute(Qt::WA_LayoutUsesWidgetRect);
    ui->PushButtonMic35->setAttribute(Qt::WA_LayoutUsesWidgetRect);
    ui->PushButtonMicHP->setAttribute(Qt::WA_LayoutUsesWidgetRect);
    mStyle[0] = "QFrame { image: url(:/Image/PushButtonGroup/PBG13_0.png); }";
    mStyle[1] = "QFrame { image: url(:/Image/PushButtonGroup/PBG13_1.png); }";
    mStyle[2] = "QFrame { image: url(:/Image/PushButtonGroup/PBG13_2.png); }";
    mStyle[3] = "QFrame { image: url(:/Image/PushButtonGroup/PBG13_3.png); }";
    setState("Mic1", "1", false);
    setLanguage("English");
}
PushButtonGroupS1M9::~PushButtonGroupS1M9()
{
    delete ui;
}


// override
void PushButtonGroupS1M9::resizeEvent(QResizeEvent* e)
{
    Q_UNUSED(e);
    mFont.setPointSize(GLBFHandle.getSuitablePointSize(mFont, ui->PushButtonMicHP->text(), ui->PushButtonMicHP->rect()) - 1);
    ui->PushButtonMic1->setFont(mFont);
    ui->PushButtonMic35->setFont(mFont);
    ui->PushButtonMicHP->setFont(mFont);
}


// slot
void PushButtonGroupS1M9::on_PushButtonMic1_clicked(bool checked)
{
    Q_UNUSED(checked);
    setState("Mic1", "1");
}
void PushButtonGroupS1M9::on_PushButtonMic35_clicked(bool checked)
{
    Q_UNUSED(checked);
    setState("Mic35", "1");
}
void PushButtonGroupS1M9::on_PushButtonMicHP_clicked(bool checked)
{
    Q_UNUSED(checked);
    setState("MicHP", "1");
}


// setter & getter
PushButtonGroupS1M9& PushButtonGroupS1M9::setFont(QFont font)
{
    mFont = font;
    QResizeEvent e(size(), size());
    resizeEvent(&e);
    update();
    return *this;
}
PushButtonGroupS1M9& PushButtonGroupS1M9::setLanguage(QString language)
{
    if(language == "English")
    {
        // ui->PushButtonMic1->setText("Mic 1");
        // ui->PushButtonMic35->setText("Mic-35");
        // ui->PushButtonMicHP->setText("Mic-HP");
    }
    else if(language == "Chinese")
    {
        // ui->PushButtonMic1->setText("Mic 1");
        // ui->PushButtonMic35->setText("Mic-35");
        // ui->PushButtonMicHP->setText("Mic-HP");
    }
    return *this;
}
PushButtonGroupS1M9& PushButtonGroupS1M9::setState(QString button, QString state, bool needEmit)
{
    unsigned int bitMask=0;
    QPushButton* currentButton=nullptr;
    if(button == "Mic1")
    {
        bitMask = 0x0001;
        currentButton = ui->PushButtonMic1;
    }
    else if(button == "Mic35")
    {
        bitMask = 0x0002;
        currentButton = ui->PushButtonMic35;
    }
    else if(button == "MicHP")
    {
        bitMask = 0x0003;
        currentButton = ui->PushButtonMicHP;
    }
    if(currentButton != nullptr)
    {
        ui->frame->setStyleSheet(mStyle.value(bitMask));
        QString preItem=mCurrentItem;
        mCurrentItem = button;
        if(preItem != mCurrentItem && needEmit) emit stateChanged(button, state);
    }
    return *this;
}
QString PushButtonGroupS1M9::getState(QString button)
{
    Q_UNUSED(button);
    return mCurrentItem;
}

