#include "globalfont.h"
#include "pushbuttons1m2.h"


PushButtonS1M2::PushButtonS1M2(QWidget* parent)
    : QWidget(parent)
{
    mPushButtonSOLO.setParent(this);
    mPushButtonMUTE.setParent(this);
    mPushButtonSOLO.setText("SOLO");
    mPushButtonMUTE.setText("MUTE");
    connect(&mPushButtonSOLO, SIGNAL(clicked()), this, SLOT(in_mPushButtonSOLO_clicked()), Qt::UniqueConnection);
    connect(&mPushButtonMUTE, SIGNAL(clicked()), this, SLOT(in_mPushButtonMUTE_clicked()), Qt::UniqueConnection);
}
PushButtonS1M2::~PushButtonS1M2()
{

}


// override
void PushButtonS1M2::resizeEvent(QResizeEvent* e)
{
    Q_UNUSED(e);
    // W
    float wPixelPerRatio=size().width() / 100.0;
    int wPushButton=wPixelPerRatio * mWeightWidth;
    int xPushButton=(size().width() - wPushButton) / 2;
    // H
    float hPushButton=size().height() / 5.0;
    mPushButtonSOLO.setGeometry(xPushButton, 0, wPushButton, hPushButton * 2);
    mPushButtonMUTE.setGeometry(xPushButton, hPushButton * 3, wPushButton, hPushButton * 2);
    mFont.setPointSize(GLBFHandle.getSuitablePointSize(mFont, mPushButtonSOLO.text(), mPushButtonSOLO.rect()));
    mPushButtonSOLO.setFont(mFont);
    mPushButtonMUTE.setFont(mFont);
    mRadius = hPushButton * 0.8;
    setPushButtonStateSOLO(mPushButtonStateSOLO);
    setPushButtonStateMUTE(mPushButtonStateMUTE);
}


// slot
void PushButtonS1M2::in_mPushButtonSOLO_clicked()
{
    QString style;
    mPushButtonStateSOLO = !mPushButtonStateSOLO;
    if(mPushButtonStateSOLO)
    {
        style = QString("QPushButton {"
                        "   border-radius: %1px;"
                        "   color: rgb(229, 229, 229);"
                        "   background-color: rgb(217, 169, 61);"
                        "}"
                        "QPushButton:hover {"
                        "   border: 2px solid gray;"
                        "   border-radius: %1px;"
                        "}").arg(mRadius);
    }
    else
    {
        style = QString("QPushButton {"
                        "   border-radius: %1px;"
                        "   color: rgb(161, 161, 161);"
                        "   background-color: rgb(60, 60, 60);"
                        "}"
                        "QPushButton:hover {"
                        "   border: 2px solid gray;"
                        "   border-radius: %1px;"
                        "}").arg(mRadius);
    }
    mPushButtonSOLO.setStyleSheet(style);
    emit buttonStateChanged(buttonSOLO, mPushButtonStateSOLO);
}
void PushButtonS1M2::in_mPushButtonMUTE_clicked()
{
    QString style;
    mPushButtonStateMUTE = !mPushButtonStateMUTE;
    if(mPushButtonStateMUTE)
    {
        style = QString("QPushButton {"
                        "   border-radius: %1px;"
                        "   color: rgb(229, 229, 229);"
                        "   background-color: rgb(149, 40, 37);"
                        "}"
                        "QPushButton:hover {"
                        "   border: 2px solid gray;"
                        "   border-radius: %1px;"
                        "}").arg(mRadius);
    }
    else
    {
        style = QString("QPushButton {"
                        "   border-radius: %1px;"
                        "   color: rgb(161, 161, 161);"
                        "   background-color: rgb(60, 60, 60);"
                        "}"
                        "QPushButton:hover {"
                        "   border: 2px solid gray;"
                        "   border-radius: %1px;"
                        "}").arg(mRadius);
    }
    mPushButtonMUTE.setStyleSheet(style);
    emit buttonStateChanged(buttonMUTE, mPushButtonStateMUTE);
}


// setter & getter
PushButtonS1M2& PushButtonS1M2::setFont(QFont font)
{
    mFont = font;
    QResizeEvent e(size(), size());
    resizeEvent(&e);
    update();
    return *this;
}
PushButtonS1M2& PushButtonS1M2::setPushButtonWeightWidth(int weight)
{
    mWeightWidth = weight;
    QResizeEvent e(size(), size());
    resizeEvent(&e);
    update();
    return *this;
}
PushButtonS1M2& PushButtonS1M2::setPushButtonStateSOLO(bool state)
{
    QString style;
    mPushButtonStateSOLO = state;
    if(mPushButtonStateSOLO)
    {
        style = QString("QPushButton {"
                        "   border-radius: %1px;"
                        "   color: rgb(229, 229, 229);"
                        "   background-color: rgb(217, 169, 61);"
                        "}"
                        "QPushButton:hover {"
                        "   border: 2px solid gray;"
                        "   border-radius: %1px;"
                        "}").arg(mRadius);
    }
    else
    {
        style = QString("QPushButton {"
                        "   border-radius: %1px;"
                        "   color: rgb(161, 161, 161);"
                        "   background-color: rgb(60, 60, 60);"
                        "}"
                        "QPushButton:hover {"
                        "   border: 2px solid gray;"
                        "   border-radius: %1px;"
                        "}").arg(mRadius);
    }
    mPushButtonSOLO.setStyleSheet(style);
    return *this;
}
PushButtonS1M2& PushButtonS1M2::setPushButtonStateMUTE(bool state)
{
    QString style;
    mPushButtonStateMUTE = state;
    if(mPushButtonStateMUTE)
    {
        style = QString("QPushButton {"
                        "   border-radius: %1px;"
                        "   color: rgb(229, 229, 229);"
                        "   background-color: rgb(149, 40, 37);"
                        "}"
                        "QPushButton:hover {"
                        "   border: 2px solid gray;"
                        "   border-radius: %1px;"
                        "}").arg(mRadius);
    }
    else
    {
        style = QString("QPushButton {"
                        "   border-radius: %1px;"
                        "   color: rgb(161, 161, 161);"
                        "   background-color: rgb(60, 60, 60);"
                        "}"
                        "QPushButton:hover {"
                        "   border: 2px solid gray;"
                        "   border-radius: %1px;"
                        "}").arg(mRadius);
    }
    mPushButtonMUTE.setStyleSheet(style);
    return *this;
}
PushButtonS1M2& PushButtonS1M2::setPushButtonClickedSOLO(bool state)
{
    mPushButtonStateSOLO = !state;
    in_mPushButtonSOLO_clicked();
    return *this;
}
PushButtonS1M2& PushButtonS1M2::setPushButtonClickedMUTE(bool state)
{
    mPushButtonStateMUTE = !state;
    in_mPushButtonMUTE_clicked();
    return *this;
}
bool PushButtonS1M2::getPushButtonStateSOLO()
{
    return mPushButtonStateSOLO;
}
bool PushButtonS1M2::getPushButtonStateMUTE()
{
    return mPushButtonStateMUTE;
}

