/************************************************************************
 *
 *  Module:       WnCriticalSection.h
 *
 *  Description:  critical section helper class
 *
 *  Runtime Env.: Win32
 *
 *  Author(s):
 *    Udo <PERSON>,  <EMAIL>
 *
 *  Companies:
 *    Thesycon GmbH, Germany      http://www.thesycon.de
 *
 ************************************************************************/

#ifndef __WnCriticalSection_h__
#define __WnCriticalSection_h__

// optionally put everything into a namespace
#ifdef LIBWN_NAMESPACE
namespace LIBWN_NAMESPACE {
#endif

//
// CRITICAL_SECTION wrapper class
//
class WnCriticalSection
{
public:
    // ctor
    WnCriticalSection()
            { ::InitializeCriticalSection(&mCritSect); }

    // dtor
    ~WnCriticalSection()
            { ::DeleteCriticalSection(&mCritSect); }


    bool
    IsValid() const
            {
                return true;
            }


    // enter section
    void
    Enter()
        { ::EnterCriticalSection(&mCritSect); }

    // leave section
    void
    Leave()
        { ::LeaveCriticalSection(&mCritSect); }


    //
    // Tries to enter the critical section without blocking.
    //
    // Returns true if the critical section was entered, false if not.
    //
#if(_WIN32_WINNT >= 0x0400)
    bool
    TryEnter()
        { return (TRUE == ::TryEnterCriticalSection(&mCritSect)); }
#endif


// implementation
protected:
    CRITICAL_SECTION mCritSect;

// make copy constructor and assignment operator unavailable
PRIVATE_COPY_CONSTRUCTOR(WnCriticalSection)
PRIVATE_ASSIGNMENT_OPERATOR(WnCriticalSection)

};//class WnCriticalSection




//
// WnAutoCriticalSection helper class
//
// Enters a critical section if constructed,
// and leaves it automatically when the object goes out of scope.
//
class WnAutoCriticalSection
{
public:
    // ctor
    WnAutoCriticalSection(
        WnCriticalSection& sect
        )
            : mCritSect(sect)
    {
        mCritSect.Enter();
    }

    // dtor
    ~WnAutoCriticalSection()
    {
        mCritSect.Leave();
    }


// implementation
protected:
    WnCriticalSection& mCritSect; //lint !e1725   class member is a reference

// make copy constructor and assignment operator unavailable
PRIVATE_COPY_CONSTRUCTOR(WnAutoCriticalSection)
PRIVATE_ASSIGNMENT_OPERATOR(WnAutoCriticalSection)

};//class WnAutoCriticalSection

#ifdef LIBWN_NAMESPACE
}
#endif

#endif // __WnCriticalSection_h__

/*************************** EOF **************************************/
