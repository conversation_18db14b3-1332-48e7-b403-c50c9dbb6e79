/************************************************************************
 *
 *  Module:       WnTrace.h
 *
 *  Description:  Win32 trace support
 *
 *  Runtime Env.: Win32
 *
 *  Author(s):
 *    <PERSON><PERSON>,  Udo<PERSON><PERSON><PERSON>@thesycon.de
 *
 *  Companies:
 *    Thesycon GmbH, Germany      http://www.thesycon.de
 *
 ************************************************************************/

#ifndef __WnTrace_h__
#define __WnTrace_h__

// optionally put everything into a namespace
#ifdef LIBWN_NAMESPACE
namespace LIBWN_NAMESPACE {
#endif

//
// set defaults for WNTRACE_ENABLE and WNASSERT_ENABLE
//
#if TB_DEBUG
// debug build
#ifndef WNTRACE_ENABLE
#define WNTRACE_ENABLE  1
#endif
#ifndef WNASSERT_ENABLE
#define WNASSERT_ENABLE 1
#endif

#else
// release build
#ifndef WNTRACE_ENABLE
#define WNTRACE_ENABLE  0
#endif
#ifndef WNASSERT_ENABLE
#define WNASSERT_ENABLE 0
#endif

#endif

// consistency check
#if WNASSERT_ENABLE && !WNTRACE_ENABLE
#error If WNASSERT_ENABLE is 1, WNTRACE_ENABLE must be set to 1 as well.
#endif


// The global trace context used by the WNTRACE macro
extern WnTraceLogContext* gWnTrace;


// init global trace context
void
WnInitTraceModule(
    const WnTraceInfoEntry* traceInfo = NULL, // pointer to array of structs
    HMODULE hModule = NULL,               // in case of DLL, specify the handle passed to DllMain
    WnTraceLogContext* traceLogContext = NULL, // alternative WnTraceLogContext (or subclass) instance
    const GUID& etwTraceProviderGuid = GUID_NULL
    );


// Set trace mask on the global trace context
void
WnSetTraceMask(
    unsigned int newMask
    );

// Get trace mask from the global trace context
unsigned int
WnGetTraceMask();



//
// WNTRACE macro, enabled via WNTRACE_ENABLE macro
//
#if WNTRACE_ENABLE

// returns true if bit bitnb is set in trace mask
#define TRCBIT(bitnb)   ( WnGetTraceMask() & TRCBITMASK(bitnb) )

// WNTRACE expects a constant as bitnb
#define WNTRACE(bitnb,x)   if TRCBIT(bitnb) { const int bn = (bitnb); (void)bn; x; }

// WNTRACEV works with variables as bitnb
#define WNTRACEV(bitnb,x)  if TRCBIT(bitnb) { const int bn = -1; (void)bn; x; }

#else
#define TRCBIT(bitnb)   0
#define WNTRACE(bitnb,x)
#define WNTRACEV(bitnb,x)
#endif



// global trace print functions, mapped to global trace context
// bitnb 0..31, or -1
template<int bitnb> void tprint_impl(const char* fmt, ...);
template<int bitnb> void tprint_impl(const WCHAR* fmt, ...);

// function to be used inside WNTRACE() or WNTRACEV()
#define tprint      tprint_impl<bn>

// function to be used outside of WNTRACE()
#define tprintf     tprint_impl<-1>




// remove existing ASSERT macro
#ifdef WNASSERT
#undef WNASSERT
#endif

//
// WNASSERT macro, enabled via WNASSERT_ENABLE macro
//
#ifdef _lint
#define WNASSERT(condition)   __assert(condition)
#else
#if WNASSERT_ENABLE
#define WNASSERT(condition)   WnAssertImpl(condition, #condition, __FILE__, __LINE__)
#else
#define WNASSERT(condition)
#endif
#endif


// assert implementation
__forceinline
void
WnAssertImpl(int cond, const char* conds, const char* file, unsigned int line)
{
    if ( !cond ) {
        // print trace message
        tprintf("\nFATAL: ASSERTION FAILED: (%s)\n" "  %s(%u)\n", conds, file, line );
    }
}



//
// In addition to the above trace functionality we define a very
// simple trace function that outputs to OutputDebugString.
// This is mainly used in the implementation of the trace modules itself
// in order to avoid recursion.
//
#if TB_DEBUG
void
WnDbgOutput(
    const char* format,
    ...
    );
#else
inline
void
WnDbgOutput(
    const char* /*format*/,
    ...
    )
        {}  // empty in case of release build
#endif

#ifdef LIBWN_NAMESPACE
}
#endif

#endif //__WnTrace_h__

/********************************* EOF *********************************/
