#ifndef CircleS1M1_H
#define CircleS1M1_H

#include <QWidget>

class CircleS1M1 : public QWidget
{
    Q_OBJECT

public:
    enum Status{
        invalid=-1,
        start,
        detect,
        complete,
        error
    };

public:
    explicit CircleS1M1(QWidget *parent = nullptr);
    void setStatus(Status status);
    void setFont(const QFont& font);
    void setValue(float value);
    void setMaxValue(float value) { mTotalValue = value; }

protected:
    void paintEvent(QPaintEvent* e)override;
    QColor getOutBg();
    QColor getInnerBg();

private:
    Status mStatus = start;
    QColor mStartOutBG{42, 42, 42};
    QColor mDetectOutBG{67, 207, 124};
    QColor mCompleteOutBG{67, 207, 124};
    QColor mErrorOutBG{255, 87, 51};
    QColor mStartInnerBG{31, 31, 31};
    QColor mDetectInnerBG{31, 31, 31};
    QColor mCompleteInnerBG{"#24362b"};
    QColor mErrorInnerBG{"#3c2622"};
    QColor mTextColor{255, 255, 255};
    float    mCurValue=0;
    float    mTotalValue=15;
    QFont mFont;
};

#endif // CircleS1M1_H
