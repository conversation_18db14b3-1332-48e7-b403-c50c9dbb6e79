/************************************************************************
 *
 *  Module:       WnModuleFileName.cpp
 *
 *  Description:  Wrapper for a module filename
 *
 *  Runtime Env.: Win32
 *
 *  Author(s):
 *    <PERSON><PERSON>,  Udo<PERSON>@thesycon.de
 *    <PERSON>, <PERSON>@thesycon.de
 *
 *  Companies:
 *    Thesycon GmbH, Germany      http://www.thesycon.de
 *
 ************************************************************************/

#include "libwn_min_global.h"

// optionally put everything into a namespace
#ifdef LIBWN_NAMESPACE
namespace LIBWN_NAMESPACE {
#endif

// ctor
WnModuleFileName::WnModuleFileName()
{
    Clear();
}


bool
WnModuleFileName::IsValid()
{
    return ( 0 != TbStringLen(mModuleName) );
}


void
WnModuleFileName::Clear()
{
    TB_ZERO_ARRAY(mModuleName);
    TB_ZERO_ARRAY(mDrive);
    TB_ZERO_ARRAY(mDir);
    TB_ZERO_ARRAY(mBaseName);
    TB_ZERO_ARRAY(mExtension);
}

WNERR
WnModuleFileName::InitWithModuleHandle(HMODULE hModule /*= NULL*/)
{
    WNERR err = ERROR_SUCCESS;

    Clear();

    DWORD len = ::GetModuleFileName(hModule, mModuleName, TB_ARRAY_ELEMENTS(mModuleName));
    if (0 == len) {
        // failed
        err = ::GetLastError();
        WnDbgOutput(__FUNCTION__": ERROR: GetModuleFileName failed, err=0x%X\n", err);
        return err;
    }

    InitPathComponents();

    return ERROR_SUCCESS;
}

WNERR
WnModuleFileName::InitWithModuleName(const TCHAR* moduleName)
{
    Clear();

    // copy module name to handle
    TbStringNCopyToArray(mModuleName, moduleName);

    InitPathComponents();

    return ERROR_SUCCESS;
}

#ifdef UNDER_CE

void
WnModuleFileName::InitPathComponents()
{
    // NOTE: under CE _tsplitpath_s is not available, so we roll our own

    const unsigned int len = TbStringLen(mModuleName);
    unsigned int idx = len-1;

    // extract extension
    for (;;) {
        if (mModuleName[idx] == '.') {
            unsigned int count = len-idx;
            count = TB_MIN(count, (_MAX_EXT-1));
            TbCopyMemory(mExtension, &mModuleName[idx], (count * sizeof(TCHAR)));
            break;
        }
        if (0 == idx) {
            break;
        }
        idx--;
    }

    idx = len-1;
    unsigned int endpos = len;
    // extract basename and path
    for (;;) {
        if ((endpos == len) && (mModuleName[idx] == '.')) {
            endpos = idx;
        }
        if (mModuleName[idx] == '\\') {
            unsigned int count = endpos-idx-1;
            count = TB_MIN(count, (_MAX_FNAME-1));
            // basename
            TbCopyMemory(mBaseName, &mModuleName[idx+1], (count * sizeof(TCHAR)));
            // path
            TbCopyMemory(mDir, &mModuleName[0], (TB_MIN(idx+1, (_MAX_DIR-1)) * sizeof(TCHAR)));
            break;
        }
        if (0 == idx) {
            break;
        }
        idx--;
    }
}

#else

void
WnModuleFileName::InitPathComponents()
{
    // split module filename in components
    _tsplitpath_s(
        mModuleName, // in
        mDrive, TB_ARRAY_ELEMENTS(mDrive),
        mDir, TB_ARRAY_ELEMENTS(mDir),
        mBaseName, TB_ARRAY_ELEMENTS(mBaseName),
        mExtension, TB_ARRAY_ELEMENTS(mExtension)
        );
}

#endif

#ifdef LIBWN_NAMESPACE
}
#endif

/***************************** EOF **************************************/
