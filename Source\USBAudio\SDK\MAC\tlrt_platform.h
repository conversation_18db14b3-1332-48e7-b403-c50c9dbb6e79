/************************************************************************

    Description:
        platform and compiler detection/abstraction

    Author(s):
        <PERSON>
        
    Thesycon Software Solutions GmbH & Co. KG, Germany, www.thesycon.de

************************************************************************/

#ifndef __tlrt_platform_h__
#define __tlrt_platform_h__

/*
    This file automatically detects the following settings.
    Client code can use these symbols to implement compile-time decisions.

    TLDEBUG
        Defined as 1 if this is a debug build.
        Defined as 0 if this is a release build.
    usage:
        #if TLDEBUG
        #endif


    TLFUNCNAME
        macro that resolves to current function name
        Note: TLFUNCNAME is like a predefined identifier and corresponds to
            const char TLFUN<PERSON>NAME[] = "MyFunction";
        Thus, string concatenation does not work and it must be used this way:
            printf("%s: error: xxx", TLFUNCNAME);

    TLFUNCNAME_DEBUG
        Same as TLFUNCNAME in debug build. Empty string in release build.


    TLSOURCE_FILE_NAME
        Macro that will be mapped to SOURCE_FILE_NAME if it is specified on the compiler command line,
        or to __FILE__ if SOURCE_FILE_NAME is not defined.


    TL_INLINE
        wrapper for the inline keyword, see below for compiler specifics
        NOTE: For a good discussion of the inline confusion, check out http://www.greenend.org.uk/rjk/tech/inline.html

    TL_ALWAYS_INLINE
        wrapper for GCC __attribute__(always_inline), MS __forceinline etc.

    TL_NO_INLINE
        wrapper for GCC __attribute__(noinline)

    TL_IRQ_HANDLER
        compiler-specific function modifier to be used for interrupt service routines


    TLRT_LITTLE_ENDIAN
        defined if target platform is little endian
    usage:
        #ifdef TLRT_LITTLE_ENDIAN
        #endif

    TLRT_BIG_ENDIAN
        defined if target platform is big endian
    usage:
        #ifdef TLRT_BIG_ENDIAN
        #endif

    TLRT_ARCH_32BIT
        defined if the target architecture is 32 bit.
    usage:
        #ifdef TLRT_ARCH_32BIT
        #endif

    TLRT_ARCH_64BIT
        defined if the target architecture is 64 bit.
    usage:
        #ifdef TLRT_ARCH_64BIT
        #endif

    TLRT_ARCH_INTEL
        Microsoft only: defined if the target architecture is Intel 32 or 64 bit.
    usage:
        #ifdef TLRT_ARCH_INTEL
        #endif

    TLRT_ARCH_ARM
        Microsoft only: defined if the target architecture is ARM 32 or 64 bit.
    usage:
        #ifdef TLRT_ARCH_ARM
        #endif

    TLRT_COMPILER_MICROSOFT
        defined if Microsoft compiler is detected.
    usage:
        #ifdef TLRT_COMPILER_MICROSOFT
        #endif

    TLRT_COMPILER_MICROSOFT_KERNEL_MODE
        defined if Windows kernel mode

    TLRT_COMPILER_MICROSOFT_USER_MODE
        defined if Windows user mode (Win32)

    TLRT_COMPILER_MICROSOFT_UNDER_CE
        defined if Windows CE


    TLRT_COMPILER_GNU
        defined if GNU compiler is detected.
        A more specific symbol is defined in addition to TLRT_COMPILER_GNU, see below.
    usage:
        #ifdef TLRT_COMPILER_GNU
        #endif

    TLRT_COMPILER_CLANG
        defined if CLANG compiler is detected.
        A more specific symbol is defined in addition to TLRT_COMPILER_CLANG, see below.
    usage:
        #ifdef TLRT_COMPILER_CLANG
        #endif

    TLRT_COMPILER_ARMCC
        defined if ARM (Keil) compiler is detected.
    usage:
        #ifdef TLRT_COMPILER_ARMCC
        #endif

    TLRT_COMPILER_C99
        defined if the compiler is C99 compliant
    usage:
        #ifdef TLRT_COMPILER_C99
        #endif

    TLRT_COMPILER_CPP_STANDARD
        Set to 17, 14, or 11 depending on the C++ standard supported by the compiler.
        Not defined in C mode.

    TLRT_INTELLISENSE_BUILD
        defined if an IntelliSense build runs in the context of an IDE
    usage:
        #ifdef TLRT_INTELLISENSE_BUILD
        #endif

    TLRT_STDINT_AVAILABLE
        Defined as 1 if stdint.h (containing fixed width integer types) is available.
        Defined as 0 if stdint.h (containing fixed width integer types) is not available.

    This file also defines the following helper macros:
    TL_COMPILE_TIME_ASSERT(expression)
    TL_CHECK_SIZEOF(type, expected_size)

*/


/* in C++ mode, use standard inline keyword */
#if defined(__cplusplus)
#define TL_INLINE inline
#endif

/* C99 compliant compiler */
/* Note:  __STDC_VERSION__ is not defined in C++ mode */
#if defined(__STDC_VERSION__) && (__STDC_VERSION__ >= 199901L)
#define TLRT_COMPILER_C99
/* C99 defines the standard inline keyword */
#ifndef TL_INLINE
    #define TL_INLINE inline
#endif
#endif


/******* GNU/Linux (i386 or ARM) *******/
#ifndef TLRT_COMPILER_DETECTED
#if defined(__GNUC__) && defined(__linux__)
    #define TLRT_COMPILER_GNU
    #define TLRT_COMPILER_GNU_LINUX
    #define TLRT_COMPILER_DETECTED

    // __func__ is part of the C++ standard, see https://en.cppreference.com/w/c/language/function_definition#func
    // __PRETTY_FUNCTION__ includes the complete function signature
    #define TLFUNCNAME __func__

    #ifndef TL_INLINE
        #define TL_INLINE inline
    #endif
    #ifndef TL_ALWAYS_INLINE
        #define TL_ALWAYS_INLINE __attribute__((always_inline))
    #endif
    #ifndef TL_NO_INLINE
        #define TL_NO_INLINE __attribute__((noinline))
    #endif
    #if defined(__i386__) || defined(__amd64__)
        /* Intel: defaults to little endian */
        #ifndef TLRT_LITTLE_ENDIAN
            #define TLRT_LITTLE_ENDIAN
        #endif
        #if defined(TLRT_BIG_ENDIAN)
            #error Linux/Intel is not big endian.
        #endif
        #if defined(__amd64__)
            #define TLRT_ARCH_64BIT
        #else
            #define TLRT_ARCH_32BIT
        #endif
    #elif defined(__arm__) || defined(__aarch64__)
        /* Linux on ARM */
        /* ARM may be little or big endian */
        #if !defined(TLRT_BIG_ENDIAN) && !defined(TLRT_LITTLE_ENDIAN)
            #if (__BYTE_ORDER__ == __ORDER_LITTLE_ENDIAN__)
                #define TLRT_LITTLE_ENDIAN
            #else
                #define TLRT_BIG_ENDIAN
            #endif
        #endif
        #if defined(__aarch64__)
            #define TLRT_ARCH_64BIT
        #else
            #define TLRT_ARCH_32BIT
        #endif
    #else
        /* unknown: */
        #error unknown GNU/Linux variant
    #endif
#endif
#endif /* #ifndef TLRT_COMPILER_DETECTED */


/******* GNU ARM embedded *******/
#ifndef TLRT_COMPILER_DETECTED
#if defined(__GNUC__) && defined(__arm__) && !defined(__linux__)
    #define TLRT_COMPILER_GNU
    #define TLRT_COMPILER_GNUARM
    #define TLRT_COMPILER_DETECTED

    // We can use __func__ or __PRETTY_FUNCTION__ which also contains the function signature.
    // See https://gcc.gnu.org/onlinedocs/gcc/Function-Names.html
    #define TLFUNCNAME __func__

    // GCC v12 supports __FILE_NAME__, see:
    //   https://gcc.gnu.org/onlinedocs/cpp/Common-Predefined-Macros.html#Common-Predefined-Macros
    //   https://gcc.gnu.org/git/gitweb.cgi?p=gcc.git;h=1a9b3f04c11eb467a8dc504a37dad57a371a0d4c
    #if ( __GNUC__>=12 && !defined(TLSOURCE_FILE_NAME) )
        // workaround required because VSCode Intellisense does not know __FILE_NAME__
        #ifdef __INTELLISENSE__
        #define TLSOURCE_FILE_NAME __FILE__
        #else
        #define TLSOURCE_FILE_NAME __FILE_NAME__
        #endif
    #endif

    /* ARM may be little or big endian */
    #if !defined(TLRT_BIG_ENDIAN) && !defined(TLRT_LITTLE_ENDIAN)
        #if (__BYTE_ORDER__ == __ORDER_LITTLE_ENDIAN__)
        #define TLRT_LITTLE_ENDIAN
        #else
        #define TLRT_BIG_ENDIAN
        #endif
    #endif
    #define TLRT_ARCH_32BIT

    /* ISR modifier */
    #define TL_IRQ_HANDLER  __attribute__((interrupt("IRQ")))
    // Note: GCC 12 introduces:  warning: FP registers might be clobbered despite 'interrupt' attribute.
    // We could simply remove the interrupt attribute because it has no effect on Cortex M anyway.
    // But for better compatibility, we should keep using the attribute, so let's define some macros to disable and re-enable this specific warning.
    #define TL_IRQ_HANDLER_WARNING_OFF      _Pragma("GCC diagnostic push") _Pragma("GCC diagnostic ignored \"-Wattributes\"")
    #define TL_IRQ_HANDLER_WARNING_RESTORE  _Pragma("GCC diagnostic pop")

    /* inline */
    #ifndef TL_INLINE
        #define TL_INLINE inline
    #endif
    #ifndef TL_ALWAYS_INLINE
        #define TL_ALWAYS_INLINE __attribute__((always_inline))
    #endif
    #ifndef TL_NO_INLINE
        #define TL_NO_INLINE __attribute__((noinline))
    #endif

    // unaligned = pack 1, see cmsis_gcc.h __PACKED
    #define TL_UNALIGNED    __attribute__((packed, aligned(1)))

    /* detect debug or release build */
    #ifndef TLDEBUG
        #if defined(DEBUG)
        #define TLDEBUG 1
        #else
        #define TLDEBUG 0
        #endif
    #endif

    // detect Visual Studio IntelliSense build (VisualGDB case)
    // Not needed for VSCode
    //#ifdef __INTELLISENSE__
    //    #define TLRT_INTELLISENSE_BUILD
    //#endif

#endif
#endif /* #ifndef TLRT_COMPILER_DETECTED */


/******* Microsoft *******/
#ifndef TLRT_COMPILER_DETECTED
#if defined(_MSC_VER) || defined(RC_INVOKED)  // includes RC
    /* Microsoft compiler detected */
    #define TLRT_COMPILER_MICROSOFT
    #define TLRT_COMPILER_DETECTED
    #define TLFUNCNAME  __FUNCTION__
    #ifndef TL_INLINE
        #define TL_INLINE __inline
    #endif
    #ifndef TL_ALWAYS_INLINE
        #define TL_ALWAYS_INLINE __forceinline
    #endif
    #ifndef TL_NO_INLINE
        #define TL_NO_INLINE __declspec(noinline)
    #endif
    // dummy definition, required to make IntelliSense happy
    #define TL_IRQ_HANDLER

    #ifdef UNDER_CE
        /* Windows CE */
        #define TLRT_COMPILER_MICROSOFT_UNDER_CE
        #define TLRT_COMPILER_MICROSOFT_USER_MODE
    #else
        #if defined(_KERNEL_MODE) || defined(ENV_KERNEL_MODE)
            /* Windows kernel mode (/kernel switch defines _KERNEL_MODE) */
            #define TLRT_COMPILER_MICROSOFT_KERNEL_MODE
            // Note: In Windows kernel mode we use the MSVC standard C++ library instead
            // of the restricted subset from the EWDK (Windows Kits\10\Include\10.0.22621.0\km\crt).
            // See also CRT_IncludePath_Override property in EWDK_Build_vs2022.props.
            // Hence, stdint.h is available in Windows kernel mode as well.
            #define TLRT_STDINT_AVAILABLE   1
        #else
            /* Windows user mode */
            #define TLRT_COMPILER_MICROSOFT_USER_MODE
        #endif
    #endif

    /* always little endian */
    #ifndef TLRT_LITTLE_ENDIAN
        #define TLRT_LITTLE_ENDIAN
    #endif
    #if defined(TLRT_BIG_ENDIAN)
        #error Microsoft compiler is not big endian.
    #endif

    #if defined(_M_X64) || defined(_M_ARM64)
        #define TLRT_ARCH_64BIT
    #else
        #define TLRT_ARCH_32BIT
    #endif

    #if defined(_M_IX86) || defined(_M_X64)
        #define TLRT_ARCH_INTEL
    #endif
    #if defined(_M_ARM) || defined(_M_ARM64)
        #define TLRT_ARCH_ARM
    #endif

    // DBG is always defined, either as 0 or 1, newer WDKs seem not to define DBG on free builds
    #ifndef DBG
    #define DBG 0
    #endif

    /* detect debug or release build */
    #ifndef TLDEBUG
        #if DBG || defined(_DEBUG) || defined(DEBUG)
        #define TLDEBUG 1
        #else
        #define TLDEBUG 0
        #endif
    #endif

    // detect Visual Studio IntelliSense build
    #ifdef __INTELLISENSE__
        #define TLRT_INTELLISENSE_BUILD
    #endif

    // We require VS2015 or later. Modern C++ support is incomplete in earlier versions.
    #if (_MSC_VER < 1900)
        #error Visual Studio 2015 is required at a minimum.
    #endif

    // detect C++17 mode
    // Note: MS does not define the numeric value of __cplusplus correctly, so we use their proprietary _MSVC_LANG
    #if defined(__cplusplus)
        #if defined(_MSVC_LANG) && (_MSVC_LANG >= 201703L)
            #define TLRT_COMPILER_CPP_STANDARD  17
            // suppress warnings about deprecated std::codecvt_utf8_utf16
            #ifndef _SILENCE_CXX17_CODECVT_HEADER_DEPRECATION_WARNING
            #define _SILENCE_CXX17_CODECVT_HEADER_DEPRECATION_WARNING
            #endif
        #else
            // assume C++14 by default
            #define TLRT_COMPILER_CPP_STANDARD  14
        #endif
    #endif

#endif
#endif /* #ifndef TLRT_COMPILER_DETECTED */


/******* clang/APPLE *******/
#ifndef TLRT_COMPILER_DETECTED
#if defined(__clang__) && defined(__APPLE__)
    #define TLRT_COMPILER_CLANG
    #define TLRT_COMPILER_APPLE
    #if defined(KERNEL)
        #define TLRT_COMPILER_APPLE_KERNEL_MODE
    #elif __is_target_os(driverkit) //defined(TARGET_OS_DRIVERKIT)
        #define TLRT_COMPILER_APPLE_DRIVERKIT_MODE
    #else
        #define TLRT_COMPILER_APPLE_USER_MODE
    #endif
    #define TLRT_COMPILER_DETECTED

    // __func__ is part of the C++ standard, see https://en.cppreference.com/w/c/language/function_definition#func
    // __PRETTY_FUNCTION__ includes the complete function signature
    #define TLFUNCNAME __func__

    #ifndef TL_INLINE
        #define TL_INLINE inline
    #endif
    #ifndef TL_ALWAYS_INLINE
        #define TL_ALWAYS_INLINE __attribute__((always_inline))
    #endif
    #ifndef TL_NO_INLINE
        #define TL_NO_INLINE __attribute__((noinline))
    #endif
    /* we assume Intel CPU, always little endian */
    #ifndef TLRT_LITTLE_ENDIAN
        #define TLRT_LITTLE_ENDIAN
    #endif
    #if defined(TLRT_BIG_ENDIAN)
        #error Apple/Intel is not big endian.
    #endif
    #if defined(__arm64__) || defined(__aarch64__)
        #define TLRT_COMPILER_APPLE_ARM
        #define TLRT_ARCH_ARM
        #define TLRT_ARCH_64BIT
    #elif defined(__x86_64__)
        #define TLRT_COMPILER_APPLE_INTEL
        #define TLRT_ARCH_INTEL
        #define TLRT_ARCH_64BIT
    #else
        #error Unknown Apple compiler arch.
    #endif
    /* detect debug or release build */
    #ifndef TLDEBUG
            #if defined(DEBUG)
                    #define TLDEBUG 1
            #else
                    #define TLDEBUG 0
            #endif
    #endif
#else
    #error Unknown Apple compiler.
#endif
#endif /* #ifndef TLRT_COMPILER_DETECTED */



/*
 * defaults
 */

// C++ level detection for compilers other than Microsoft.
#if !defined(TLRT_COMPILER_CPP_STANDARD) && defined(__cplusplus)
    #if (__cplusplus >= 201703L)
        #define TLRT_COMPILER_CPP_STANDARD  17
    #elif (__cplusplus >= 201402L)
        #define TLRT_COMPILER_CPP_STANDARD  14
    #else
        #error C++14 or later is required.
    #endif
#endif

// function name is an empty string by default
#ifndef TLFUNCNAME
#define TLFUNCNAME ""
#endif

// name of the current source file
#ifndef TLSOURCE_FILE_NAME
#ifdef SOURCE_FILE_NAME
#define TLSOURCE_FILE_NAME SOURCE_FILE_NAME
#else
#define TLSOURCE_FILE_NAME __FILE__
#endif
#endif

// packed pointer attribute is empty by default
#ifndef TL_PACKED_PTR
#define TL_PACKED_PTR
#endif

// unaligned attribute is empty by default
#ifndef TL_UNALIGNED
#define TL_UNALIGNED
#endif


#ifndef TLRT_STDINT_AVAILABLE
    #ifdef TLRT_COMPILER_C99
        // C99 compliant compiler, stdint.h should be available
        #define TLRT_STDINT_AVAILABLE       1
    #elif defined(__cplusplus) && (__cplusplus >= 201103L)
        // C++ 11 or later compliant compiler, stdint.h should be available
        #define TLRT_STDINT_AVAILABLE       1
    #elif defined(TLRT_COMPILER_MICROSOFT_USER_MODE) && (_MSC_VER >= 1900)
        // Visual Studio compiler does not set __cplusplus correctly
        // (i.e. is still set to 199711L). But at least Visual Studio 2015
        // supports stdint.h when compiling for user-mode.
        #define TLRT_STDINT_AVAILABLE       1
    #else
        // unknown compiler, assume that stdint.h is not available
        #define TLRT_STDINT_AVAILABLE       0
    #endif
#endif


/*
 * some checks
 */
#if !defined(TLRT_COMPILER_DETECTED)
    #error Compiler not detected.
#endif

#if ( (defined(TLRT_BIG_ENDIAN) + defined(TLRT_LITTLE_ENDIAN)) != 1 )
    #error Either little or big endian must be defined.
#endif

#if ( (defined(TLRT_ARCH_32BIT) + defined(TLRT_ARCH_64BIT)) != 1 )
    #error Either 32 bit or 64 bit arch must be defined.
#endif

#if !defined(TL_INLINE)
    #error TL_INLINE not defined for this compiler.
#endif

#if !defined(TL_ALWAYS_INLINE)
    #error TL_ALWAYS_INLINE not defined for this compiler.
#endif

#ifndef TLDEBUG
    #error TLDEBUG must be defined. Add either TLDEBUG=0 or TLDEBUG=1 to your makefile or project settings.
#endif



#if defined(TLRT_COMPILER_APPLE_KERNEL_MODE)
// Apple kernel mode

#if defined(__cplusplus)
// C++
    namespace std
    {
        typedef decltype(nullptr) nullptr_t;
    }
    #include <sys/types.h>
#else
// C
    #include <stddef.h>
#endif

// standard C runtime
#include <stdarg.h>
#include <stdint.h>
#include <string.h>

#else
// other than Apple kernel mode

#if defined(__cplusplus)
    // C++
    #include <cstddef>
#else
    // C
    #include <stddef.h>
#endif

// standard C runtime
#include <stdarg.h>
#include <stdlib.h>
#include <string.h>

#endif



/*
 * Detection of OS environment
 */

#if defined(TLRT_COMPILER_MICROSOFT_USER_MODE)
#define TLRT_OS_ENV_WINDOWS 1
#ifdef UMDF_VERSION_MAJOR
#define TLRT_OS_ENV_WINDOWS_UMDF 1  /* together with TLRT_OS_ENV_WINDOWS */
#endif
#endif
#if defined(TLRT_COMPILER_MICROSOFT_KERNEL_MODE)
#define TLRT_OS_ENV_WINDOWS_KERNEL 1
#ifdef KMDF_VERSION_MAJOR
#define TLRT_OS_ENV_WINDOWS_KMDF 1  /* together with TLRT_OS_ENV_WINDOWS_KERNEL */
#endif
#endif
#if defined(TLRT_COMPILER_GNU_LINUX)
#define TLRT_OS_ENV_LINUX 1
#endif
#if defined(TLRT_COMPILER_APPLE_USER_MODE)
#define TLRT_OS_ENV_MACOS 1
#endif
#if defined(TLRT_COMPILER_APPLE_KERNEL_MODE)
#define TLRT_OS_ENV_MACOS_KERNEL 1
#endif
#if defined(TLRT_COMPILER_APPLE_DRIVERKIT_MODE)
#define TLRT_OS_ENV_MACOS 1
#define TLRT_OS_ENV_MACOS_DRIVERKIT 1
#endif
#if defined(TLRT_COMPILER_GNUARM)
#define TLRT_OS_ENV_EMBEDDED 1
#endif

// TLRT_OS_ENV_* constants are always defined, and either 0 or 1
#ifndef TLRT_OS_ENV_WINDOWS
#define TLRT_OS_ENV_WINDOWS 0
#endif
#ifndef TLRT_OS_ENV_WINDOWS_KERNEL
#define TLRT_OS_ENV_WINDOWS_KERNEL 0
#endif
#ifndef TLRT_OS_ENV_WINDOWS_UMDF
#define TLRT_OS_ENV_WINDOWS_UMDF 0
#endif
#ifndef TLRT_OS_ENV_WINDOWS_KMDF
#define TLRT_OS_ENV_WINDOWS_KMDF 0
#endif
#ifndef TLRT_OS_ENV_LINUX
#define TLRT_OS_ENV_LINUX 0
#endif
#ifndef TLRT_OS_ENV_MACOS
#define TLRT_OS_ENV_MACOS 0
#endif
#ifndef TLRT_OS_ENV_MACOS_KERNEL
#define TLRT_OS_ENV_MACOS_KERNEL 0
#endif
#ifndef TLRT_OS_ENV_EMBEDDED
#define TLRT_OS_ENV_EMBEDDED 0
#endif

// check
#if ( (TLRT_OS_ENV_WINDOWS + TLRT_OS_ENV_WINDOWS_KERNEL + TLRT_OS_ENV_LINUX + TLRT_OS_ENV_MACOS + TLRT_OS_ENV_MACOS_KERNEL + TLRT_OS_ENV_EMBEDDED) != 1 )
#error Exactly one of the TLRT_OS_ENV constants must be set to 1.
#endif


/*
 * some basic utilities
 */


//
// TL_COMPILE_TIME_ASSERT() for compile-time assertions.
// An assertion failure results in error C2118: negative subscript.
//
#ifdef _lint
/* don't confuse lint with this special stuff */
/*lint -emacro( {19}, TL_COMPILE_TIME_ASSERT) */
#ifdef TL_COMPILE_TIME_ASSERT
#undef TL_COMPILE_TIME_ASSERT
#endif
#define TL_COMPILE_TIME_ASSERT(exp)
#else

/* Default implementation that is active if TL_COMPILE_TIME_ASSERT was not defined in the compiler-specific section above. */
/* Our default implementation is based on an external variable declaration which seems to work with most compilers. */
#ifndef TL_COMPILE_TIME_ASSERT
/* if __COUNTER__ is supported by the compiler then we use it to generate unique identifiers */
#ifdef __COUNTER__
#define TL_COMPILE_TIME_ASSERT(exp) TL_COMPILE_TIME_ASSERT_TAG((exp), __COUNTER__)
#else
#define TL_COMPILE_TIME_ASSERT(exp) TL_COMPILE_TIME_ASSERT_TAG((exp), 0)
#endif
/* use this variant with a unique tag name if linkage specifier conflicts are reported by the compiler */
#define TL_COMPILE_TIME_ASSERT_TAG(exp,tag) TL_COMPILE_TIME_ASSERT_impl((exp), tag)
/* implementation helper */
#define TL_COMPILE_TIME_ASSERT_impl(exp,tag) extern char __tlrt_compile_time_assert_##tag [1 - 2*!(exp)]
#endif

#endif // _lint


//
// sizeof compile time check
//
#ifdef _lint
/* don't confuse lint with this special stuff */
/*lint -emacro( {19}, TL_CHECK_SIZEOF) */
#define TL_CHECK_SIZEOF(type,size)
#else

#ifndef TL_CHECK_SIZEOF
#define TL_CHECK_SIZEOF(type,size)  TL_COMPILE_TIME_ASSERT(sizeof(type)==(size))
#endif

#endif // _lint


//
// Calculate the byte offset of a field in a structure of type structType.
// Mapped to the standard macro offsetof from stddef.h, if possible.
// NOTE: This cannot be used with C++ classes.
//
#ifdef _lint
// always use standard offsetof operator for lint
//lint -emacro(413, TL_OFFSETOF_FIELD) */
//lint -emacro(413, offsetof) */
//lint -emacro(545, offsetof) */
#define TL_OFFSETOF_FIELD(structType, fieldName) offsetof(structType, fieldName)
#else
#if defined(TLRT_COMPILER_GNU) || defined(TLRT_COMPILER_CLANG)
// GNU supports a special intrinsic that should be used to avoid warnings with TL_COMPILE_TIME_ASSERT
#define TL_OFFSETOF_FIELD(structType, fieldName) __builtin_offsetof(structType, fieldName)
#else
// standard offsetof operator, typically a macro in stddef.h
#define TL_OFFSETOF_FIELD(structType, fieldName) offsetof(structType, fieldName)
#endif
#endif

//
// Calculate the size of a field in a structure of type structType.
//
#define TL_SIZEOF_FIELD(structType, fieldName) ( sizeof(((structType*)0)->fieldName) )

//
// Calculate the pointer to the base of a surrounding struct given its type and a pointer to a field within the struct.
// NOTE: This cannot be used with C++ classes.
//
//lint -emacro(413, TL_BASE_POINTER_FROM_FIELD)
//lint -emacro(826, TL_BASE_POINTER_FROM_FIELD)
#define TL_BASE_POINTER_FROM_FIELD(fieldPtr, structType, fieldName) ( (structType*)(((char*)(fieldPtr)) - TL_OFFSETOF_FIELD(structType,fieldName)) )



//
// macro to mark function arguments or variables unused,
// this will make lint and /W4 happy
//
#define TL_UNUSED(x)       ((void)(x))

// the same but active in release builds only
#if TLDEBUG
#define TL_RELEASE_BUILD_UNUSED(x)
#else
#define TL_RELEASE_BUILD_UNUSED(x) TL_UNUSED(x)
#endif



//
// Microsoft compiler only:
// issue a compile time warning message,
// formatted properly so that VS and the WDK build utility can interpret it.
// usage example:
// #ifdef FOO
// #pragma TL_COMPILE_TIME_WARNING("FOO active.")
// #endif
//
#if defined(TLRT_COMPILER_MICROSOFT)
#define TL_COMPILE_TIME_WARNING(txt) message(__FILE__ "(" TL_TO_STRING(__LINE__) ")" " : warning : " txt)
#endif


// Microsoft compiler:
#if defined(TLRT_COMPILER_MICROSOFT)

// build target string, useful in debug trace messages
#if defined(_M_AMD64)
#define TL_BUILD_TARGET_STR  "x64"
#elif defined(_M_IX86)
#define TL_BUILD_TARGET_STR  "x86"
#elif defined(_M_ARM64)
#define TL_BUILD_TARGET_STR  "ARM64"
#elif defined(_M_ARM)
#define TL_BUILD_TARGET_STR  "ARM"
#else
#define TL_BUILD_TARGET_STR  "build_target_unknown"
#endif


#elif defined(TLRT_COMPILER_GNU) || defined(TLRT_COMPILER_CLANG)

// build target string, useful in debug trace messages
#if defined(__amd64__)
#define TL_BUILD_TARGET_STR  "x64"
#elif defined(__i386__)
#define TL_BUILD_TARGET_STR  "x86"
#elif defined(__aarch64__)
#define TL_BUILD_TARGET_STR  "ARM64"
#elif defined(__arm__)
#define TL_BUILD_TARGET_STR  "ARM"
#else
#define TL_BUILD_TARGET_STR  "build_target_unknown"
#endif

#else

#error TL_BUILD_TARGET_STR not defined.

#endif



// useful strings that can be used in debug trace messages
#if TLDEBUG
#define TL_BUILD_TYPE_STR "debug"
#else
#define TL_BUILD_TYPE_STR "release"
#endif


// FUNCNAME that is available in DEBUG build only
#if TLDEBUG
#define TLFUNCNAME_DEBUG    TLFUNCNAME
#else
#define TLFUNCNAME_DEBUG    ""
#endif



#endif

/*** EOF ***/
