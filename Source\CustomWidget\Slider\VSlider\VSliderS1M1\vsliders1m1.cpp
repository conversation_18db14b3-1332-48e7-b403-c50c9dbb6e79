#include <QRegularExpressionValidator>

#include "globalfont.h"
#include "vsliders1m1.h"


VSliderS1M1::VSliderS1M1(QWidget *parent)
    : QWidget(parent)
{
    mLineEdit.setParent(this);
    mLineEdit.setAlignment(Qt::AlignCenter);
    mSlider.setParent(this);
    mSlider.setOrientation(Qt::Vertical);
    mSlider.setPageStep(5);
    mSlider.installEventFilter(this);
    connect(&mLineEdit, SIGNAL(editingFinished()), this, SLOT(in_mLineEdit_editingFinished()));
    connect(&mSlider, SIGNAL(valueChanged(int)), this, SLOT(in_mSlider_valueChanged(int)));
    setRange(-50, 50).setDefault(0).setValue(18);
}
VSliderS1M1::~VSliderS1M1()
{

}


// override
bool VSliderS1M1::eventFilter(QObject* obj, QEvent* e)
{
    if(obj == &mSlider && mSlider.isEnabled())
    {
        if(e->type() == QEvent::MouseButtonDblClick)
        {
            mSlider.setValue(mValueDefault);
            return true;
        }
        else if(e->type() == QEvent::Wheel)
        {
            QWheelEvent *wheelEvent=static_cast<QWheelEvent *>(e);
            int numSteps=wheelEvent->angleDelta().y() / 120;
            mSlider.setValue(mSlider.value() + numSteps);
            return true;
        }
        else if(e->type() == QEvent::KeyPress)
        {
            QKeyEvent* keyEvent=static_cast<QKeyEvent*>(e);
            if(keyEvent->key() == Qt::Key_PageUp || keyEvent->key() == Qt::Key_PageDown || keyEvent->key() == Qt::Key_Home || keyEvent->key() == Qt::Key_End)
            {
                return true;
            }
        }
    }
    return QWidget::eventFilter(obj, e);
}
void VSliderS1M1::resizeEvent(QResizeEvent* e)
{
    Q_UNUSED(e);
    float pixelPerRatio = size().height() / 100.0;
    int hText=pixelPerRatio * mHText;
    int hSpace1=pixelPerRatio * mHSpace;
    int hSlider=pixelPerRatio * mHSlider;
    mLineEdit.setGeometry(0, 0, width(), hText);
    mSlider.setGeometry(width() / 2 - width() / 10.0 * 4, hText + hSpace1, width() / 10.0 * 8, hSlider - pixelPerRatio);
    mFont.setPointSize(GLBFHandle.getSuitablePointSize(mFont, "+9.0", mLineEdit.rect()));
    mLineEdit.setFont(mFont);
    QString style;
    style = QString("QLineEdit {"
                    "   color: rgb(161, 161, 161);"
                    // "   background-color: rgb(46, 46, 46);"
                    "   background-color: transparent;"
                    "   border-radius: %1px;"
                    "   selection-color: rgb(0, 121, 107);"
                    "   selection-background-color: rgb(224, 247, 250);"
                    "}").arg(width() / 5);
    mLineEdit.setStyleSheet(style);
    style = QString("QSlider {"
                    "   background-color: transparent;"
                    "}"
                    "QSlider::groove:vertical {"
                    "   background: #333333;"
                    "   border-radius: %1px;"
                    "   width: %2px;"
                    "}"
                    "QSlider::add-page:vertical {"
                    "   background: #CCCCCC;"
                    "   border-radius: %1px;"
                    "   width: %2px;"
                    "}"
                    "QSlider::handle:vertical {"
                    "   border-image: url(:/Icon/SliderHandle.png);"
                    "   height: %3px;"
                    "   margin: -0px  -%4px -1px -%4px;"
                    "}").arg(mSlider.width() * 0.1).arg(mSlider.width() * 0.25).arg(mSlider.width() * 0.7).arg(mSlider.width() * 0.225);
    mSlider.setStyleSheet(style);
}


// slot
void VSliderS1M1::in_mLineEdit_editingFinished()
{
    QString text=mLineEdit.text();
    text.remove("+");
    text.remove("-");
    text.remove("∞");
    if(text.isEmpty())
    {
        if(mLineEdit.text() == "-∞")
        {
            if(mSlider.value() != mSlider.minimum())
            {
                mSlider.setValue(mSlider.minimum());
            }
            else
            {
                in_mSlider_valueChanged(mSlider.minimum());
            }
        }
        else if(mLineEdit.text() == "+∞")
        {
            if(mSlider.value() != mSlider.maximum())
            {
                mSlider.setValue(mSlider.maximum());
            }
            else
            {
                in_mSlider_valueChanged(mSlider.maximum());
            }
        }
        else
        {
            in_mSlider_valueChanged(mSlider.value());
        }
    }
    else
    {
        if(mSlider.value() != mLineEdit.text().toInt())
        {
            mSlider.setValue(mLineEdit.text().toInt());
        }
        else
        {
            in_mSlider_valueChanged(mLineEdit.text().toInt());
        }
    }
    mLineEdit.clearFocus();
}
void VSliderS1M1::in_mSlider_valueChanged(int value)
{
    if(value == 0)
    {
        mLineEdit.setText(QString::number(value));
    }
    else if(value > 0)
    {
        mLineEdit.setText("+" + QString::number(value));
    }
    else
    {
        mLineEdit.setText(QString::number(value));
    }
    if(value == mSlider.minimum() && mShowInfinitesimal)
    {
        mLineEdit.setText("-∞");
    }
    else if(value == mSlider.maximum() && mShowInfinity)
    {
        mLineEdit.setText("+∞");
    }
    if(mValue != value)
    {
        mValue = value;
        if(mEmitOpen)
        {
            emit valueChanged(mValue);
        }
    }
}


// setter & getter
VSliderS1M1& VSliderS1M1::setFont(QFont font)
{
    mFont = font;
    update();
    return *this;
}
VSliderS1M1& VSliderS1M1::setValue(int value)
{
    mEmitOpen = false;
    mSlider.setValue(value);
    mEmitOpen = true;
    return *this;
}
VSliderS1M1& VSliderS1M1::setStep(int value)
{
    mSlider.setSingleStep(value);
    return *this;
}
VSliderS1M1& VSliderS1M1::setDefault(int value)
{
    mValueDefault = value;
    return *this;
}
VSliderS1M1& VSliderS1M1::setRange(int minValue, int maxValue)
{
    QString regexStr("^(^$)|(\\-∞)|(\\+∞)|(\\-)|(\\+)|(");
    for(int i=minValue;i<=maxValue;i++)
    {
        if(i < 0)
        {
            regexStr += "\\";
            regexStr += QString::number(i);
            regexStr += "|";
        }
        else if(i == 0)
        {
            regexStr += "\\-";
            regexStr += QString::number(i);
            regexStr += "|";
            regexStr += QString::number(i);
            regexStr += "|";
            regexStr += "\\+";
            regexStr += QString::number(i);
            regexStr += "|";
        }
        else
        {
            regexStr += QString::number(i);
            regexStr += "|";
            regexStr += "\\+";
            regexStr += QString::number(i);
            regexStr += "|";
        }
    }
    regexStr.chop(1);
    regexStr += ")$";
    QRegularExpression reg(regexStr);
    mLineEdit.setValidator(new QRegularExpressionValidator(reg, this));
    mEmitOpen = false;
    mSlider.setRange(minValue, maxValue);
    mEmitOpen = true;
    return *this;
}
VSliderS1M1& VSliderS1M1::setHeightRatio(int text, int space, int Slider)
{
    mHText = text;
    mHSpace = space;
    mHSlider = Slider;
    return *this;
}
VSliderS1M1& VSliderS1M1::showInfinity(bool state)
{
    mShowInfinity = state;
    return *this;
}
VSliderS1M1& VSliderS1M1::showInfinitesimal(bool state)
{
    mShowInfinitesimal = state;
    return *this;
}

