/************************************************************************

    Description:
        Application Programming Interface (API) exposed by the 
        USB Device Firmware Upgrade dynamic link library

    Author(s):
        <PERSON><PERSON><PERSON>hardt
        
    Thesycon Software Solutions GmbH & Co. KG, Germany, www.thesycon.de

************************************************************************/

#ifndef __tlusbdfu_api_h__
#define __tlusbdfu_api_h__

#include "tlrt_platform.h"
#include "tlrt_types.h"
#include "tlstatus_codes.h"


// Tell doxygen that we want to document this file.
/*! \file */


/*!
    \mainpage

    \section sec_overview Overview

    This documentation describes the application programming interface (API) of the USB Device Firmware Upgrade (DFU) driver.
    The programming interface consists of a set of C-style functions, constants and types.
    The API supports communication with multiple device instances in parallel.

    The USB DFU library programming interface is exported by the DLL/shared library <tt>tlusbdfuapi.dll</tt> or <tt>tlusbdfuapi.so</tt>, respectively.

    \section sec_prog_if Programming Interface

    The programming interface is declared in <tt>tlusbdfu_api.h</tt> and described in the following sections:
        - \ref general_functions
        - \ref dev_enum_acc_functions
        - \ref dev_add_remove_functions
        - \ref dfu_operation_functions
        - \ref firmware_image_functions
        - \ref constants_types
        - \ref api_version_constants

    The programming interface provides the following functions:
        - TLDFU_GetApiVersion()
        - TLDFU_CheckApiVersion()
        - TLDFU_GetUniCharSize()
        - TLDFU_GetErrorText()
        - TLDFU_StartFileLogging()
        - TLDFU_EnumerateUsbDfuDevices()
        - TLDFU_CloseEnumeration()
        - TLDFU_CompareDeviceInstance()
        - TLDFU_RegisterDeviceChangeCallback()
        - TLDFU_UnregisterDeviceChangeCallback()
        - TLDFU_OpenDevice()
        - TLDFU_CloseDevice()
        - TLDFU_CheckDeviceConnection()
        - TLDFU_GetDevicePropertyUint()
        - TLDFU_GetDevicePropertyString()
        - TLDFU_GetTargetImagePropertyUint()
        - TLDFU_GetTargetImagePropertyUint64()
        - TLDFU_GetTargetImagePropertyString()
        - TLDFU_InterfaceVendorInRequest()
        - TLDFU_InterfaceVendorOutRequest()
        - TLDFU_RebootDevice()
        - TLDFU_StartUpgrade()
        - TLDFU_GetUpgradeStatus()
        - TLDFU_FinishUpgrade()
        - TLDFU_StartReadout()
        - TLDFU_GetReadoutStatus()
        - TLDFU_FinishReadout()
        - TLDFU_LoadFirmwareImageFromFile()
        - TLDFU_LoadFirmwareFromBuffer()
        - TLDFU_StoreFirmwareInBuffer()
        - TLDFU_UnloadFirmwareImage()
        - TLDFU_GetImagePropertyUint()
        - TLDFU_GetImagePropertyUint64()
        - TLDFU_GetImagePropertyString()


    \section sec_integration_cpp API Integration for C++ Applications

    An application implemented in C++ should integrate the API as follows:
    
    Extract the folder <tt>source_common/tlusbdfuapi</tt> from the SDK and integrate the source code
    contained therein into your application. 
    Note that the source code contained in the <tt>tlusbdfuapi</tt> folder does not have dependencies 
    on any other sources shipped with the SDK.

    Include <tt>tlusbdfusdk.h</tt> in your application code. 
    This makes the API definition (contained in <tt>tlusbdfu_api.h</tt>) and some wrapper classes available.
    Also make sure that the <tt>.cpp</tt> files contained in the <tt>tlusbdfuapi</tt> folder will be compiled 
    with your application code.
        
    Use the \c TLDfuApi class to dynamically load the API library at runtime. 
    After the API was successfully loaded, use the wrapper classes \c TLDfuEnumerator, \c TLDfuDevice and \c TLDfuImage
    to call the \c TLDFU_* functions defined in <tt>tlusbdfu_api.h</tt>.

    For an example, refer to the sources of the sample application (console mode) that is shipped with the SDK. 
    It can be found in the folders <tt>source_common/tlusbdfucons_impl</tt> and <tt>source_win/tlusbdfucons</tt>



    \section sec_compatibility API Compatibility Check

    An application should use TLDFU_CheckApiVersion() to verify, at runtime,
    that the loaded library is compatible with the <tt>tlusbdfu_api.h</tt> file the application has been compiled with.
    See TLDFU_CheckApiVersion() and \ref api_version_constants for details.

    Note that compatibility is checked automatically by the dynamic library loader implemented 
    in the \c TLDfuApi class.


    \section sec_enum_dev Enumerating Devices Attached to the System

    Typically, at startup an application needs to determine how many devices are
    currently connected to the system and build a device list.
    This is called device enumeration.
    See also \ref dev_filter_desc and TLDFU_EnumerateUsbDfuDevices().

    The following pseudo code shows how an application should implement initial device enumeration.
    This is not a complete C++ source code example.
    Error handling and other details are not shown.

    \b NOTE: Working with multiple device instances in parallel requires more complex application logic than shown below.
    This is because after a mode switch (between APP and DFU mode or vice-versa) unambiguous device instance 
    identification is not always possibly.
    Hence Thesycon recommends that a DFU application supports only one device instance.

    \code

    const char[] deviceFilterDescription = "VID=0x0152A, PID=0x0201\n"
                                           "VID=0x0152A, PID=0x0202\n";

    // this is our list that holds active devices (open handles)
    MyDeviceContainer deviceContainer;

    // enumerate attached devices
    // As an example we set retryCount to 25 and retryDelay to 200.
    // If no devices are found, the function retries to enumerate available devices
    // up to 25 times (which corresponds to a total timeout interval of 5 seconds). 
    // The function returns either if the number of available devices is 
    // greater than zero or the specified number of retries are exhausted. Between two retries the
    // function waits 200 milliseconds. Both parameters can also be set to 0 to enumerate the
    // available device exactly once.
    TLDfuEnumerationHandle enumHandle = TLDFU_INVALID_HANDLE;
    unsigned int deviceCount = 0;
    TLDFU_EnumerateUsbDfuDevices(
                deviceFilterDescription, // const char* deviceFilterDescription,
                &enumHandle,             // TLDfuEnumerationHandle* enumerationHandle,
                &deviceCount,            // unsigned int* deviceCount,
                25,                      // unsigned int retryCount,
                200,                     // unsigned int retryDelay,
                0                        // unsigned int flags
                );

    // fill device container with the instances currently available in the system
    for ( unsigned int i=0; i<deviceCount; i++ ) {
        TLDfuDeviceHandle h;
        st = TLDFU_OpenDevice(enumHandle, i, &h, 0);
        if ( TLSTATUS_SUCCESS == st ) {
            // device successfully opened, add a new object to our container
            MyDeviceObj* devobj = new MyDeviceObj(h);
            deviceContainer.Add(devobj);
        }
    }

    // cleanup
    TLDFU_CloseEnumeration(enumHandle);

    \endcode


    \section dyn_add_remove Dynamic Add/Remove Device Handling

    This section describes how dynamic add/remove device handling is typically to be implemented in an application.
    Note that handling of dynamic device connect/disconnect events is not necessary for all types of applications.
    For example, a command line based utility will typically implement initial device enumeration only
    and will ignore subsequent device add or remove events.
    Whether the logic described in this section is required or not, is a decision to be made by the application designer.

    \b NOTE: Working with multiple device instances in parallel requires more complex application logic than shown in this section.
    This is because after a mode switch (between APP and DFU mode or vice-versa) unambiguous device instance 
    identification is not always possibly.
    Hence Thesycon recommends that a DFU application supports only one device instance.

    When a device is removed from or attached to the system while the application is running, the API calls
    the application's device change callback function.
    In the device change callback (or in another thread which gets triggered by the callback) 
    the application needs to update its device container to reflect the current state of the system.
    Note that more than one device instance can be removed or attached at once, e.g. in case a USB hub is disconnected or connected.

    The following pseudo code shows how an application should implement re-enumeration in its device change callback.
    This is not a complete C++ source code example. 
    Error handling and other details are not shown.

    \code

    const char[] deviceFilterDescription = "VID=0x0152A, PID=0x0201\n"
                                           "VID=0x0152A, PID=0x0202\n";

    // this is our list that holds active devices (open handles)
    MyDeviceContainer deviceContainer;

    // device change callback routine
    void MyDeviceChangeCallback(void* callbackContext, TLDfuDeviceEvent eventType)
    {
        if ( TLDfuEvent_DeviceRemoved == eventType ) {
            // one or more devices have been removed from the system
            // remove from our device container all instances that are not present any longer
            foreach ( MyDeviceObj* devobj in deviceContainer ) {
                if ( TLSTATUS_SUCCESS != TLDFU_CheckDeviceConnection(devobj->Handle) ) {
                    // device has been disconnected, remove it from our container and close the handle
                    deviceContainer.Remove(devobj);
                    TLDFU_CloseDevice(devobj->Handle);
                    delete devobj;
                }
            }
        }

        if ( TLDfuEvent_DeviceAttached == eventType ) {
            // one or more new devices have been added to the system
            // enumerate all attached devices
            TLDfuEnumerationHandle enumHandle = TLDFU_INVALID_HANDLE;
            unsigned int deviceCount = 0;
            TLDFU_EnumerateUsbDfuDevices(deviceFilterDescription, &enumHandle, &deviceCount, 0, 0, 0);

            // we need to open all new instances and add the resulting handle to our device container
            for ( unsigned int i=0; i<deviceCount; i++ ) {
                // assume it is a new device and then check if we have a handle for this instance already
                bool isNewDevice = true;
                foreach ( MyDeviceObj* devobj in deviceContainer ) {
                    if ( TLSTATUS_SUCCESS == TLDFU_CompareDeviceInstance(enumHandle, i, devobj->Handle) ) {
                        // devobj == instance i, we opened this instance already
                        isNewDevice = false;
                        break;
                    }
                }
                if ( isNewDevice ) {
                    // open this instance
                    TLDfuDeviceHandle h = TLDFU_INVALID_HANDLE;
                    st = TLDFU_OpenDevice(enumHandle, i, &h);
                    if ( TLSTATUS_SUCCESS == st ) {
                        // add a new object to our container
                        MyDeviceObj* devobj = new MyDeviceObj(h);
                        deviceContainer.Add(devobj);
                    }
                }
            }

            // cleanup
            TLDFU_CloseEnumeration(enumHandle);
        }

    }

    \endcode



    \section dev_filter_desc USB Device Filter Description

    When the available USB devices are enumerated with a call to TLDFU_EnumerateUsbDfuDevices(),
    the application passes an ASCII string that describes the set of devices that should be enumerated.
    This string is called a device filter description.

    The string is formatted as a multi-line string. Each line contains multiple key-value pairs
    to describe one device type. Multiple keys in a line are separated by commas.
    The keys are case-insensitive.

    The following keys are supported:
        - VID - USB Vendor ID

            The USB vendor Id reported by the device in the idVendor field of the USB device descriptor.

        - PID - USB Product ID
            
            The USB product Id reported by the device in the idProduct field of the USB device descriptor.

        - MIN_BCDDEVICE - minimum BCD device number
            
            The smallest accepted BCD device number reported by the device through the bcdDevice field of the USB device descriptor.

        - MAX_BCDDEVICE - maximum BCD device number
            
            The largest accepted BCD device number reported by the device through the bcdDevice field of the USB device descriptor.
    
    A combination of a VID, a PID and an optional min./max. BCD device number is called a device Id. The device 
    filter description can contain multiple device Ids to describe all supported devices (as a white list).
    A device Id is specified in a line with the keys "VID", "PID", and optional "MIN_BCDDEVICE" and "MAX_BCDDEVICE".
    The keys VID and PID must be present in each line, otherwise the line does not specify a valid device Id. 
    Other keys are optional. 
    
    For example, the line: <tt>"VID=0x0152A, PID=0x0201\n"</tt> matches all devices with the VID 0x0152A and
    the PID 0x0201 despite the reported BCD device number.

    The following is an example for a multi-line device filter description for two distinct USB device types.

    <tt>
    "VID=0x0152A, PID=0x0201\n" <br>
    "VID=0x0152A, PID=0x0202\n"
    </tt>



    \section sec_exclusive_access Exclusive Device Access

    Through this API, communication with a given device instance can be controlled by one application at a time only.
    In the TLDFU_OpenDevice() call the API blocks concurrent access to a device instance by multiple
    applications, or multiple instances of the same application.
    An application can open only one handle for a given device instance.
    In other words, any given USB device instance is always opened exclusively.

    Note that if there are multiple USB device instances attached to the system then these instances can be
    opened and used concurrently through the API.


    \section sec_unichar UNICODE Characters

    API functions that expect string arguments or return strings use the character type \c T_UNICHAR.
    This type varies depending on the platform this API is running on.

    On Windows a \c T_UNICHAR is 2 bytes in size and corresponds to \c WCHAR or \c wchar_t.
    A UNICODE character is represented by a 16-bit wide character.

    On Linux and macOS a \c T_UNICHAR is 1 byte in size and corresponds to \c char.
    A UNICODE character is a UTF-8 encoded multi-byte sequence.

    If this API is used from scripting languages, care must be taken to use the correct
    character size when calling API functions. Where required, strings must be converted to meet
    the native character representation of the scripting language.

    See also TLDFU_GetUniCharSize().

    
    \section sec_threading Multi-threading

    This API and its implementation is thread-safe.
    API functions can be called by multiple threads in parallel.

*/



/*!
    \defgroup api_version_constants Interface version constants
    @{

    \brief API version number

    If changes are made to the interface that are compatible with previous versions
    of the interface then the minor version number will be incremented.
    If changes are made to the interface that cause an incompatibility with previous versions
    of the interface then the major version number will be incremented.

    An application should use TLDFU_CheckApiVersion() to check if it can work with the present version of this library.
    It can use TLDFU_GetApiVersion() to retrieve the API version implemented by the library.
*/

//! Major version number of the programming interface.
#define TLDFU_API_VERSION_MJ   1
//! Minor version number of the programming interface.
#define TLDFU_API_VERSION_MN   17

//! Version number of the programming interface as 32 bit unsigned integer (upper 16 bits = major version, lower 16 bits = minor version). This value is returned by TLDFU_GetApiVersion().
#define TLDFU_API_VERSION  ( ((unsigned int)TLDFU_API_VERSION_MN)|(((unsigned int)TLDFU_API_VERSION_MJ)<<16) )

//! Extract the major version number from the integer value returned by TLDFU_GetApiVersion().
#define TLDFU_API_EXTRACT_MJ_VERSION(ver)   ( ((ver)>>16) & 0xFFFFu )

//! Extract the minor version number from the integer value returned by TLDFU_GetApiVersion().
#define TLDFU_API_EXTRACT_MN_VERSION(ver)   ( (ver) & 0xFFFFu )

/*!
    @}
*/



/*!
    \defgroup constants_types Constants and types
    @{

    \brief Constants and types used in the programming interface.
*/

//! Represents a notification handle. The value #TLDFU_INVALID_HANDLE (zero) is an invalid handle.
typedef unsigned int TLDfuNotificationHandle;

//! Represents an enumeration handle. The value #TLDFU_INVALID_HANDLE (zero) is an invalid handle.
typedef unsigned int TLDfuEnumerationHandle;

//! Represents a device handle. The value #TLDFU_INVALID_HANDLE (zero) is an invalid handle.
typedef unsigned int TLDfuDeviceHandle;

//! Represents an image handle. The value #TLDFU_INVALID_HANDLE (zero) is an invalid handle.
typedef unsigned int TLDfuImageHandle;

//! The value zero is an invalid handle.
#define TLDFU_INVALID_HANDLE        0


/*!
    \brief Device event types, see TLDFU_DeviceChangeCallback().
*/
typedef enum tagTLDfuDeviceEvent
{
    //! Reserved for internal use.
    TLDfuEvent_reserved = 0,

    //! One or more USB devices have been attached to the system.
    TLDfuEvent_DeviceAttached,

    //! One or more USB devices have been removed from the system.
    TLDfuEvent_DeviceRemoved

} TLDfuDeviceEvent;

TL_COMPILE_TIME_ASSERT(sizeof(TLDfuDeviceEvent)==sizeof(int));


/*!
    \brief Device property identifiers.
*/
typedef enum tag_TLDfuDeviceProperty
{
    //! Reserved for internal use.
    TLDfuDeviceProperty_reserved = 0,

    //! USB Vendor ID.
    //! See also TLDFU_GetDevicePropertyUint().
    TLDfuDeviceProperty_VendorId,

    //! USB Product ID.
    //! See also TLDFU_GetDevicePropertyUint().
    TLDfuDeviceProperty_ProductId,

    //! bcdDevice value reported in the USB device descriptor.
    //! See also TLDFU_GetDevicePropertyUint().
    TLDfuDeviceProperty_BcdDevice,


    //! Manufacturer string reported by the device (optional string descriptor).
    //! See also TLDFU_GetDevicePropertyString().
    TLDfuDeviceProperty_UsbManufacturerString,

    //! Product string reported by the device (optional string descriptor).
    //! See also TLDFU_GetDevicePropertyString().
    TLDfuDeviceProperty_UsbProductString,

    //! USB serial number string reported by the device (optional string descriptor).
    //! See also TLDFU_GetDevicePropertyString().
    TLDfuDeviceProperty_UsbSerialNumberString,

    //! The current run mode of the device.
    //! The returned values is one of the \ref run_modes.
    //! See also TLDFU_GetDevicePropertyUint().
    TLDfuDeviceProperty_CurrentRunMode,

    //! String which identifies the given DFU device or subdevice instance.
    //! See also TLDFU_GetDevicePropertyString().
    TLDfuDeviceProperty_DeviceInstanceIdString,

    //! String which identifies the physical device the given DFU subdevice belongs to.
    //! See also TLDFU_GetDevicePropertyString().
    TLDfuDeviceProperty_PhysicalDeviceIdString,

} TLDfuDeviceProperty;

TL_COMPILE_TIME_ASSERT(sizeof(TLDfuDeviceProperty)==sizeof(int));


/*!
    \brief Image property identifiers.
*/
typedef enum tag_TLDfuImageProperty
{
    //! Reserved for internal use.
    TLDfuImageProperty_reserved = 0,

    //! The size, in bytes, of the image. If a DFU file suffix is present, this suffix is not included in the size.
    //! See also TLDFU_GetImagePropertyUint64() and TLDFU_GetTargetImagePropertyUint64().
    TLDfuImageProperty_ImageSize,

    //! Target type of this image, 0 for unspecified, 1 for Bootloader, 2 for Application, 3 for Device Config.
    //! This property is supported for image type \ref TLDfuImageType_TLBinary only.
    //! See also TLDFU_GetImagePropertyUint() and TLDFU_GetTargetImagePropertyUint().
    TLDfuImageProperty_ImageTargetType,

    //! Target ID. Specifies the target chip and memory area for this image.
    //! This property is supported for image type \ref TLDfuImageType_TLBinary only.
    //! See also TLDFU_GetImagePropertyUint() and TLDFU_GetTargetImagePropertyUint().
    TLDfuImageProperty_ImageTargetId,

    //! Major version number of this image.
    //! This property is supported for image type \ref TLDfuImageType_TLBinary only.
    //! See also TLDFU_GetImagePropertyUint() and TLDFU_GetTargetImagePropertyUint().
    TLDfuImageProperty_ImageVersionMajor,
    //! Minor version number of this image.
    //! This property is supported for image type \ref TLDfuImageType_TLBinary only.
    //! See also TLDFU_GetImagePropertyUint() and TLDFU_GetTargetImagePropertyUint().
    TLDfuImageProperty_ImageVersionMinor,
    //! Sub version number of this image.
    //! This property is supported for image type \ref TLDfuImageType_TLBinary only.
    //! See also TLDFU_GetImagePropertyUint() and TLDFU_GetTargetImagePropertyUint().
    TLDfuImageProperty_ImageVersionSub,
    //! SubSub version number of this image.
    //! This property is supported for image type \ref TLDfuImageType_TLBinary only.
    //! See also TLDFU_GetImagePropertyUint() and TLDFU_GetTargetImagePropertyUint().
    TLDfuImageProperty_ImageVersionSubSub,

    //! Compatibility ID of this image.
    //! This property is supported for image type \ref TLDfuImageType_TLBinary only.
    //! See also TLDFU_GetImagePropertyUint() and TLDFU_GetTargetImagePropertyUint().
    TLDfuImageProperty_ImageCompatibilityId,
    //! Compatibility Mask of this image.
    //! This property is supported for image type \ref TLDfuImageType_TLBinary only.
    //! See also TLDFU_GetImagePropertyUint() and TLDFU_GetTargetImagePropertyUint().
    TLDfuImageProperty_ImageCompatibilityMask,

    //! Build date/time string. This data item is optional and might not be present in the given image.
    //! This property is supported for image type \ref TLDfuImageType_TLBinary only.
    //! See also TLDFU_GetImagePropertyString() and TLDFU_GetTargetImagePropertyString().
    TLDfuImageProperty_ImageBuildDateTime,

    //! Description string. This data item is optional and might not be present in the given image.
    //! This property is supported for image type \ref TLDfuImageType_TLBinary only.
    //! See also TLDFU_GetImagePropertyString() and TLDFU_GetTargetImagePropertyString().
    TLDfuImageProperty_ImageDescription,


    //! Value that indicates whether a DFU file suffix is present in the image.
    //! The value is 1 if a DFU file suffix is present, or 0 if no suffix is present.
    //! See also TLDFU_GetImagePropertyUint().
    TLDfuImageProperty_HasDfuFileSuffix,
    //! Vendor ID specified in the DFU file suffix (if available).
    //! This property is only supported if a DFU file suffix is present in the image.
    //! See also TLDFU_GetImagePropertyUint().
    TLDfuImageProperty_VendorId,
    //! Product ID specified in the DFU file suffix (if available).
    //! This property is only supported if a DFU file suffix is present in the image.
    //! See also TLDFU_GetImagePropertyUint().
    TLDfuImageProperty_ProductId,
    //! bcdDevice number specified in the DFU file suffix (if available).
    //! This property is only supported if a DFU file suffix is present in the image.
    //! See also TLDFU_GetImagePropertyUint().
    TLDfuImageProperty_BcdDevice,
    //! bcdDFU number specified in the DFU file suffix (if available).
    //! This property is only supported if a DFU file suffix is present in the image.
    //! See also TLDFU_GetImagePropertyUint().
    TLDfuImageProperty_BcdDFU,


    //! The CRC32 checksum of the image.
    //! This property is supported for image type \ref TLDfuImageType_TLBinary only.
    //! See also TLDFU_GetImagePropertyUint() and TLDFU_GetTargetImagePropertyUint().
    TLDfuImageProperty_Crc32Sum,

    //! The base address of the FLASH memory.
    //! This property is supported for image type \ref TLDfuImageType_TLBinary only.
    //! See also TLDFU_GetImagePropertyUint64() and TLDFU_GetTargetImagePropertyUint64().
    TLDfuImageProperty_FlashBaseAddress,

    //! The address where this image resides in FLASH memory.
    //! This property is supported for image type \ref TLDfuImageType_TLBinary only.
    //! See also TLDFU_GetImagePropertyUint64() and TLDFU_GetTargetImagePropertyUint64().
    TLDfuImageProperty_ImageBaseAddress,

    //! The maximum image size, in bytes, the given storage area supports (typically identical to the storage area size).
    //! See also TLDFU_GetTargetImagePropertyUint64().
    TLDfuImageProperty_MaxImageSize

} TLDfuImageProperty;



/*!
    \brief Image types.
*/
typedef enum tag_TLDfuImageType
{
    //! Reserved for internal use.
    TLDfuImageType_reserved = 0,

    //! A raw binary image which will not be interpreted by the library in any way.
    TLDfuImageType_RawBinary,

    //! A binary image that contains embedded metadata such as version information and description strings.
    //! Optionally, image integrity is protected by a CRC32 checksum.
    //! By convention, such an image file uses a .tlimg extension, however this is not enforced by the DFU library.
    TLDfuImageType_TLBinary,

    // for backward compatibility:
    TLDfuImageType_ToriLogicBinary = TLDfuImageType_TLBinary

} TLDfuImageType;



/*!
    \brief Firmware upgrade states.
*/
typedef enum tag_TLDfuUpgradeState
{
    //! No upgrade operation is in progress.
    TLDfuUpgradeState_Idle = 0,

    //! The upgrade operation is about to start.
    TLDfuUpgradeState_Initializing,

    //! The upgrade operation is in progress.
    TLDfuUpgradeState_InProgress,

    //! The upgrade operation has completed successfully.
    TLDfuUpgradeState_Finished,
    
    //! The upgrade operation has completed with an error.
    TLDfuUpgradeState_Failed

} TLDfuUpgradeState;


/*!
    \brief Firmware readout states.
*/
typedef enum tag_TLDfuReadoutState
{
    //! No readout operation is in progress.
    TLDfuReadoutState_Idle = 0,

    //! The readout operation is about to start.
    TLDfuReadoutState_Initializing,

    //! The readout operation is in progress.
    TLDfuReadoutState_InProgress,

    //! The readout operation has completed successfully.
    TLDfuReadoutState_Finished,
    
    //! The readout operation has completed with an error.
    TLDfuReadoutState_Failed

} TLDfuReadoutState;



/*!
    \defgroup run_modes Device run modes
    @{

    \brief The mode a given device operates in.
*/

//! The run mode is unknown.
#define TLDFU_RUNMODE_UNKNOWN           0

//! The device runs in application mode (aka APP mode).
#define TLDFU_RUNMODE_APPLICATION       1

//! The device runs in bootloader mode (aka DFU mode).
#define TLDFU_RUNMODE_BOOTLOADER        2

/*!
@}
*/

/*!
    \defgroup dev_flags Device flags.
    @{

    \brief Flags that can be used with device-specific functions.
*/

//! The XMOS specific DFU extensions should be used when communicating with the device.
#define TLDFU_DEVICE_FLAG_USE_XMOS_DFU_EXTENSIONS       0x00000001u

//! Unlock the target through a proprietary unlock-mechanism before downloading an image to the target.
#define TLDFU_DEVICE_FLAG_UNLOCK_TARGET                 0x00000002u

/*!
@}
*/


/*!
    \defgroup log_file_flags File logging flags.
    @{

    \brief Flags that can be used with StartFileLogging function.
*/

//! Append logs to existing file.
#define TLDFU_LOG_FILE_APPEND_TO_EXISTING_FILE          0x00000001u

//! Flush log file after each write.
#define TLDFU_LOG_FILE_FLUSH_AFTER_EACH_WRITE           0x00000002u

/*!
@}
*/



/*!
@}
*/


#if TLRT_OS_ENV_WINDOWS

// function decoration
#if defined(TLDFU_API_DLL_EXPORTS)
// if .h file is included by the DLL implementation:
// A .def file is used to specify the exports.
#define TLDFU_API_DECL
#else
// if .h file is included (imported) by client code
#define TLDFU_API_DECL
#endif

// calling convention
#define TLDFU_API_CALL  __stdcall

#elif TLRT_OS_ENV_LINUX

#if defined(TLDFU_API_DLL_EXPORTS)
// if .h file is included by the library implementation:
#define TLDFU_API_DECL     __attribute__((__visibility__("default")))
#else
// if .h file is included (imported) by client code
#define TLDFU_API_DECL
#endif

// calling convention
#define TLDFU_API_CALL

#elif TLRT_OS_ENV_MACOS

#if defined(TLDFU_API_DLL_EXPORTS)
// if .h file is included by the library implementation:
#define TLDFU_API_DECL     __attribute__((__visibility__("default")))
#else
// if .h file is included (imported) by client code
#define TLDFU_API_DECL
#endif

// calling convention
#define TLDFU_API_CALL

#else
#error "OS not supported"
#endif



//
// Functions exported by the library
//

#if defined (__cplusplus)
extern "C" {
#endif


/*!
    \defgroup general_functions General functions
    @{

    \brief General API functions
*/


/*!
    \brief Returns the API version implemented by the library.

    \return
        This function returns the version number of the programming interface (API) implemented by the library.
        The number returned is the #TLDFU_API_VERSION constant the library has been compiled with.
*/
TLDFU_API_DECL
unsigned int
TLDFU_API_CALL
TLDFU_GetApiVersion(void);
// corresponding function type (useful for explicit loading, e.g. with GetProcAddress)
typedef
unsigned int
(TLDFU_API_CALL *F_TLDFU_GetApiVersion)(void);


/*!
    \brief Checks if the given API version number is compatible with this library.

    \param[in] majorVersion
        The major version number the application is expecting.
        An application should set this parameter to the #TLDFU_API_VERSION_MJ constant.

    \param[in] minorVersion
        The minor version number the application is expecting.
        An application should set this parameter to the #TLDFU_API_VERSION_MN constant.

    \return
        The function returns 1 if the specified version constants are compatible with this library.
        The function returns 0 if the specified version constants are not compatible with this library.

    An application should call this function before it uses any other function exported by the library
    in order to verify its compatibility with the library at runtime.
    If this function fails then the function interface exported by the library is not compatible
    with the .h file the application has been compiled with and the application should
    reject working with this library version.

    The library interface is considered compatible if its major version number is equal to \p majorVersion
    and its minor version number is greater than or equal to \p minorVersion.
*/
TLDFU_API_DECL
int
TLDFU_API_CALL
TLDFU_CheckApiVersion(
    unsigned int majorVersion,
    unsigned int minorVersion
    );
// corresponding function type (useful for explicit loading, e.g. with GetProcAddress)
typedef
int
(TLDFU_API_CALL *F_TLDFU_CheckApiVersion)(
    unsigned int majorVersion,
    unsigned int minorVersion
    );


/*!
    \brief Returns the size, in bytes, of the \c T_UNICHAR type.
  
    \return
        The function returns the size, in bytes, of the \c T_UNICHAR type.
	
	The \c T_UNICHAR type is used by API functions that expect string arguments or return strings.
	The size of the \c T_UNICHAR type varies depending on the platform.

	On Windows a \c T_UNICHAR character is 2 bytes in size, i.e. it corresponds to \c WCHAR or \c wchar_t.
	UNICODE characters are represented by 16-bit characters.

	On GNU/Linux a \c T_UNICHAR character is 1 byte in size, i.e. it corresponds to \c char.
	UNICODE characters are encoded as UTF-8.

	See also \ref sec_unichar.

	Use this function to implement API wrapper functions in platform-independent code such as Python scripts.
*/
TLDFU_API_DECL
unsigned int
TLDFU_API_CALL
TLDFU_GetUniCharSize(void);
// corresponding function type (useful for explicit loading, e.g. with GetProcAddress)
typedef
unsigned int
(TLDFU_API_CALL *F_TLDFU_GetUniCharSize)(void);


/*!
    \brief Returns a human-readable text message that describes the given error code.

    \param[in] statusCode
        The status code.

    \return
        The function returns a pointer to a null-terminated ASCII string that represents a description of the given status code.
*/
TLDFU_API_DECL
const char*
TLDFU_API_CALL
TLDFU_GetErrorText(
    TLSTATUS statusCode
    );
// corresponding function type (useful for explicit loading, e.g. with GetProcAddress)
typedef
const char*
(TLDFU_API_CALL *F_TLDFU_GetErrorText)(
    TLSTATUS statusCode
    );



/*!
    \brief Enables logging in file for the API.

    \param[in] logFileName
        The log file name.

    \param[in] flags
        This parameter is set to zero or a bitwise OR combination of the following constants:
            - #TLDFU_LOG_FILE_APPEND_TO_EXISTING_FILE
            - #TLDFU_LOG_FILE_FLUSH_AFTER_EACH_WRITE

    \return
        The function returns \c TLSTATUS_SUCCESS if successful, an error code otherwise.
*/
TLDFU_API_DECL
TLSTATUS
TLDFU_API_CALL
TLDFU_StartFileLogging(
    const T_UNICHAR* logFileName,
    unsigned int flags
    );
// corresponding function type (useful for explicit loading, e.g. with GetProcAddress)
typedef
TLSTATUS
(TLDFU_API_CALL *F_TLDFU_StartFileLogging)(
    const T_UNICHAR* logFileName,
    unsigned int flags
    );

/*!
  @}
*/



/*!
  \defgroup dev_enum_acc_functions Device enumeration and access
  @{

  \brief API functions for device enumeration and device access
*/


/*!
    \brief Enumerates devices connected to the system.

    \param[in] deviceFilterDescription
        A null-terminated string that describes the set of devices that should be enumerated.
        See \ref dev_filter_desc for details about the device filter description.

    \param[out] enumerationHandle
        Address of a caller-provided variable which will be set to a handle that represents the internal device list
        created by this call.
        Note that the value #TLDFU_INVALID_HANDLE (zero) is an invalid handle.
        If the function succeeds, it will return a valid non-zero handle.
        When the handle is no longer needed it must be closed with TLDFU_CloseEnumeration() to free internal resources.

    \param[out] deviceCount
        Address of a caller-provided variable which will be set to the number of devices found during enumeration.
        This corresponds to the number of items available in the internal device list.
        The list is addressed by means of a zero-based index which ranges from zero to \p deviceCount - 1.
        If no devices are present, the function sets \p deviceCount to zero and succeeds.

    \param[in] retryCount
        The number of retries the function tries to enumerate the available devices.
        The function returns either if the number of available devices is greater zero or the
        specified number of retries are exhausted. If the number of retries are exhausted and 
        no devices are present, the function sets \p deviceCount to zero and succeeds.
        The function waits \p retryDelay milliseconds between two retries.
        If \p retryCount is set to 0 the function enumerates the available devices exactly once.

    \param[in] retryDelay
        The time, in milliseconds, the function waits between two retries.
        If \p retryCount is set to 0 this parameter is ignored.

    \param[in] flags
        This parameter is reserved for future use and must be set to zero.

    \return
        The function returns \c TLSTATUS_SUCCESS if successful, an error code otherwise.
        Some specific error codes are described below.

    \retval TLSTATUS_INVALID_LICENSE_DATA
        The license data record is invalid.

    \retval TLSTATUS_LICENSE_CHECK_FAILED
        The license is not valid for the DFU library.

    A call to this function creates an internal list which contains all device instances currently attached
    to the system that match the characteristics specified with the \p deviceFilterDescription.
    The list represents a snapshot of the system's state at the point in time when TLDFU_EnumerateUsbDfuDevices() is called.
    Based on the returned device count the application iterates through the list and uses TLDFU_OpenDevice()
    to open the device and to establish communication.

    See also \ref sec_enum_dev.

    The internal device list is represented by \p enumerationHandle.
    To free internal resources, the application must close this handle with TLDFU_CloseEnumeration() when done with device
    enumeration. If no device is present in the system, the function sets \p deviceCount to zero, sets \p enumerationHandle
    to a valid non-zero handle, and returns \c TLSTATUS_SUCCESS.
    TLDFU_CloseEnumeration() still needs to be called in this case.
    In other words, whenever the function returns \c TLSTATUS_SUCCESS then \p enumerationHandle needs to be closed to free
    resources.
*/
TLDFU_API_DECL
TLSTATUS
TLDFU_API_CALL
TLDFU_EnumerateUsbDfuDevices(
    const char* deviceFilterDescription,
    TLDfuEnumerationHandle* enumerationHandle,
    unsigned int* deviceCount,
    unsigned int retryCount,
    unsigned int retryDelay,
    unsigned int flags
    );
// corresponding function type (useful for explicit loading, e.g. with GetProcAddress)
typedef
TLSTATUS
(TLDFU_API_CALL *F_TLDFU_EnumerateUsbDfuDevices)(
    const char* deviceFilterDescription,
    TLDfuEnumerationHandle* enumerationHandle,
    unsigned int* deviceCount,
    unsigned int retryCount,
    unsigned int retryDelay,
    unsigned int flags
    );


/*!
    \brief Closes a device enumeration previously created with TLDFU_EnumerateUsbDfuDevices().

    \param[in] enumerationHandle
        Represents the internal device list that is to be destroyed.
        The specified handle becomes invalid after this call and must not be used in any subsequent API calls.

    \return
        The function returns \c TLSTATUS_SUCCESS if successful, an error code otherwise.
        If a valid handle is specified, the function always returns \c TLSTATUS_SUCCESS.

    Note that device handles do not have a dependency on enumeration handles.
    Closing an enumeration handle does not have a side effect on any device handles
    that have been created based on that enumeration handle.
*/
TLDFU_API_DECL
TLSTATUS
TLDFU_API_CALL
TLDFU_CloseEnumeration(
    TLDfuEnumerationHandle enumerationHandle
    );
// corresponding function type (useful for explicit loading, e.g. with GetProcAddress)
typedef
TLSTATUS
(TLDFU_API_CALL *F_TLDFU_CloseEnumeration)(
    TLDfuEnumerationHandle enumerationHandle
    );


/*!
    \brief Compares an enumerated device instance with an instance represented by a device handle.

    \param[in] enumerationHandle
        Represents an internal device list previously created with TLDFU_EnumerateUsbDfuDevices().

    \param[in] deviceIndex
        Specifies an item in the internal device list as a zero-based index.
        The index ranges from zero to \p deviceCount - 1.
        The \p deviceCount is returned by TLDFU_EnumerateUsbDfuDevices().

    \param[in] deviceHandle
        Identifies a device instance the caller has already opened.

    \retval TLSTATUS_SUCCESS
        The device instance addressed by \p deviceIndex is identical with the instance that is
        represented by \p deviceHandle.

    \retval TLSTATUS_NO_MATCH
        The device instance addressed by \p deviceIndex is not identical with the instance that is
        represented by \p deviceHandle.

    \retval TLSTATUS_INVALID_HANDLE
        An invalid handle was specified.

    \retval TLSTATUS_INVALID_INDEX
        An invalid index was specified.

    An application iterates through all devices found by TLDFU_EnumerateUsbDfuDevices() and uses this function
    to decide whether or not it has already opened a given device instance.

    See also \ref sec_enum_dev.
*/
TLDFU_API_DECL
TLSTATUS
TLDFU_API_CALL
TLDFU_CompareDeviceInstance(
    TLDfuEnumerationHandle enumerationHandle,
    unsigned int deviceIndex,
    TLDfuDeviceHandle deviceHandle
    );
// corresponding function type (useful for explicit loading, e.g. with GetProcAddress)
typedef
TLSTATUS
(TLDFU_API_CALL *F_TLDFU_CompareDeviceInstance)(
    TLDfuEnumerationHandle enumerationHandle,
    unsigned int deviceIndex,
    TLDfuDeviceHandle deviceHandle
    );


/*!
    \brief Opens a device instance.

    \param[in] enumerationHandle
        Represents an internal device list previously created with TLDFU_EnumerateUsbDfuDevices().

    \param[in] deviceIndex
        Specifies an item in the internal device list as a zero-based index.
        The index ranges from zero to \p deviceCount - 1.
        The \p deviceCount is returned by TLDFU_EnumerateUsbDfuDevices().

    \param[out] deviceHandle
        Address of a caller-provided variable which will be set to a handle value if the function succeeds.
        The handle represents the connection to the device instance and is required for all operations targeted to this device.
        Note that the value #TLDFU_INVALID_HANDLE (zero) is an invalid handle.
        If the function succeeds, it will return a valid non-zero handle.

    \param[in] flags
        This parameter is reserved for future use and must be set to zero.

    \return
        The function returns \c TLSTATUS_SUCCESS if successful, an error code otherwise.
        Some specific error codes are described below.

    \retval TSTATUS_INVALID_INDEX
        An invalid \p deviceIndex was specified.

    \retval TLSTATUS_IN_USE
        The given device instance is already opened by this application, or by another application.
        See also \ref sec_exclusive_access.

    \retval TLSTATUS_INVALID_LICENSE_DATA
        The license data record is invalid.

    \retval TLSTATUS_LICENSE_CHECK_FAILED
        The license is not valid for the DFU library or the specified device instance.

    The function opens the specified device instance and returns a handle for it.
    If the specified device instance is already opened, the function fails, see also \ref sec_exclusive_access.

    The device handle returned by this function does not have a dependency on the specified enumeration handle.
    The provided \p enumerationHandle needs to be valid during this function call only and can be closed after 
    the function returned, see TLDFU_CloseEnumeration().
*/
TLDFU_API_DECL
TLSTATUS
TLDFU_API_CALL
TLDFU_OpenDevice(
    TLDfuEnumerationHandle enumerationHandle,
    unsigned int deviceIndex,
    TLDfuDeviceHandle* deviceHandle,
    unsigned int flags
    );
// corresponding function type (useful for explicit loading, e.g. with GetProcAddress)
typedef
TLSTATUS
(TLDFU_API_CALL *F_TLDFU_OpenDevice)(
    TLDfuEnumerationHandle enumerationHandle,
    unsigned int deviceIndex,
    TLDfuDeviceHandle* deviceHandle,
    unsigned int flags
    );


/*!
    \brief Closes a device.

    \param[in] deviceHandle
        Identifies the device instance.
        The specified handle becomes invalid after this call and must not be used in any subsequent API calls.

    \return
        The function returns \c TLSTATUS_SUCCESS if successful, an error code otherwise.
        If a valid handle is specified, the function always returns \c TLSTATUS_SUCCESS.

    If a firmware upgrade is currently in progress for the device it is aborted.
*/
TLDFU_API_DECL
TLSTATUS
TLDFU_API_CALL
TLDFU_CloseDevice(
    TLDfuDeviceHandle deviceHandle
    );
// corresponding function type (useful for explicit loading, e.g. with GetProcAddress)
typedef
TLSTATUS
(TLDFU_API_CALL *F_TLDFU_CloseDevice)(
    TLDfuDeviceHandle deviceHandle
    );


/*!
    \brief Checks if the given device instance is still connected to the system.

    \param[in] deviceHandle
        Identifies the device instance.

    \retval TLSTATUS_SUCCESS
        The device is still connected to the system.

    \retval TLSTATUS_DEVICE_REMOVED
        The device has been disconnected.
        No further communication is possible with this device instance.
        The application should close the handle via TLDFU_CloseDevice().

    The function checks if a given device is present in the system (USB cable attached)
    or not present (USB cable detached).
*/
TLDFU_API_DECL
TLSTATUS
TLDFU_API_CALL
TLDFU_CheckDeviceConnection(
    TLDfuDeviceHandle deviceHandle
    );
// corresponding function type (useful for explicit loading, e.g. with GetProcAddress)
typedef
TLSTATUS
(TLDFU_API_CALL *F_TLDFU_CheckDeviceConnection)(
    TLDfuDeviceHandle deviceHandle
    );


/*!
    \brief This function returns an integer property of the device.

    \param[in] deviceHandle
        Identifies the device instance.

    \param[in] propertyId
        Specifies the device property to be retrieved.
        Note that the size of this parameter corresponds to the \c int type.
        This parameter can be one of the following values.
            - #TLDfuDeviceProperty_VendorId
            - #TLDfuDeviceProperty_ProductId
            - #TLDfuDeviceProperty_BcdDevice
            - #TLDfuDeviceProperty_CurrentRunMode

    \param[out] propertyValue
        Address of a caller-provided variable which will be set to the value of the property.

    \return
    The function returns \c TLSTATUS_SUCCESS if successful, an error code otherwise.
    Some specific error codes are described below.
 
    \retval TLSTATUS_NOT_AVAILABLE
    The property ID is valid, but the value is not available.
 
    \retval TLSTATUS_INVALID_PARAMETER
    The property ID is invalid.
*/
TLDFU_API_DECL
TLSTATUS
TLDFU_API_CALL
TLDFU_GetDevicePropertyUint(
    TLDfuDeviceHandle deviceHandle,
    TLDfuDeviceProperty propertyId,
    unsigned int* propertyValue
    ); 
// corresponding function type (useful for explicit loading, e.g. with GetProcAddress)
typedef
TLSTATUS
(TLDFU_API_CALL *F_TLDFU_GetDevicePropertyUint)(
    TLDfuDeviceHandle deviceHandle,
    TLDfuDeviceProperty propertyId,
    unsigned int* propertyValue
    );


/*!
    \brief This function returns a string property of the device.

    \param[in] deviceHandle
        Identifies the device instance.

    \param[in] propertyId
        Specifies the device property to be retrieved.
        Note that the size of this parameter corresponds to the \c int type.
        This parameter can be one of the following values.
            - #TLDfuDeviceProperty_UsbManufacturerString
            - #TLDfuDeviceProperty_UsbProductString
            - #TLDfuDeviceProperty_UsbSerialNumberString
            - #TLDfuDeviceProperty_DeviceInstanceIdString
            - #TLDfuDeviceProperty_PhysicalDeviceIdString

    \param[out] stringBuffer
        Points to a caller-provided string buffer that receives a null-terminated Unicode string.
        On Windows the returned string is a 16-bit WCHAR Unicode string.
        The function guarantees that the returned string is terminated by a null character.

    \param[in] stringBufferMaxItems
        Specifies the maximum number of \c T_UNICHAR items the buffer pointed to by \p stringBuffer can hold.
        Note that the buffer size is \b not specified in terms of bytes.
        The size in bytes calculates as product of \p stringBufferMaxItems and the size of
        one \c T_UNICHAR which is OS-specific and is returned by TLDFU_GetUniCharSize().

    \return
    The function returns \c TLSTATUS_SUCCESS if successful, an error code otherwise.
    Some specific error codes are described below.
 
    \retval TLSTATUS_NOT_AVAILABLE
    The property ID is valid, but the value is not available.
 
    \retval TLSTATUS_INVALID_PARAMETER
    The property ID is invalid.
*/
TLDFU_API_DECL
TLSTATUS
TLDFU_API_CALL
TLDFU_GetDevicePropertyString(
    TLDfuDeviceHandle deviceHandle,
    TLDfuDeviceProperty propertyId,
    T_UNICHAR* stringBuffer,
    unsigned int stringBufferMaxItems
    );
// corresponding function type (useful for explicit loading, e.g. with GetProcAddress)
typedef
TLSTATUS
(TLDFU_API_CALL *F_TLDFU_GetDevicePropertyString)(
    TLDfuDeviceHandle deviceHandle,
    TLDfuDeviceProperty propertyId,
    T_UNICHAR* stringBuffer,
    unsigned int stringBufferMaxItems
    );



/*!
    \brief This function returns a 32-bit integer property of the image 
    that is contained in the specified storage area.

    \param[in] deviceHandle
        Identifies the device instance.

    \param[in] targetId
        Specifies the image storage area.
        Values are device-specific and correspond to the alternate setting numbers 
        reported in the configuration descriptor of the DFU bootloader.

    \param[in] propertyId
        Specifies the image property to be retrieved.
        Note that the size of this parameter corresponds to the \c int type.
        This parameter can be one of the following values.
            - #TLDfuImageProperty_ImageTargetType
            - #TLDfuImageProperty_ImageTargetId
            - #TLDfuImageProperty_ImageVersionMajor
            - #TLDfuImageProperty_ImageVersionMinor
            - #TLDfuImageProperty_ImageVersionSub
            - #TLDfuImageProperty_ImageVersionSubSub
            - #TLDfuImageProperty_ImageCompatibilityId
            - #TLDfuImageProperty_ImageCompatibilityMask
            - #TLDfuImageProperty_Crc32Sum

    \param[out] propertyValue
        Address of a caller-provided variable which will be set to the value of the property.

    \param[in] flags
        This parameter is reserved for future use and must be set to zero.

    \return
        The function returns \c TLSTATUS_SUCCESS if successful, an error code otherwise.
        Some specific error codes are described below.
 
    \retval TLSTATUS_NOT_SUPPORTED
        The property is invalid or not supported for the property type or image type.
*/
TLDFU_API_DECL
TLSTATUS
TLDFU_API_CALL
TLDFU_GetTargetImagePropertyUint(
    TLDfuDeviceHandle deviceHandle,
    unsigned int targetId,
    TLDfuImageProperty propertyId,
    unsigned int* propertyValue,
    unsigned int flags
    );
// corresponding function type (useful for explicit loading, e.g. with GetProcAddress)
typedef
TLSTATUS
(TLDFU_API_CALL *F_TLDFU_GetTargetImagePropertyUint)(
    TLDfuDeviceHandle deviceHandle,
    unsigned int targetId,
    TLDfuImageProperty propertyId,
    unsigned int* propertyValue,
    unsigned int flags
    );


/*!
    \brief This function returns a 64-bit integer property of the image
    that is contained in the specified storage area.

    \param[in] deviceHandle
        Identifies the device instance.

    \param[in] targetId
        Specifies the image storage area.
        Values are device-specific and correspond to the alternate setting numbers 
        reported in the configuration descriptor of the DFU bootloader.

    \param[in] propertyId
        Specifies the image property to be retrieved.
        Note that the size of this parameter corresponds to the \c int type.
        This parameter can be one of the following values.
            - #TLDfuImageProperty_ImageSize
            - #TLDfuImageProperty_FlashBaseAddress
            - #TLDfuImageProperty_ImageBaseAddress
            - #TLDfuImageProperty_MaxImageSize

    \param[out] propertyValue
        Address of a caller-provided variable which will be set to the value of the property.

    \param[in] flags
        This parameter is reserved for future use and must be set to zero.

    \return
        The function returns \c TLSTATUS_SUCCESS if successful, an error code otherwise.
        Some specific error codes are described below.
 
    \retval TLSTATUS_NOT_SUPPORTED
        The property is invalid or not supported for the property type or image type.
*/
TLDFU_API_DECL
TLSTATUS
TLDFU_API_CALL
TLDFU_GetTargetImagePropertyUint64(
    TLDfuDeviceHandle deviceHandle,
    unsigned int targetId,
    TLDfuImageProperty propertyId,
    unsigned long long* propertyValue,
    unsigned int flags
    );
// corresponding function type (useful for explicit loading, e.g. with GetProcAddress)
typedef
TLSTATUS
(TLDFU_API_CALL *F_TLDFU_GetTargetImagePropertyUint64)(
    TLDfuDeviceHandle deviceHandle,
    unsigned int targetId,
    TLDfuImageProperty propertyId,
    unsigned long long* propertyValue,
    unsigned int flags
    );


/*!
    \brief This function returns a string property of the image
    that is contained in the specified storage area.

    \param[in] deviceHandle
        Identifies the device instance.

    \param[in] targetId
        Specifies the image storage area.
        Values are device-specific and correspond to the alternate setting numbers 
        reported in the configuration descriptor of the DFU bootloader.

    \param[in] propertyId
        Specifies the image property to be retrieved.
        Note that the size of this parameter corresponds to the \c int type.
        This parameter can be one of the following values.
            - #TLDfuImageProperty_ImageBuildDateTime
            - #TLDfuImageProperty_ImageDescription

    \param[out] stringBuffer
        Points to a caller-provided buffer that receives a null-terminated Unicode string.
        On Windows the returned string is a 16-bit WCHAR Unicode string.
        The function guarantees that the returned string is terminated by a null character.

    \param[in] stringBufferMaxItems
        Specifies the maximum number of \c T_UNICHAR items the buffer pointed to by \p stringBuffer can hold.
        Note that the buffer size is \b not specified in terms of bytes.
        The size in bytes calculates as product of \p stringBufferMaxItems and the size of
        one \c T_UNICHAR which is OS-specific and is returned by TLDFU_GetUniCharSize().

    \param[in] flags
        This parameter is reserved for future use and must be set to zero.

    \return
        The function returns \c TLSTATUS_SUCCESS if successful, an error code otherwise.
        Some specific error codes are described below.
 
    \retval TLSTATUS_NOT_SUPPORTED
        The property is invalid or not supported for the property type or image type.
*/
TLDFU_API_DECL
TLSTATUS
TLDFU_API_CALL
TLDFU_GetTargetImagePropertyString(
    TLDfuDeviceHandle deviceHandle,
    unsigned int targetId,
    TLDfuImageProperty propertyId,
    T_UNICHAR* stringBuffer,
    unsigned int stringBufferMaxItems,
    unsigned int flags
    );
// corresponding function type (useful for explicit loading, e.g. with GetProcAddress)
typedef
TLSTATUS
(TLDFU_API_CALL *F_TLDFU_GetTargetImagePropertyString)(
    TLDfuDeviceHandle deviceHandle,
    unsigned int targetId,
    TLDfuImageProperty propertyId,
    T_UNICHAR* stringBuffer,
    unsigned int stringBufferMaxItems,
    unsigned int flags
    );



/*!
    \brief This function sends a vendor IN request to the DFU interface.
    
    \param[in] deviceHandle
        Identifies the device instance.

    \param[in] bRequest
        bRequest field of the request.

    \param[in] wValue
        wValue field of the request.

    \param[out] buffer
        Points to a caller-provided buffer that receives the requested data.
        This parameter is required as each IN request requires a data phase.

    \param[in] bufferSize
        Size, in bytes, of the given buffer.
        This parameter must not be zero as each IN request requires a data phase.

    \param[out] bytesTransferred
        Address of a variable that will be set to the number of bytes transferred to the given buffer.
        This parameter is optional. It can be set to NULL if the caller is not interested in this information.

    \param[in] timeout
        Specifies the timeout interval, in milliseconds, for the operation.
        A negative value specifies an infinite timeout interval.
        \n \b Note: Using an infinite timeout is not recommended as this can cause 
        the calling thread to block forever.

    \return
        The function returns \c TLSTATUS_SUCCESS if successful, an error code otherwise.

    The function builds a SETUP request with type set to Vendor and recipient set to Interface, and sends it to the device.
    The wIndex field will be set to the interface number of the DFU interface (APP or DFU mode interface descriptor).
*/
TLDFU_API_DECL
TLSTATUS
TLDFU_API_CALL
TLDFU_InterfaceVendorInRequest(
    TLDfuDeviceHandle deviceHandle,
    unsigned int bRequest,     
    unsigned int wValue,
    void* buffer,                   // out
    unsigned int bufferSize,
    unsigned int* bytesTransferred,  // out, optional
    TLTimeoutInterval timeout
    );
// corresponding function type (useful for explicit loading, e.g. with GetProcAddress)
typedef
TLSTATUS
(TLDFU_API_CALL *F_TLDFU_InterfaceVendorInRequest)(
    TLDfuDeviceHandle deviceHandle,
    unsigned int bRequest,     
    unsigned int wValue,
    void* buffer,                   // out
    unsigned int bufferSize,
    unsigned int* bytesTransferred,  // out, optional
    TLTimeoutInterval timeout
    );



/*!
    \brief This function sends a vendor OUT request to the DFU interface.
    
    \param[in] deviceHandle
        Identifies the device instance.

    \param[in] bRequest
        bRequest field of the request.

    \param[in] wValue
        wValue field of the request.

    \param[in] data
        Points to a caller-provided buffer that contains the data to be sent.
        This parameter is optional.
        It can be set to NULL if the request has no data phase.

    \param[in] dataLength
        Length of the data.
        Set to zero if the request has no data phase.

    \param[out] bytesTransferred
        Address of a variable that will be set to the number of bytes transferred from the given data buffer.
        This parameter is optional. It can be set to NULL if the caller is not interested in this information.

    \param[in] timeout
        Specifies the timeout interval, in milliseconds, for the operation.
        A negative value specifies an infinite timeout interval.
        \n \b Note: Using an infinite timeout is not recommended as this can cause 
        the calling thread to block forever.

    \return
        The function returns \c TLSTATUS_SUCCESS if successful, an error code otherwise.

    The function builds a SETUP request with type set to Vendor and recipient set to Interface, and sends it to the device.
    The wIndex field will be set to the interface number of the DFU interface (APP or DFU mode interface descriptor).
*/
TLDFU_API_DECL
TLSTATUS
TLDFU_API_CALL
TLDFU_InterfaceVendorOutRequest(
    TLDfuDeviceHandle deviceHandle,
    unsigned int bRequest,     
    unsigned int wValue,
    const void* data,                // in, optional
    unsigned int dataLength,
    unsigned int* bytesTransferred,  // out, optional
    TLTimeoutInterval timeout
    );
// corresponding function type (useful for explicit loading, e.g. with GetProcAddress)
typedef
TLSTATUS
(TLDFU_API_CALL *F_TLDFU_InterfaceVendorOutRequest)(
    TLDfuDeviceHandle deviceHandle,
    unsigned int bRequest,     
    unsigned int wValue,
    const void* data,                // in, optional
    unsigned int dataLength,
    unsigned int* bytesTransferred,  // out, optional
    TLTimeoutInterval timeout
    );

/*!
  @}
*/



/*!
  \defgroup dev_add_remove_functions Device add/remove notifications
  @{

  \brief API functions for dynamic device add/remove handling
*/


/*!
    \brief Callback function which will be called when a new device is attached to the system, or removed from the system.

    \param[in] callbackContext
        The pointer value that was passed to TLDFU_RegisterDeviceChangeCallback() when the callback was registered.

    \param[in] eventType
        This value indicates the type of the event.
        Note that the size of this parameter corresponds to the \c int type.

    When receiving a \ref TLDfuEvent_DeviceRemoved event the application should call 
    TLDFU_CheckDeviceConnection() for each device it has currently opened and close, via TLDFU_CloseDevice(),
    any device instance which is not present in the system any longer.

    When receiving a \ref TLDfuEvent_DeviceAttached event an application should call TLDFU_EnumerateUsbDfuDevices()
    to create a list of devices currently connected to the system.
    Then it iterates through this list and uses TLDFU_CompareDeviceInstance() to find device instances 
    that are not opened yet.
    For each new device instance the application then calls TLDFU_OpenDevice()
    to open the device and to establish communication.

    See also \ref dyn_add_remove.

    The callback function executes in the context of an internal worker thread.
    So the callback is asynchronous with respect to any other thread of the application.

    The callback function should return as quickly as possible and should not block internally waiting
    for external events to occur.
    If the function blocks internally then this prevents subsequent notifications from being delivered.
*/
typedef
void
(TLDFU_API_CALL *TLDFU_DeviceChangeCallback) (
    void* callbackContext,
    TLDfuDeviceEvent eventType
    );


/*!
    \brief Registers a callback to receive device change notifications.

    \param[in] deviceFilterDescription
        A null-terminated string that describes the set of devices that should be considered.
        See \ref dev_filter_desc for details about the device filter description.

    \param[in] callback
        Address of a caller-provided callback function.

    \param[in] callbackContext
        Caller-provided pointer which will be passed to the callback function unmodified.
        Can be set to NULL if unused.

    \param[out] notificationHandle
        Address of a caller-provided variable which will be set to a handle that represents the registered callback.
        To unregister the callback, this handle must be passed to TLDFU_UnregisterDeviceChangeCallback().
        Note that the value #TLDFU_INVALID_HANDLE (zero) is an invalid handle.
        If the function succeeds, it will return a valid non-zero handle.

    \return
        The function returns \c TLSTATUS_SUCCESS if successful, an error code otherwise.

    The function registers an application-provided callback function which will be called
    when a new USB device instance is attached to the system, or removed from the system.

    The API supports registration of multiple callback instances.
    Each instance is represented by a handle.
    An application should unregister any callback with TLDFU_UnregisterDeviceChangeCallback() before
    the application quits or unloads the library.
*/
TLDFU_API_DECL
TLSTATUS
TLDFU_API_CALL
TLDFU_RegisterDeviceChangeCallback(
    const char* deviceFilterDescription,
    TLDFU_DeviceChangeCallback callback,
    void* callbackContext,
    TLDfuNotificationHandle* notificationHandle
    );
// corresponding function type (useful for explicit loading, e.g. with GetProcAddress)
typedef
TLSTATUS
(TLDFU_API_CALL *F_TLDFU_RegisterDeviceChangeCallback)(
    const char* deviceFilterDescription,
    TLDFU_DeviceChangeCallback callback,
    void* callbackContext,
    TLDfuNotificationHandle* notificationHandle
    );


/*!
    \brief Unregisters a device change callback previously registered with TLDFU_RegisterDeviceChangeCallback().

    \param[in] notificationHandle
        Represents the callback to be unregistered.
        The specified handle becomes invalid after this call and must not be used in any subsequent API calls.

    \return
        The function returns \c TLSTATUS_SUCCESS if successful, an error code otherwise.
        If a valid handle is specified, the function always returns \c TLSTATUS_SUCCESS.

    IMPORTANT: This function must not be called from the TLDFU_DeviceChangeCallback() callback function itself.
    Furthermore, when an application calls TLDFU_UnregisterDeviceChangeCallback() then it must not hold a resource (e.g. Mutex) which
    is also used by the callback function.
    This can cause a dead lock to occur.
*/
TLDFU_API_DECL
TLSTATUS
TLDFU_API_CALL
TLDFU_UnregisterDeviceChangeCallback(
    TLDfuNotificationHandle notificationHandle
    );
// corresponding function type (useful for explicit loading, e.g. with GetProcAddress)
typedef
TLSTATUS
(TLDFU_API_CALL *F_TLDFU_UnregisterDeviceChangeCallback)(
    TLDfuNotificationHandle notificationHandle
    );


/*!
  @}
*/



/*!
  \defgroup dfu_operation_functions DFU operation
  @{

  \brief API functions for DFU operation
*/

/*!
    \brief This function reboots the device and starts it in the given \p runMode.

    \param[in] deviceHandle
        Identifies the device instance.

    \param[in] runMode
        Specifies the run mode the device should be rebooted to.
        For possible values see \ref run_modes.

    \param[in] flags
        This parameter is set to zero or a bitwise OR combination of the following constants:
            - #TLDFU_DEVICE_FLAG_USE_XMOS_DFU_EXTENSIONS

                The XMOS specific DFU requests should be used when communicating with the device.

    \return
        The function returns \c TLSTATUS_SUCCESS if successful, an error code otherwise.
        Some specific error codes are described below.

    \retval TLSTATUS_ALREADY_DONE
        The device already runs in the specified mode.

    Typically the device temporarily disconnects and the open device disappears.
    The device will reconnect autonomously and appears as another device running in the requested 
    run mode. So the specified \p deviceHandle can not be used to call another API function and 
    must be closed with a call to TLDFU_CloseDevice().

    The function initiates the reboot but does not wait until the device reconnects.
    The application may enumerate periodically until it detects the new device.

    If a upgrade operation is currently in progress the function fails.
*/
TLDFU_API_DECL
TLSTATUS
TLDFU_API_CALL
TLDFU_RebootDevice(
    TLDfuDeviceHandle deviceHandle,
    unsigned int runMode,
    unsigned int flags
    );
// corresponding function type (useful for explicit loading, e.g. with GetProcAddress)
typedef
TLSTATUS
(TLDFU_API_CALL *F_TLDFU_RebootDevice)(
    TLDfuDeviceHandle deviceHandle,
    unsigned int runMode,
    unsigned int flags
    );


/*!
    \brief Starts an upgrade operation to download the specified image to the device.

    \param[in] deviceHandle
        Identifies the device instance.

    \param[in] imageHandle
        Identifies the image to be downloaded.

    \param[in] targetId
        Specifies the image storage area.
        Values are device-specific and correspond to the alternate setting numbers 
        reported in the configuration descriptor of the DFU bootloader.

    \param[in] flags
        This parameter is set to zero or a bitwise OR combination of the following constants:
            - #TLDFU_DEVICE_FLAG_USE_XMOS_DFU_EXTENSIONS

                The XMOS specific DFU requests should be used when communicating with the device.

            - #TLDFU_DEVICE_FLAG_UNLOCK_TARGET

                Unlock the target through a proprietary unlock-mechanism before downloading an image to the target.

    \return
        The function returns \c TLSTATUS_SUCCESS if successful, an error code otherwise.
        Some specific error codes are described below.

    \retval TLSTATUS_WRONG_DEVICE_STATE
        The device is not running in DFU (bootloader) mode currently.

    \retval TLSTATUS_NOT_ALLOWED
        A download or upload operation is already in progress.

    \retval TLSTATUS_NOT_SUPPORTED
        The device does not support image download.

    A successful call to TLDFU_StartUpgrade() initializes an internal state machine which performs the upgrade operation.
    An application calls TLDFU_GetUpgradeStatus() to query current state and monitor progress of the operation.

    The overall duration of the download procedure depends on the DFU implementation in the device.
    When finished, the state machine enters either \ref TLDfuUpgradeState_Finished or \ref TLDfuUpgradeState_Failed.
    An application should call TLDFU_GetUpgradeStatus() periodically until one of those states is reported.
    
    When TLDFU_GetUpgradeStatus() reports \ref TLDfuUpgradeState_Finished or \ref TLDfuUpgradeState_Failed,
    the operation has completed and the application has to call TLDFU_FinishUpgrade() 
    to finalize the operation and free internal resources.

    A call to TLDFU_StartUpgrade() fails if an operation is already in progress.
*/
TLDFU_API_DECL
TLSTATUS
TLDFU_API_CALL
TLDFU_StartUpgrade(
    TLDfuDeviceHandle deviceHandle,
    TLDfuImageHandle imageHandle,
    unsigned int targetId,
    unsigned int flags
    );
// corresponding function type (useful for explicit loading, e.g. with GetProcAddress)
typedef
TLSTATUS
(TLDFU_API_CALL *F_TLDFU_StartUpgrade)(
    TLDfuDeviceHandle deviceHandle,
    TLDfuImageHandle imageHandle,
    unsigned int targetId,
    unsigned int flags
    );


/*!
    \brief Queries the status of the current upgrade operation.

    \param[in] deviceHandle
        Identifies the device instance.

    \param[out] upgradeState
        Address of a caller-provided variable that will be set to the current state of the operation.
        See the comments below and \ref TLDfuUpgradeState for more information.

    \param[out] currentBytes
        Address of a caller-provided variable that will be set to the number of bytes already processed.
        The parameter is optional and can be set to \c NULL if the information is not needed.

    \param[out] totalBytes
        Address of a caller-provided variable that will be set to the total number of bytes to be processed.
        The parameter is optional and can be set to \c NULL if the information is not needed.
        This information can also be retrieved through the image property \ref TLDfuImageProperty_ImageSize.
    
    \param[out] completionStatus
        Address of a caller-provided variable that will be set to the final completion status of the operation.
        The parameter is optional and can be set to \c NULL if the information is not needed.

    \return
        The function returns \c TLSTATUS_SUCCESS if successful, an error code otherwise.
        Some specific error codes are described below.

    \retval TLSTATUS_WRONG_DEVICE_STATE
        The device is not running in DFU (bootloader) mode currently.
    
    An application should check the \p upgradeState variable first.
    If the variable is set to \ref TLDfuUpgradeState_Finished or \ref TLDfuUpgradeState_Failed then the operation has completed
    and the application has to call TLDFU_FinishUpgrade() to finalize the operation.
    
    The state \ref TLDfuUpgradeState_Failed indicates that the operation has failed.
    In this case the \p completionStatus variable will be set to a specific error code.
*/
TLDFU_API_DECL
TLSTATUS
TLDFU_API_CALL
TLDFU_GetUpgradeStatus(
    TLDfuDeviceHandle deviceHandle,
    TLDfuUpgradeState* upgradeState,
    unsigned long long* currentBytes,
    unsigned long long* totalBytes,
    TLSTATUS* completionStatus
    );
// corresponding function type (useful for explicit loading, e.g. with GetProcAddress)
typedef
TLSTATUS
(TLDFU_API_CALL *F_TLDFU_GetUpgradeStatus)(
    TLDfuDeviceHandle deviceHandle,
    TLDfuUpgradeState* upgradeState,
    unsigned long long* currentBytes,
    unsigned long long* totalBytes,
    TLSTATUS* completionStatus
    );


/*!
    \brief Finishes the upgrade operation.

    \param[in] deviceHandle
        Identifies the device instance.

    \param[in] flags
        This parameter is reserved for future use and must be set to zero.

    \return
        The function returns \c TLSTATUS_SUCCESS if successful, an error code otherwise.
        Some specific error codes are described below.

    \retval TLSTATUS_ABORTED
        The function has aborted the current operation which was not completed yet.

    The function must be used to finalize an upgrade operation that was indicated as completed.
    For more details, see also the comments TLDFU_StartUpgrade().

    The function can be used to abort an operation that is still in progress.
    In this case, the function resets the internal state machine only. 
    It does not initiate a state change in the device, and does not reboot the device. 
*/
TLDFU_API_DECL
TLSTATUS
TLDFU_API_CALL
TLDFU_FinishUpgrade(
    TLDfuDeviceHandle deviceHandle,
    unsigned int flags
    );
// corresponding function type (useful for explicit loading, e.g. with GetProcAddress)
typedef
TLSTATUS
(TLDFU_API_CALL *F_TLDFU_FinishUpgrade)(
    TLDfuDeviceHandle deviceHandle,
    unsigned int flags
    );



/*!
    \brief Starts a readout operation to retrieve image data from the device.

    \param[in] deviceHandle
        Identifies the device instance.

    \param[in] targetId
        Specifies the image storage area.
        Values are device-specific and correspond to the alternate setting numbers 
        reported in the configuration descriptor of the DFU bootloader.

    \param[in] flags
        This parameter is set to zero or a bitwise OR combination of the following constants:
            - #TLDFU_DEVICE_FLAG_USE_XMOS_DFU_EXTENSIONS

                The XMOS specific DFU requests should be used when communicating with the device.

    \return
        The function returns \c TLSTATUS_SUCCESS if successful, an error code otherwise.
        Some specific error codes are described below.

    \retval TLSTATUS_WRONG_DEVICE_STATE
        The device is not running in DFU (bootloader) mode currently.

    \retval TLSTATUS_NOT_ALLOWED
        A download or upload operation is already in progress.

    \retval TLSTATUS_NOT_SUPPORTED
        The device does not support image upload.

    A successful call to TLDFU_StartReadout() initializes an internal state machine which performs the upgrade operation.
    An application calls TLDFU_GetReadoutStatus() to query current state and monitor progress of the operation.

    The overall duration of the upload procedure depends on the DFU implementation in the device.
    When finished, the state machine enters either \ref TLDfuReadoutState_Finished or \ref TLDfuReadoutState_Failed.
    An application should call TLDFU_GetReadoutStatus() periodically until one of those states is reported.
    
    When TLDFU_GetReadoutStatus() reports \ref TLDfuReadoutState_Finished or \ref TLDfuReadoutState_Failed,
    the operation has completed and the application has to call TLDFU_FinishReadout() 
    to finalize the operation, retrieve the image data, and free internal resources.

    A call to TLDFU_StartReadout() fails if an operation is already in progress.
*/
TLDFU_API_DECL
TLSTATUS
TLDFU_API_CALL
TLDFU_StartReadout(
    TLDfuDeviceHandle deviceHandle,
    unsigned int targetId,
    unsigned int flags
    );
// corresponding function type (useful for explicit loading, e.g. with GetProcAddress)
typedef
TLSTATUS
(TLDFU_API_CALL *F_TLDFU_StartReadout)(
    TLDfuDeviceHandle deviceHandle,
    unsigned int targetId,
    unsigned int flags
    );



/*!
    \brief Queries the status of the current readout operation.

    \param[in] deviceHandle
        Identifies the device instance.

    \param[out] readoutState
        Address of a caller-provided variable that will be set to the current state of the operation.
        See the comments below and \ref TLDfuReadoutState for more information.

    \param[out] currentBytes
        Address of a caller-provided variable that will be set to the number of bytes already processed.
        The parameter is optional and can be set to \c NULL if the information is not needed.

    \param[out] completionStatus
        Address of a caller-provided variable that will be set to the final completion status of the operation.
        The parameter is optional and can be set to \c NULL if the information is not needed.

    \return
        The function returns \c TLSTATUS_SUCCESS if successful, an error code otherwise.
        Some specific error codes are described below.

    \retval TLSTATUS_WRONG_DEVICE_STATE
        The device is not running in DFU (bootloader) mode currently.
    
    An application should check the \p readoutState variable first.
    If the variable is set to \ref TLDfuReadoutState_Finished or \ref TLDfuReadoutState_Failed then the operation has completed
    and the application has to call TLDFU_FinishReadout() to finalize the operation and retrieve the image data.
    
    The state \ref TLDfuReadoutState_Failed indicates that the operation has failed.
    In this case the \p completionStatus variable will be set to a specific error code.
*/
TLDFU_API_DECL
TLSTATUS
TLDFU_API_CALL
TLDFU_GetReadoutStatus(
    TLDfuDeviceHandle deviceHandle,
    TLDfuReadoutState* readoutState,
    unsigned long long* currentBytes,
    TLSTATUS* completionStatus
    );
// corresponding function type (useful for explicit loading, e.g. with GetProcAddress)
typedef
TLSTATUS
(TLDFU_API_CALL *F_TLDFU_GetReadoutStatus)(
    TLDfuDeviceHandle deviceHandle,
    TLDfuReadoutState* readoutState,
    unsigned long long* currentBytes,
    TLSTATUS* completionStatus
    );



/*!
    \brief Finishes the readout operation.

    \param[in] deviceHandle
        Identifies the device instance.

    \param[out] imageHandle
        Address of a caller-provided variable which will be set to a handle value that represents the
        image that has been retrieved from the device.
        To extract the image data, use TLDFU_StoreFirmwareInBuffer().
        If the function succeeds, it will return a valid non-zero handle,
        otherwise it returns #TLDFU_INVALID_HANDLE (zero) which is an invalid handle.

    \param[in] flags
        This parameter is reserved for future use and must be set to zero.

    \return
        The function returns \c TLSTATUS_SUCCESS if successful, an error code otherwise.
        Some specific error codes are described below.

    \retval TLSTATUS_ABORTED
        The function has aborted the current operation which was not completed yet.

    The function must be used to finalize an upgrade operation that was indicated as completed.
    For more details, see also the comments TLDFU_StartReadout().

    The function can be used to abort an operation that is still in progress.
    In this case, the function resets the internal state machine only. 
    It does not initiate a state change in the device, and does not reboot the device. 
*/
TLDFU_API_DECL
TLSTATUS
TLDFU_API_CALL
TLDFU_FinishReadout(
    TLDfuDeviceHandle deviceHandle,
    TLDfuImageHandle* imageHandle,
    unsigned int flags
    );
// corresponding function type (useful for explicit loading, e.g. with GetProcAddress)
typedef
TLSTATUS
(TLDFU_API_CALL *F_TLDFU_FinishReadout)(
    TLDfuDeviceHandle deviceHandle,
    TLDfuImageHandle* imageHandle,
    unsigned int flags
    );


/*!
  @}
*/



/*!
  \defgroup firmware_image_functions Firmware image handling
  @{

  \brief API functions for firmware image handling
*/

/*!
    \defgroup load_image_flags Load image flags.
    @{

    \brief Flags that can be used with image loading functions.
*/

//! If the image type is set to \ref TLDfuImageType_TLBinary, the CRC of the image will be checked when the image is loaded.
#define TLDFU_IMAGE_FLAG_ENFORCE_CRC_CHECK      0x00000001u

/*!
@}
*/

/*!
    \brief Loads an image from a file.

    \param[in] filePathAndName
        A null-terminated string that specifies the path and name of the image file.

    \param[in] imageType
        The type of the image to be loaded.
        For possible values see \ref TLDfuImageType.

    \param[out] imageHandle
        Address of a caller-provided variable which will be set to a handle value if the function succeeds.
        Note that the value #TLDFU_INVALID_HANDLE (zero) is an invalid handle.
        If the function succeeds, it will return a valid non-zero handle.
        When the handle is no longer needed it must be unloaded with TLDFU_UnloadFirmwareImage() to free internal resources.
        
    \param[in] flags
        This parameter is set to zero or a bitwise OR combination of the following constants:
            - #TLDFU_IMAGE_FLAG_ENFORCE_CRC_CHECK

                If \p image type is set to \ref TLDfuImageType_TLBinary, the CRC of the image will be checked.
                It is recommended to always specify this flag for images of type \ref TLDfuImageType_TLBinary.
                For image types other than \ref TLDfuImageType_TLBinary, this flag will be ignored.

    \return
        The function returns \c TLSTATUS_SUCCESS if successful, an error code otherwise.
*/
TLDFU_API_DECL
TLSTATUS
TLDFU_API_CALL
TLDFU_LoadFirmwareImageFromFile(
    const T_UNICHAR* filePathAndName,
    TLDfuImageType imageType,
    TLDfuImageHandle* imageHandle,
    unsigned int flags
    );
// corresponding function type (useful for explicit loading, e.g. with GetProcAddress)
typedef
TLSTATUS
(TLDFU_API_CALL *F_TLDFU_LoadFirmwareImageFromFile)(
    const T_UNICHAR* filePathAndName,
    TLDfuImageType imageType,
    TLDfuImageHandle* imageHandle,
    unsigned int flags
    );


/*!
    \brief Loads an image from the provided \p imageData.

    \param[in] imageData
        Address of a caller-provided buffer which contains the data of the image to be loaded.

    \param[in] imageDataSize
        Specifies the size, in bytes, of the image data given with \p imageData.

    \param[in] imageType
        The type of the image to be loaded.
        For possible values see \ref TLDfuImageType.

    \param[out] imageHandle
        Address of a caller-provided variable which will be set to a handle value if the function succeeds.
        Note that the value #TLDFU_INVALID_HANDLE (zero) is an invalid handle.
        If the function succeeds, it will return a valid non-zero handle.
        When the handle is no longer needed it must be unloaded with TLDFU_UnloadFirmwareImage() to free internal resources.
        
    \param[in] flags
        This parameter is set to zero or a bitwise OR combination of the following constants:
            - #TLDFU_IMAGE_FLAG_ENFORCE_CRC_CHECK

                If \p image type is set to \ref TLDfuImageType_TLBinary, the CRC of the image will be checked.
                It is recommended to always specify this flag for images of type \ref TLDfuImageType_TLBinary.
                For image types other than \ref TLDfuImageType_TLBinary, this flag will be ignored.

    \return
        The function returns \c TLSTATUS_SUCCESS if successful, an error code otherwise.
*/
TLDFU_API_DECL
TLSTATUS
TLDFU_API_CALL
TLDFU_LoadFirmwareFromBuffer(
    const void* imageData,
    unsigned long long imageDataSize,
    TLDfuImageType imageType,
    TLDfuImageHandle* imageHandle,
    unsigned int flags
    );
// corresponding function type (useful for explicit loading, e.g. with GetProcAddress)
typedef
TLSTATUS
(TLDFU_API_CALL *F_TLDFU_LoadFirmwareFromBuffer)(
    const void* imageData,
    unsigned long long imageDataSize,
    TLDfuImageType imageType,
    TLDfuImageHandle* imageHandle,
    unsigned int flags
    );



/*!
    \brief Copies the image data to a provided buffer.

    \param[in] imageHandle
        Identifies the image instance.

    \param[in] buffer
        Address of a caller-provided buffer which receives the data.

    \param[in] bufferSize
        Specifies the size, in bytes, of the provided buffer.
        The buffer must be large enough to hold the entire image, otherwise the function fails.
        The total image size can be determined by querying the \ref TLDfuImageProperty_ImageSize property via TLDFU_GetImagePropertyUint64().

    \param[out] bytesCopied
        Address of a caller-provided variable which will be set to the number of bytes
        that have been copied to the provided buffer if the function succeeds.
        The parameter is optional and can be set to \c NULL if the information is not needed.
        
    \param[in] flags
        This parameter is reserved for future use and must be set to zero.

    \return
        The function returns \c TLSTATUS_SUCCESS if successful, an error code otherwise.
        Some specific error codes are described below.

    \retval TLSTATUS_BUFFER_TOO_SMALL
        The provided buffer is too small to hold the image data.
        No data have been copied to the buffer.
*/
TLDFU_API_DECL
TLSTATUS
TLDFU_API_CALL
TLDFU_StoreFirmwareInBuffer(
    TLDfuImageHandle imageHandle,
    void* buffer,
    unsigned long long bufferSize,
    unsigned long long* bytesCopied,
    unsigned int flags
    );
// corresponding function type (useful for explicit loading, e.g. with GetProcAddress)
typedef
TLSTATUS
(TLDFU_API_CALL *F_TLDFU_StoreFirmwareInBuffer)(
    TLDfuImageHandle imageHandle,
    void* buffer,
    unsigned long long bufferSize,
    unsigned long long* bytesCopied,
    unsigned int flags
    );



/*!
    \brief Unloads an image.

    \param[in] imageHandle
        Identifies the image instance.
        The specified handle becomes invalid after this call and must not be used in any subsequent API calls.

    \return
        The function returns \c TLSTATUS_SUCCESS if successful, an error code otherwise.
        If a valid handle is specified, the function always returns \c TLSTATUS_SUCCESS.
*/
TLDFU_API_DECL
TLSTATUS
TLDFU_API_CALL
TLDFU_UnloadFirmwareImage(
    TLDfuImageHandle imageHandle
    );
// corresponding function type (useful for explicit loading, e.g. with GetProcAddress)
typedef
TLSTATUS
(TLDFU_API_CALL *F_TLDFU_UnloadFirmwareImage)(
    TLDfuImageHandle imageHandle
    );


/*!
    \brief This function returns a 32-bit integer property of the image.

    \param[in] imageHandle
        Identifies the image instance.

    \param[in] propertyId
        Specifies the image property to be retrieved.
        Note that the size of this parameter corresponds to the \c int type.
        This parameter can be one of the following values.
            - #TLDfuImageProperty_ImageTargetType
            - #TLDfuImageProperty_ImageTargetId
            - #TLDfuImageProperty_ImageVersionMajor
            - #TLDfuImageProperty_ImageVersionMinor
            - #TLDfuImageProperty_ImageVersionSub
            - #TLDfuImageProperty_ImageVersionSubSub
            - #TLDfuImageProperty_ImageCompatibilityId
            - #TLDfuImageProperty_ImageCompatibilityMask
            - #TLDfuImageProperty_Crc32Sum
            - #TLDfuImageProperty_HasDfuFileSuffix
            - #TLDfuImageProperty_VendorId
            - #TLDfuImageProperty_ProductId
            - #TLDfuImageProperty_BcdDevice
            - #TLDfuImageProperty_BcdDFU

    \param[out] propertyValue
        Address of a caller-provided variable which will be set to the value of the property.

    \return
        The function returns \c TLSTATUS_SUCCESS if successful, an error code otherwise.
        Some specific error codes are described below.
 
    \retval TLSTATUS_NOT_SUPPORTED
        The property is invalid or not supported for the property type or image type.
*/
TLDFU_API_DECL
TLSTATUS
TLDFU_API_CALL
TLDFU_GetImagePropertyUint(
    TLDfuImageHandle imageHandle,
    TLDfuImageProperty propertyId,
    unsigned int* propertyValue
    );
// corresponding function type (useful for explicit loading, e.g. with GetProcAddress)
typedef
TLSTATUS
(TLDFU_API_CALL *F_TLDFU_GetImagePropertyUint)(
    TLDfuImageHandle imageHandle,
    TLDfuImageProperty propertyId,
    unsigned int* propertyValue
    );


/*!
    \brief This function returns a 64-bit integer property of the image.

    \param[in] imageHandle
        Identifies the image instance.

    \param[in] propertyId
        Specifies the image property to be retrieved.
        Note that the size of this parameter corresponds to the \c int type.
        This parameter can be one of the following values.
            - #TLDfuImageProperty_ImageSize
            - #TLDfuImageProperty_FlashBaseAddress
            - #TLDfuImageProperty_ImageBaseAddress

    \param[out] propertyValue
        Address of a caller-provided variable which will be set to the value of the property.

    \return
        The function returns \c TLSTATUS_SUCCESS if successful, an error code otherwise.
        Some specific error codes are described below.
 
    \retval TLSTATUS_NOT_SUPPORTED
        The property is invalid or not supported for the property type or image type.
*/
TLDFU_API_DECL
TLSTATUS
TLDFU_API_CALL
TLDFU_GetImagePropertyUint64(
    TLDfuImageHandle imageHandle,
    TLDfuImageProperty propertyId,
    unsigned long long* propertyValue
    );
// corresponding function type (useful for explicit loading, e.g. with GetProcAddress)
typedef
TLSTATUS
(TLDFU_API_CALL *F_TLDFU_GetImagePropertyUint64)(
    TLDfuImageHandle imageHandle,
    TLDfuImageProperty propertyId,
    unsigned long long* propertyValue
    );


/*!
    \brief This function returns a string property of the image.

    \param[in] imageHandle
        Identifies the image instance.

    \param[in] propertyId
        Specifies the image property to be retrieved.
        Note that the size of this parameter corresponds to the \c int type.
        This parameter can be one of the following values.
            - #TLDfuImageProperty_ImageBuildDateTime
            - #TLDfuImageProperty_ImageDescription

    \param[out] stringBuffer
        Points to a caller-provided buffer that receives a null-terminated Unicode string.
        On Windows the returned string is a 16-bit WCHAR Unicode string.
        The function guarantees that the returned string is terminated by a null character.

    \param[in] stringBufferMaxItems
        Specifies the maximum number of \c T_UNICHAR items the buffer pointed to by \p stringBuffer can hold.
        Note that the buffer size is \b not specified in terms of bytes.
        The size in bytes calculates as product of \p stringBufferMaxItems and the size of
        one \c T_UNICHAR which is OS-specific and is returned by TLDFU_GetUniCharSize().

    \return
        The function returns \c TLSTATUS_SUCCESS if successful, an error code otherwise.
        Some specific error codes are described below.
 
    \retval TLSTATUS_NOT_SUPPORTED
        The property is invalid or not supported for the property type or image type.
*/
TLDFU_API_DECL
TLSTATUS
TLDFU_API_CALL
TLDFU_GetImagePropertyString(
    TLDfuImageHandle imageHandle,
    TLDfuImageProperty propertyId,
    T_UNICHAR* stringBuffer,
    unsigned int stringBufferMaxItems
    );
// corresponding function type (useful for explicit loading, e.g. with GetProcAddress)
typedef
TLSTATUS
(TLDFU_API_CALL *F_TLDFU_GetImagePropertyString)(
    TLDfuImageHandle imageHandle,
    TLDfuImageProperty propertyId,
    T_UNICHAR* stringBuffer,
    unsigned int stringBufferMaxItems
    );

/*!
  @}
*/



#if defined (__cplusplus)
}
#endif


#endif  // __tlusbdfu_api_h__

/*************************** EOF **************************************/
