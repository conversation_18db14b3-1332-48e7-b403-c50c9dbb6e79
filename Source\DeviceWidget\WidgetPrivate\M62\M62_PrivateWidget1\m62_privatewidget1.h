#ifndef M62_PrivateWidget1_H
#define M62_PrivateWidget1_H

#include "framelesswindow.h"
#include "workspace.h"
#include "appsettings.h"

namespace Ui {
class M62_PrivateWidget1;
}

class DialS1M1;
class QLabel;
class M62_PrivateWidget1 : public FramelessWindow, public WorkspaceObserver, public AppSettingsObserver {
    Q_OBJECT

public:
    explicit M62_PrivateWidget1(QWidget *parent = nullptr, const QString& name = {});
    ~M62_PrivateWidget1();
    void setName(const QString &name);
    void setWidthHeight(bool isCenter = true);
    void show();
    bool isWidgetEmitAction() { return mEmitAction; }
    void setWidgetEmitAction(bool state)
    {
        mEmitAction = state;
    }

    void setFont(const QFont& font);
    void setINName(const QString& str);
    void setINChannelName(const QString& str);
    void setVolumeMeterLeft(int value);
    void setVolumeMeterRight(int value);

    float getThresholdDefaultValue();
    void setThresholdDefaultValue(float value);
    void setThresholdValue(float value, bool isSendSig=true);
    void setThresholdRange(float min, float max);
    void setThresholdLabelL(const QString& text);
    void setThresholdLabelR(const QString& text);

    float getAttackDefaultValue();
    void setAttackDefaultValue(float value);
    void setAttackValue(float value, bool isSendSig=true);
    void setAttackRange(float min, float max);
    void setAttackLabelL(const QString& text);
    void setAttackLabelR(const QString& text);

    float getReductionDefaultValue();
    void setReductionDefaultValue(float value);
    void setReductionValue(float value, bool isSendSig=true);
    void setReductionRange(float min, float max);
    void setReductionLabelL(const QString& text);
    void setReductionLabelR(const QString& text);

    float getReleaseDefaultValue();
    void setReleaseDefaultValue(float value);
    void setReleaseValue(float value, bool isSendSig=true);
    void setReleaseRange(float min, float max);
    void setReleaseLabelL(const QString& text);
    void setReleaseLabelR(const QString& text);

    void setButtonOFFChecked(bool isChecked, bool isSendSig=true, bool isSendWorkState = true);
    void setIN1AUXChecked(bool isChecked, bool isSendSig=true);
    void setIN2AUXChecked(bool isChecked, bool isSendSig=true);
    void setIN1BTChecked(bool isChecked, bool isSendSig=true);
    void setIN2BTChecked(bool isChecked, bool isSendSig=true);
    void setIN1OTGChecked(bool isChecked, bool isSendSig=true);
    void setIN2OTGChecked(bool isChecked, bool isSendSig=true);
    void setplayback1_2IN1Checked(bool isChecked, bool isSendSig=true);
    void setplayback1_2IN2Checked(bool isChecked, bool isSendSig=true);
    void setplayback3_4IN1Checked(bool isChecked, bool isSendSig=true);
    void setplayback3_4IN2Checked(bool isChecked, bool isSendSig=true);
    void setplayback5_6IN1Checked(bool isChecked, bool isSendSig=true);
    void setplayback5_6IN2Checked(bool isChecked, bool isSendSig=true);
    void setplayback7_8IN1Checked(bool isChecked, bool isSendSig=true);
    void setplayback7_8IN2Checked(bool isChecked, bool isSendSig=true);
    void setplayback9_10IN1Checked(bool isChecked, bool isSendSig=true);
    void setplayback9_10IN2Checked(bool isChecked, bool isSendSig=true);

    void setPlayback3_4BackIsVisible(bool isVisible);

protected:
    void resizeEvent(QResizeEvent* e)override;
    void showEvent(QShowEvent* e)override;
    void setAllChildFont(QWidget* widget, const QFont& font);
    void loadSettings() override;
    void AppSettingsChanged(QString objectName, QString attribute, QString value) override;
    void updateAttribute();
    void saveAttribute(QString attribute, QString value = {});
    void reset();
    uchar calculateMapValue(const QString& prefix);
    void setOFFButtonText(bool isChecked);
    void sendSigWorkState(int index);

private slots:
    void in_mWidgetListAll_attributeChanged(QString objectName, QString attribute, QString value);

    void on_buttonOFF_toggled(bool checked);

    void on_IN1AUX_toggled(bool checked);

    void on_IN2AUX_toggled(bool checked);

    void on_IN1BT_toggled(bool checked);

    void on_IN2BT_toggled(bool checked);

    void on_IN1OTG_toggled(bool checked);

    void on_IN2OTG_toggled(bool checked);

    void on_playback1_2IN1_toggled(bool checked);

    void on_playback1_2IN2_toggled(bool checked);

    void on_playback3_4IN1_toggled(bool checked);

    void on_playback3_4IN2_toggled(bool checked);

    void on_playback5_6IN1_toggled(bool checked);

    void on_playback5_6IN2_toggled(bool checked);

    void on_playback7_8IN1_toggled(bool checked);

    void on_playback7_8IN2_toggled(bool checked);

    void on_playback9_10IN1_toggled(bool checked);

    void on_playback9_10IN2_toggled(bool checked);

signals:
    void attributeChanged(QString objectName, QString attribute, QString value);

private:
    void setDialGeometry(QWidget* dial, QLabel* top, QLabel* left, QLabel* right);
    void initConnect();
    void adjustGeometry();

private:
    Ui::M62_PrivateWidget1 *ui;
    QWidget* mWidget;
    bool mEmitAction = false;
    QString mLineEditText;
    double mThreshold;
    double mAttack;
    double mReduction;
    double mRelease;
    int mOFF;
    int mIN1AUX;
    int mIN2AUX;
    int mIN1BT;
    int mIN2BT;
    int mIN1OTGIN;
    int mIN2OTGIN;
    int mIN1Playback1_2;
    int mIN2Playback1_2;
    int mIN1Playback3_4;
    int mIN2Playback3_4;
    int mIN1Playback5_6;
    int mIN2Playback5_6;
    int mIN1Playback7_8;
    int mIN2Playback7_8;
    int mIN1Playback9_10;
    int mIN2Playback9_10;
    int mIn1;
    int mIn2;
};

#endif // M62_PrivateWidget1_H
