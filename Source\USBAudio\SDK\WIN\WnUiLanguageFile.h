/************************************************************************
 *  The module is used to access a file containing texts for an UI language.
 *  
 *  Thesycon GmbH, Germany      
 *  http://www.thesycon.de
 *
 ************************************************************************/

#ifndef __WnUiLanguageFile_h__
#define __WnUiLanguageFile_h__

// optionally put everything into a namespace
#ifdef LIBWN_NAMESPACE
namespace LIBWN_NAMESPACE {
#endif

#define UILF_COMMENT_BEGIN_TOKEN L";"
#define UILF_COMMENT_END_TOKEN L""
#define UILF_VALUE_BEGIN_TOKEN L""
#define UILF_VALUE_END_TOKEN L""
#define UILF_LINE_CONTINUATION_TOKEN L"\\"
#define UILF_ID_TO_VALUE_DELIMITER L"="

//
// Wrapper class for an UI language.
//
class WnUiLanguageFile
{
//construction/destruction/assignment
public:    
    WnUiLanguageFile();        
    ~WnUiLanguageFile();
    
    // make copy constructor and assignment operator unavailable
    PRIVATE_COPY_CONSTRUCTOR(WnUiLanguageFile)
    PRIVATE_ASSIGNMENT_OPERATOR(WnUiLanguageFile)

//interface
public:
    //
    // get/set the full path and name of the file
    //
    const WString& GetFilePathAndName() const
    {
        return mFilePathAndName;
    }

    void SetFilePathAndName(const WString& file)
    {
        mFilePathAndName = file;
    }
    
    //
    // Try to read the language text from the file for each text instance specified within the given text groups. 
    // Text instances for which no language text could be found remain unchanged.
    //   
    WNERR
    Read(
        WnUiLanguageTextGroupVector& textGroups
    );

    //
    // Write the content of the given text groups to the file.  
    //
    // Note: The directory of the file must already exist. Otherwise the function fails.
    //
    // parameters:
    //      textGroups      groups with UI language texts
    //      onlyDefault     true: write only the default language texts
    //                      false: write the language texts as far as available, otherwise the default language texts
    //
    WNERR
    Write(
        const WnUiLanguageTextGroupVector& textGroups,
        bool onlyDefault
    );

private:
    
//data
private:
    //full path and name of the file
    WString mFilePathAndName;    

    //token at the beginning of a comment within the file
    WString mCommentBeginToken{UILF_COMMENT_BEGIN_TOKEN};

    //Optional token at the end of a comment within the file. If no end token is specified
    //a comment automatically ends with the line.
    WString mCommentEndToken{UILF_COMMENT_END_TOKEN};

    //Optional token at the beginning of a language text value within the file.
    WString mValueBeginToken{UILF_VALUE_BEGIN_TOKEN};

    //Optional token at the end of a language text value within the file.
    WString mValueEndToken{UILF_VALUE_END_TOKEN};

    //Optional token at the end of a line used to indicate that the value is continued in the next line.
    //Leading white spaces in the next line are ignored in this case. If no token is specified a line
    //cannot be continued.
    WString mLineContinuationToken{UILF_LINE_CONTINUATION_TOKEN};

    //delimiter between identifier and value of a language text within a file
    WString mIdToValueDelimiter{UILF_ID_TO_VALUE_DELIMITER};
};

#ifdef LIBWN_NAMESPACE
}
#endif

#endif // __WnUiLanguageFile_h__

/*************************** EOF **************************************/
