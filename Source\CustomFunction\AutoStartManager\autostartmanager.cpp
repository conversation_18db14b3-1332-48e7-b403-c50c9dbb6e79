#include "autostartmanager.h"
#include <QSettings>
#include <QCoreApplication>
#include <QDir>

#ifdef Q_OS_MACOS
#include <CoreFoundation/CoreFoundation.h>
#include <CoreServices/CoreServices.h>
#include <QStandardPaths>
#include <QProcess>
#endif

AutoStartManager::AutoStartManager(QObject *parent) : QObject(parent)
{
}

AutoStartManager::~AutoStartManager()
{
}

void AutoStartManager::setAutoStart(bool enable)
{
#ifdef Q_OS_WIN
    setAutoStartForWindows(enable);
#elif defined(Q_OS_MACOS)
    setAutoStartForMac(enable);
#endif
}

bool AutoStartManager::isAutoStartEnabled() const
{
#ifdef Q_OS_WIN
    return isAutoStartEnabledForWindows();
#elif defined(Q_OS_MACOS)
    return isAutoStartEnabledForMac();
#endif
    return false;
}

void AutoStartManager::setAutoStartForWindows(bool enable)
{
#ifdef Q_OS_WIN
    QSettings settings("HKEY_CURRENT_USER\\Software\\Microsoft\\Windows\\CurrentVersion\\Run", 
                      QSettings::NativeFormat);
    QString appName = QCoreApplication::applicationName();
    QString appPath = QDir::toNativeSeparators(QCoreApplication::applicationFilePath());

    if (enable) {
        settings.setValue(appName, QString("\"%1\"").arg(appPath));
    } else {
        settings.remove(appName);
    }
#endif
}

bool AutoStartManager::isAutoStartEnabledForWindows() const
{
#ifdef Q_OS_WIN
    QSettings settings("HKEY_CURRENT_USER\\Software\\Microsoft\\Windows\\CurrentVersion\\Run", 
                      QSettings::NativeFormat);
    QString appName = QCoreApplication::applicationName();
    return settings.contains(appName);
#else
    return false;
#endif
}

void AutoStartManager::setAutoStartForMac(bool enable)
{
#ifdef Q_OS_MACOS
    QString appBundlePath = QCoreApplication::applicationFilePath();

    QString launchAgentPath = QStandardPaths::writableLocation(QStandardPaths::HomeLocation) 
                            + "/Library/LaunchAgents/";
    QDir().mkpath(launchAgentPath);
    
    QString plistPath = launchAgentPath + "com." + QCoreApplication::organizationName().toLower() 
                       + "." + QCoreApplication::applicationName().toLower() + ".plist";

    if (enable) {
        QString plistContent = QString(
            "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n"
            "<!DOCTYPE plist PUBLIC \"-//Apple//DTD PLIST 1.0//EN\" "
            "\"http://www.apple.com/DTDs/PropertyList-1.0.dtd\">\n"
            "<plist version=\"1.0\">\n"
            "<dict>\n"
            "    <key>Label</key>\n"
            "    <string>com.%1.%2</string>\n"
            "    <key>ProgramArguments</key>\n"
            "    <array>\n"
            "        <string>%3</string>\n"
            "    </array>\n"
            "    <key>RunAtLoad</key>\n"
            "    <true/>\n"
            "    <key>KeepAlive</key>\n"
            "    <false/>\n"
            "</dict>\n"
            "</plist>\n"
        ).arg(QCoreApplication::organizationName().toLower(),
              QCoreApplication::applicationName().toLower(),
              appBundlePath);

        QFile file(plistPath);
        if (file.open(QIODevice::WriteOnly | QIODevice::Text)) {
            file.write(plistContent.toUtf8());
            file.close();
            QProcess::execute("launchctl", QStringList() << "load" << "-w" << plistPath);
        }
    } else {
        QFile file(plistPath);
        if (file.exists()) {
            QProcess::execute("launchctl", QStringList() << "unload" << "-w" << plistPath);
            file.remove();
        }
    }
#endif
}

bool AutoStartManager::isAutoStartEnabledForMac() const
{
#ifdef Q_OS_MACOS
    QString launchAgentPath = QStandardPaths::writableLocation(QStandardPaths::HomeLocation) 
                            + "/Library/LaunchAgents/";
    QString plistPath = launchAgentPath + "com." + QCoreApplication::organizationName().toLower() 
                       + "." + QCoreApplication::applicationName().toLower() + ".plist";
    return QFile::exists(plistPath);
#else
    return false;
#endif
}
