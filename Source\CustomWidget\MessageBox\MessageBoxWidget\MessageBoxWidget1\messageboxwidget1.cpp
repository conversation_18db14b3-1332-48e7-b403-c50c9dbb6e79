#include "globalfont.h"
#include "messageboxwidget1.h"
#include "ui_messageboxwidget1.h"


MessageBoxWidget1::MessageBoxWidget1(QWidget *parent)
    : QWidget(parent)
    , ui(new Ui::MessageBoxWidget1)
{
    ui->setupUi(this);
    QString style;
    style = "QLabel {"
            "   image: url(:/Icon/Trash.png);"
            "}";
    ui->Label1->setStyleSheet(style);
    style = "QLabel {"
            "   color: rgb(255, 255, 255);"
            "}";
    ui->Label2->setStyleSheet(style);
    ui->Label3->setStyleSheet(style);
    style = "QLabel {"
            "   color: rgb(207, 63, 65);"
            "}";
    ui->Label4->setStyleSheet(style);
    style = "QPushButton {"
            "   background-color: rgb(48, 48, 48);"
            "   color: rgb(255, 255, 255);"
            "   border: none;"
            "   border-radius: 8px;"
            "}";
    ui->PushButton1->setStyleSheet(style);
    style = "QPushButton {"
            "   background-color: rgb(229, 69, 71);"
            "   color: rgb(255, 255, 255);"
            "   border: none;"
            "   border-radius: 8px;"
            "}";
    ui->PushButton2->setStyleSheet(style);
    setLanguage("English");
}
MessageBoxWidget1::~MessageBoxWidget1()
{
    delete ui;
}


// override
void MessageBoxWidget1::resizeEvent(QResizeEvent* e)
{
    Q_UNUSED(e);
    mFont.setPointSize(GLBFHandle.getSuitablePointSize(mFont, height() / 12));
    QFont font(mFont);
    font.setPointSize(font.pointSize() - 2);
    ui->Label2->setFont(mFont);
    ui->Label3->setFont(mFont);
    ui->Label4->setFont(font);
    ui->PushButton1->setFont(mFont);
    ui->PushButton2->setFont(mFont);
}


// slot
void MessageBoxWidget1::on_PushButton1_clicked()
{
    emit attributeChanged("", "Cancel", "1");
}
void MessageBoxWidget1::on_PushButton2_clicked()
{
    emit attributeChanged("", "Ok", "1");
}


// setter & getter
MessageBoxWidget1& MessageBoxWidget1::setFont(QFont font)
{
    mFont = font;
    QResizeEvent e(size(), size());
    resizeEvent(&e);
    update();
    return *this;
}
MessageBoxWidget1& MessageBoxWidget1::setLanguage(QString language)
{
    if(language == "English")
    {
        ui->Label2->setText("Delete workspace");
        ui->Label4->setText("Once deleted, it cannot be recovered.");
        ui->PushButton1->setText("Cancel");
        ui->PushButton2->setText("Ok");
    }
    else if(language == "Chinese")
    {
        ui->Label2->setText("删除工作区");
        ui->Label4->setText("删除后将无法恢复");
        ui->PushButton1->setText("取消");
        ui->PushButton2->setText("确定");
    }
    return *this;
}
MessageBoxWidget1& MessageBoxWidget1::showItemText(QString text)
{
    ui->Label3->setText(text);
    return *this;
}

