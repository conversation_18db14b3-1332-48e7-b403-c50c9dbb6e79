#ifndef VOLUMEMETERS1M4_H
#define VOLUMEMETERS1M4_H


#include <QFont>
#include <QRect>
#include <QTimer>
#include <QWidget>
#include <QPainter>
#include <QMouseEvent>
#include <QPaintEvent>
#include <QResizeEvent>


class VolumeMeterS1M4 : public QWidget
{
    Q_OBJECT
public:
    explicit VolumeMeterS1M4(QWidget* parent=nullptr);
    ~VolumeMeterS1M4();
    void setFont(QFont font);
    void setColorBG(QColor color);
    void setValue(int value, int gain);
    void setValueOrigin(int value);
    void setValueGained(int value);
    void setMeterClear();
    void setMeterSlip();
    void setWidthRatio(int scale, int space1, int meterOrigin, int space2, int meterGained);
    void setHeightRatio(int clip, int space1, int volume, int space2);
    void setScaleHidden(bool hidden=true);
    void setScaleLineHidden(bool hidden=true);
    void setMeterOriginHidden(bool hidden=true);
    void setMeterGainedHidden(bool hidden=true);
protected:
    void resizeEvent(QResizeEvent* e) override;
    void paintEvent(QPaintEvent* e) override;
    void mouseDoubleClickEvent(QMouseEvent* e) override;
private:
    struct RectMeter
    {
        int volumeValue=-90;
        bool clipStatus=false;
        QRect clip;
        QRect volume;
        QTimer timerClip;
    };
    QTimer mTimerMeter;
    RectMeter mRectMeterOrigin;
    RectMeter mRectMeterGained;
    QRect mRectScale;
    QFont mFont;
    QColor mColorBG=QColor(22, 22, 22);
    int mScale=50;
    int mSpace1=6;
    int mMeterOrigin=20;
    int mSpace2=12;
    int mMeterGained=12;
    int mHClip=3;
    int mHSpace1=2;
    int mHVolume=93;
    int mHSpace2=2;
    bool mScaleHidden=false;
    bool mScaleLineHidden=false;
    bool mMeterOriginHidden=false;
    bool mMeterGainedHidden=false;
    void drawBG(QPainter* painter);
    void drawElement(QPainter* painter);
private slots:
    void in_timerClipOrigin_timeout();
    void in_timerClipGained_timeout();
    void in_mTimerMeter_timeout();
};


#endif // VOLUMEMETERS1M4_H

