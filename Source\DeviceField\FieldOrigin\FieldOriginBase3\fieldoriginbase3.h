#ifndef FIELDORIGINBASE3_H
#define FIELDORIGINBASE3_H


#include <QPair>
#include <QFont>
#include <QRect>
#include <QLabel>
#include <QColor>
#include <QWidget>
#include <QVector>
#include <QString>
#include <QPainter>
#include <QScrollArea>
#include <QPaintEvent>
#include <QPushButton>
#include <QResizeEvent>

#include "originbase.h"
#include "buttonboxs1m1.h"


class FieldOriginBase3 : public QWidget
{
    Q_OBJECT
public:
    explicit FieldOriginBase3(QWidget* parent=nullptr);
    ~FieldOriginBase3();
    FieldOriginBase3& modifyWidgetList(QVector<OriginBase*> list);
    FieldOriginBase3& setFont(QFont font);
    FieldOriginBase3& setVisibleList(QVector<QString> list);
    FieldOriginBase3& setFieldTitle(QString text);
    FieldOriginBase3& setFieldColor(QColor color);
    FieldOriginBase3& setWidgetAreaColor(QColor color);
    FieldOriginBase3& setWidgetAreaVisible(bool state=true);
    FieldOriginBase3& setFieldHeadAreaStretchFactor(float factor);
    FieldOriginBase3& setAdditionVisible(bool state=true);
    FieldOriginBase3& setAdditionButtonWeight(int weightWidth, int weightHeight);
protected:
    void resizeEvent(QResizeEvent* e) override;
    void paintEvent(QPaintEvent* e) override;
private:
    struct FieldWidget
    {
        bool visible;
        OriginBase* widget;
    };
    QFont mFont;
    float mHeadStretchFactor=5.3;
    QColor mColorBG=QColor(31, 31, 31);
    QColor mColorWidgetArea=QColor(128, 128, 128);
    QRect mRectHead;
    QRect mRectBody;
    QRect mRectWidgetArea;
    QLabel mLabelTitle;
    QScrollArea mScrollArea;
    int mScrollBarValue=0;
    QWidget mWidget;
    QVector<FieldWidget*> mWidgetList;
    ButtonBoxS1M1 mWidgetAddition;
    bool mWidgetAreaVisible=true;
    bool mAdditionVisible=true;
    void drawBG(QPainter* painter);
    void drawWidgetArea(QPainter* painter);
private slots:
    void in_mScrollArea_valueChanged(int value);
    void in_mWidgetListAll_attributeChanged(QString objectName, QString attribute, QString value);
    void in_mWidgetAddition_attributeChanged(QString objectName, QString attribute, QString value);
signals:
    void attributeChanged(QString objectName, QString attribute, QString value);
};


#endif // FIELDORIGINBASE3_H

