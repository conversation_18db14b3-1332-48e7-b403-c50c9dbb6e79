#include "singleinstancemanager.h"
#include "mainwindow_base.h"
#include <QApplication>
#include <QSharedMemory>
#include <QLocalServer>
#include <QLocalSocket>
#include <QSystemTrayIcon>
#include <QMenu>
#include <QStyle>
#include "appsettings.h"
#include "deviceconnector.h"
#include "globalfont.h"

SingleInstanceManager::SingleInstanceManager(QObject *parent) 
    : QObject(parent)
    , m_isFirstInstance(false)
    , m_trayIcon(nullptr)
    , m_trayMenu(nullptr)
{
    m_uniqueKey = QApplication::applicationName();
    m_sharedMemory = new QSharedMemory(m_uniqueKey, this);
}

SingleInstanceManager::~SingleInstanceManager()
{

}

void SingleInstanceManager::check()
{
    if (isAnotherInstanceRunning()) {
        activateExistingInstance();
        ::exit(0);
    }
}

bool SingleInstanceManager::isAnotherInstanceRunning()
{    
    bool isRunning = false;
    if (m_sharedMemory->attach()) {
        isRunning = true;
        m_sharedMemory->detach();
    } else {
        m_sharedMemory->create(1);
        m_isFirstInstance = true;
        initializeLocalServer();
        setupTrayIcon();
    }
    return isRunning;
}

void SingleInstanceManager::activateExistingInstance()
{
    QLocalSocket socket;
    socket.connectToServer(m_uniqueKey);
    if (socket.waitForConnected(1000)) {
        socket.write("ACTIVATE");
        socket.waitForBytesWritten();
    }
}

void SingleInstanceManager::setupTrayIcon()
{
    if (!QSystemTrayIcon::isSystemTrayAvailable()) {
        return;
    }

    m_trayIcon = new QSystemTrayIcon(this);
    connect(m_trayIcon, &QSystemTrayIcon::activated, this, [this](QSystemTrayIcon::ActivationReason reason){
        if(reason == QSystemTrayIcon::Trigger){
            showWindows();
        }
    });

    setTrayIcon(":/Icon/AppIcon.ico");
    
    createTrayMenu();
    m_trayIcon->setContextMenu(m_trayMenu);
    m_trayIcon->show();
}

void SingleInstanceManager::setTrayIcon(const QString& fileName)
{
    m_trayIcon->setIcon(QIcon(fileName));
}

void SingleInstanceManager::createTrayMenu()
{
    m_trayMenu = new QMenu();
    m_trayMenu->setWindowFlags(m_trayMenu->windowFlags()  | Qt::FramelessWindowHint | Qt::NoDropShadowWindowHint);
    m_trayMenu->setAttribute(Qt::WA_TranslucentBackground);
    m_trayMenu->setFont(GLBFHandle.font());
    m_trayMenu->setStyleSheet(R"(
        QMenu {
            background-color: #1e1e1e;
            color: #ffffff;
            border: 1px solid #444;
            border-radius: 5px;
            padding: 12px;
            font-size: 12pt;
        }
        QMenu::item {
            padding: 5px 10px 5px 10px;
        }
        QMenu::item:selected {
            background-color: #3a3a3a;
            border-left: 2px solid #4CAF50;
        }
        QMenu::separator {
            height: 1px;
            background-color: #444;
            margin: 5px 0;
        }
    )");

    
    QAction *showAction = new QAction("显示", this);
    connect(showAction, &QAction::triggered, this, &SingleInstanceManager::showWindows);
    
    QAction *quitAction = new QAction("退出", this);
    connect(quitAction, &QAction::triggered, qApp, &QApplication::quit);
    
    connect(&APPSHandle, &AppSettingsSubject::attributeChanged, this,[this,showAction, quitAction](QString objectName, QString attribute, QString value){
        if(attribute == "ModifyLanguage")
        {
            if(value == "English")
            {
                showAction->setText("Show");
                quitAction->setText("Quit");
            }
            else if(value == "Chinese")
            {
                showAction->setText("显示");
                quitAction->setText("退出");
            }
        }
    });
    m_trayMenu->addAction(showAction);
    m_trayMenu->addSeparator();
    m_trayMenu->addAction(quitAction);
}

void SingleInstanceManager::showWindows()
{
    auto findWidget = [](auto* dummy = nullptr) -> decltype(dummy) {
        for (auto widget : QApplication::topLevelWidgets()) {
            if (auto w = qobject_cast<decltype(dummy)>(widget)) {
                return w;
            }
        }
        return nullptr;
    };

    QWidget* w = findWidget(static_cast<MainWindow_Base*>(nullptr));

    if (!w) {
        w = findWidget(static_cast<DeviceConnector*>(nullptr));
    }
    if(!w)  return;
    w->showNormal();
    w->raise();
    w->activateWindow();
}

void SingleInstanceManager::handleNewConnection()
{
    QLocalSocket *socket = m_localServer->nextPendingConnection();
    if (socket) {
        connect(socket, &QLocalSocket::readyRead, [this, socket]() {
            if (socket->bytesAvailable()) {
                QByteArray data = socket->readAll();
                if(data == "ACTIVATE") {
                    showWindows();
                }
            }
        });
        connect(socket, &QLocalSocket::disconnected, socket, &QLocalSocket::deleteLater);
    }
}

void SingleInstanceManager::initializeLocalServer()
{
    m_localServer = new QLocalServer(this);
    if (m_localServer->isListening()) {
        m_localServer->close();
    }
    
    connect(m_localServer, &QLocalServer::newConnection, 
            this, &SingleInstanceManager::handleNewConnection);
            
    if (!m_localServer->listen(m_uniqueKey)) {
        qWarning() << "无法启动本地服务器:" << m_localServer->errorString();
    }
}
