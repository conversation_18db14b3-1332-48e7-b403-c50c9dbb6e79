#include "globalfont.h"
#include "comboboxs1m2.h"
#include <QAbstractItemView>

ComboBoxS1M2::ComboBoxS1M2(QComboBox* parent)
    : QComboBox(parent)
{
#ifdef Q_OS_MACOS
    setStyle(new MacComboBoxStyle(style()));
#endif
    connect(this, SIGNAL(currentTextChanged(QString)), this, SLOT(in_mComboBox_currentTextChanged(QString)), Qt::UniqueConnection);
}
ComboBoxS1M2::~ComboBoxS1M2()
{

}


// override
void ComboBoxS1M2::resizeEvent(QResizeEvent* e)
{
    Q_UNUSED(e);
    QString style;
    style = QString("QComboBox {"
                    "   border: none;"
                    "   color: rgb(216,216,216);"
                    "   background-color: transparent;"
                    "}"
                    "QComboBox:hover {"
                    "   color: rgb(216,216,216);"
                    "   border: 2px solid rgb(172,172,172);"
                    "   border-radius: 5px;"
                    "}"
                    "QComboBox::drop-down {"
                    "   border: none;"
                    "}"
                    "QComboBox::down-arrow {"
                    "   image: url(:/Icon/ArrowDown.png);"
                    "   width: %1px;"
                    "   height: %1px;"
                    "   padding-right: %2px;"
                    "}"
                    "QComboBox QAbstractItemView {"
                    "   outline: 0px;"
                    "   color: rgb(216,216,216);"
                    "   background-color: #2e2e2e;"
                    "}"
                    "QComboBox QAbstractItemView::item {"
                    "   height: %3px;"
                    "}"
                    "QComboBox QAbstractItemView::item:hover {"
                    "   color: rgb(216,216,216);"
                    "   background-color: rgb(66,66,66);"
                    "}"
                    "QComboBox QAbstractItemView::item:selected {"
                    "   color: rgb(216,216,216);"
                    "   background-color: rgb(66,66,66);"
                    "}"
                    "QComboBox QScrollBar:vertical {"
                    "   background-color: #2e2e2e;"
                    "   border-radius: 0px;"
                    "   padding-top: 3px;"
                    "   padding-bottom: 3px;"
                    "   width: %4px;"
                    "}"
                    "QComboBox QScrollBar::handle:vertical {"
                    "   width: %4px;"
                    "   border-radius: 3px;"
                    "   background-color: rgb(112,112,112);"
                    "}"
                    "QComboBox QScrollBar::handle:vertical:hover {"
                    "   background-color: rgb(112,112,112);"
                    "}"
                    "QComboBox QScrollBar::add-line:vertical {"
                    "   border: none;"
                    "}"
                    "QComboBox QScrollBar::sub-line:vertical {"
                    "   border: none;"
                    "}"
                    "QComboBox QScrollBar::add-page:vertical {"
                    "   background: none;"
                    "}"
                    "QComboBox QScrollBar::sub-page:vertical {"
                    "   background: none;"
                    "}").arg(size().height() / 2).arg(size().height() / 3).arg(size().height()).arg(size().height() / 3);
    setStyleSheet(style);
    mFont.setPointSize(GLBFHandle.getSuitablePointSize(mFont, height()));
    QComboBox::setFont(mFont);
}
void ComboBoxS1M2::wheelEvent(QWheelEvent* e)
{
    e->ignore();
}
void ComboBoxS1M2::hidePopup()
{
    QComboBox::hidePopup();
#ifdef Q_OS_MACOS
    setAttribute(Qt::WA_UnderMouse, false);
#endif
}
void ComboBoxS1M2::showPopup()
{
    QComboBox::showPopup();
#ifdef Q_OS_MACOS
    setAttribute(Qt::WA_UnderMouse, true);
#endif
}

// slot
void ComboBoxS1M2::in_mComboBox_currentTextChanged(QString text)
{
    if(mInitOK)
    {
        emit attributeChanged(objectName(), "ItemChanged", text);
    }
}


// setter & getter
ComboBoxS1M2& ComboBoxS1M2::setFont(QFont font)
{
    mFont = font;
    QResizeEvent e(size(), size());
    resizeEvent(&e);
    update();
    return *this;
}
ComboBoxS1M2& ComboBoxS1M2::setName(QString name)
{
    setObjectName(name);
    return *this;
}
ComboBoxS1M2& ComboBoxS1M2::modifyItemList(QVector<QString> list)
{
    mInitOK = false;
    clear();
    addItems(list);
    return *this;
}
ComboBoxS1M2& ComboBoxS1M2::modifyItemList(QVector<QString> list, QString defaultItem)
{
    mInitOK = false;
    clear();
    addItems(list);
    setDefaultItem(defaultItem);
    return *this;
}
ComboBoxS1M2& ComboBoxS1M2::setDefaultItem(QString item)
{
    if(findText(item) != -1 && !mInitOK)
    {
        setCurrentText(item);
        mInitOK = true;
    }
    return *this;
}

