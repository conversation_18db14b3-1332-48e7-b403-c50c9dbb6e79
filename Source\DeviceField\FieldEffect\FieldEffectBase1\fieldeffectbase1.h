#ifndef FIELDEFFECTBASE1_H
#define FIELDEFFECTBASE1_H


#include <QPair>
#include <QFont>
#include <QRect>
#include <QLabel>
#include <QColor>
#include <QWidget>
#include <QVector>
#include <QString>
#include <QPainter>
#include <QScrollArea>
#include <QPaintEvent>
#include <QPushButton>
#include <QResizeEvent>

#include "effectbase.h"
#include "buttonboxs1m1.h"


class FieldEffectBase1 : public QWidget
{
    Q_OBJECT
public:
    explicit FieldEffectBase1(QWidget* parent=nullptr);
    ~FieldEffectBase1();
    FieldEffectBase1& modifyWidgetList(QVector<EffectBase*> list);
    FieldEffectBase1& setFont(QFont font);
    FieldEffectBase1& setVisibleList(QVector<QString> list);
    FieldEffectBase1& setFieldTitle(QString text);
    FieldEffectBase1& setFieldColor(QColor color);
    FieldEffectBase1& setWidgetAreaColor(QColor color);
    FieldEffectBase1& setWidgetAreaVisible(bool state=true);
    FieldEffectBase1& setFieldHeadAreaStretchFactor(float factor);
    FieldEffectBase1& setAdditionVisible(bool state=true);
    FieldEffectBase1& setAdditionButtonWeight(int weightWidth, int weightHeight);
protected:
    void resizeEvent(QResizeEvent* e) override;
    void paintEvent(QPaintEvent* e) override;
private:
    struct FieldWidget
    {
        bool visible;
        EffectBase* widget;
    };
    QFont mFont;
    float mHeadStretchFactor=5.3;
    QColor mColorBG=QColor(31, 31, 31);
    QColor mColorWidgetArea=QColor(128, 128, 128);
    QRect mRectHead;
    QRect mRectBody;
    QRect mRectWidgetArea;
    QLabel mLabelTitle;
    QScrollArea mScrollArea;
    int mScrollBarValue=0;
    QWidget mWidget;
    QVector<FieldWidget*> mWidgetList;
    ButtonBoxS1M1 mWidgetAddition;
    bool mWidgetAreaVisible=true;
    bool mAdditionVisible=true;
    void drawBG(QPainter* painter);
    void drawWidgetArea(QPainter* painter);
private slots:
    void in_mScrollArea_valueChanged(int value);
    void in_mWidgetListAll_attributeChanged(QString objectName, QString attribute, QString value);
    void in_mWidgetAddition_attributeChanged(QString objectName, QString attribute, QString value);
signals:
    void attributeChanged(QString objectName, QString attribute, QString value);
};


#endif // FIELDEFFECTBASE1_H

