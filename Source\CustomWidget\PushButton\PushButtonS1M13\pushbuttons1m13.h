#ifndef PUSHBUTTONS1M13_H
#define PUSHBUTTONS1M13_H


#include <QWidget>
#include <QPushButton>
#include <QResizeEvent>


class PushButtonS1M13 : public QWidget
{
    Q_OBJECT
public:
    explicit PushButtonS1M13(QWidget* parent=nullptr);
    ~PushButtonS1M13();
    enum ButtonID
    {
        buttonSOLOLeft=0,
        buttonSOLORight,
        buttonMUTELeft,
        buttonMUTERight,
        buttonANTILeft,
        buttonANTIRight
    };
    PushButtonS1M13& setFont(QFont font);
    PushButtonS1M13& setPushButtonWeightWidth(int weight);
    PushButtonS1M13& setPushButtonStateSOLOLeft(bool state);
    PushButtonS1M13& setPushButtonStateSOLORight(bool state);
    PushButtonS1M13& setPushButtonStateMUTELeft(bool state);
    PushButtonS1M13& setPushButtonStateMUTERight(bool state);
    PushButtonS1M13& setPushButtonStateANTILeft(bool state);
    PushButtonS1M13& setPushButtonStateANTIRight(bool state);
    PushButtonS1M13& setPushButtonClickedSOLOLeft(bool state);
    PushButtonS1M13& setPushButtonClickedSOLORight(bool state);
    PushButtonS1M13& setPushButtonClickedMUTELeft(bool state);
    PushButtonS1M13& setPushButtonClickedMUTERight(bool state);
    PushButtonS1M13& setPushButtonClickedANTILeft(bool state);
    PushButtonS1M13& setPushButtonClickedANTIRight(bool state);
    bool getPushButtonStateSOLOLeft();
    bool getPushButtonStateSOLORight();
    bool getPushButtonStateMUTELeft();
    bool getPushButtonStateMUTERight();
    bool getPushButtonStateANTILeft();
    bool getPushButtonStateANTIRight();
protected:
    void resizeEvent(QResizeEvent* e) override;
private:
    QFont mFont;
    bool mPushButtonStateSOLOLeft=false;
    bool mPushButtonStateSOLORight=false;
    bool mPushButtonStateMUTELeft=false;
    bool mPushButtonStateMUTERight=false;
    bool mPushButtonStateANTILeft=false;
    bool mPushButtonStateANTIRight=false;
    QPushButton mPushButtonSOLOLeft;
    QPushButton mPushButtonSOLORight;
    QPushButton mPushButtonMUTELeft;
    QPushButton mPushButtonMUTERight;
    QPushButton mPushButtonANTILeft;
    QPushButton mPushButtonANTIRight;
    int mWeightWidth=40;
    int mRadius=0;
private slots:
    void in_mPushButtonSOLOLeft_clicked();
    void in_mPushButtonSOLORight_clicked();
    void in_mPushButtonMUTELeft_clicked();
    void in_mPushButtonMUTERight_clicked();
    void in_mPushButtonANTILeft_clicked();
    void in_mPushButtonANTIRight_clicked();
signals:
    void buttonStateChanged(ButtonID button, bool state);
};


#endif // PUSHBUTTONS1M13_H

