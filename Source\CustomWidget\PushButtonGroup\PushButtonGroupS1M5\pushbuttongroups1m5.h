#ifndef PUSHBUTTONGROUPS1M5_H
#define PUSHBUTTONGROUPS1M5_H


#include <QHash>
#include <QWidget>
#include <QResizeEvent>


namespace Ui {
class PushButtonGroupS1M5;
}


class PushButtonGroupS1M5 : public QWidget
{
    Q_OBJECT
public:
    explicit PushButtonGroupS1M5(QWidget* parent=nullptr);
    ~PushButtonGroupS1M5();
    PushButtonGroupS1M5& setFont(QFont font);
    PushButtonGroupS1M5& setLanguage(QString language);
    PushButtonGroupS1M5& setState(QString button, QString state, bool needEmit=true);
    QString getState(QString button);
protected:
    void resizeEvent(QResizeEvent* e) override;
    void showEvent(QShowEvent*) override { resizeEvent(nullptr); }
private:
    Ui::PushButtonGroupS1M5* ui;
    QFont mFont;
    unsigned int mBitmap=0;
    QHash<unsigned int, QString> mStyle;
private slots:
    void on_PushButton48V_clicked(bool checked);
    void on_PushButtonAUTO_clicked(bool checked);
signals:
    void stateChanged(QString button, QString state);
};


#endif // PUSHBUTTONGROUPS1M5_H

