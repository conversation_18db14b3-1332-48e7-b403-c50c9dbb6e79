#ifndef EFFECTS1M1_H
#define EFFECTS1M1_H


#include <QFont>
#include <QEvent>
#include <QTimer>
#include <QObject>
#include <QWidget>
#include <QResizeEvent>

#include "effectbase.h"
#include "workspace.h"
#include "appsettings.h"
#include "pushbuttons1m4.h"
#include "pushbuttons1m7.h"


namespace Ui {
class EffectS1M1;
}


class EffectS1M1 : public EffectBase, public WorkspaceObserver, public AppSettingsObserver
{
    Q_OBJECT
public:
    explicit EffectS1M1(QWidget* parent=nullptr, QString name="");
    ~EffectS1M1();
    EffectS1M1& setName(QString name);
    EffectS1M1& setFont(QFont font);
    EffectS1M1& setVolumeMeter(int value);
    EffectS1M1& setVolumeMeterClear();
    EffectS1M1& setVolumeMeterSlip();
    EffectS1M1& setGain(float value);
    EffectS1M1& setGainLock(bool state=true);
    EffectS1M1& setMuteAffectGain(bool state=true);
    EffectS1M1& setGainAffectMute(bool state=true);
    EffectS1M1& setGainRange(float min, float max);
    EffectS1M1& setGainDefault(float value);
    EffectS1M1& setGainWidgetDisable(float value);
    EffectS1M1& setChannelNameEditable(bool state=true);
    EffectS1M1& setValueNCChannel(QString channel);
    EffectS1M1& setValueNCType(QString type);
    EffectS1M1& setValueGAIN(float value);
    EffectS1M1& setValueMUTE(bool state=true);
protected:
    bool eventFilter(QObject* obj, QEvent* e) override;
    void resizeEvent(QResizeEvent* e) override;
    void updateAttribute() override;
    void loadSettings() override;
    void AppSettingsChanged(QString objectName, QString attribute, QString value) override;
private:
    Ui::EffectS1M1* ui;
    QTimer mTimer;
    QFont mFont;
    QString mPreNCChannel="";
    QString mPreNCType="";
    int mPreMUTE=-2147483648;
    float mPreGAIN=-2147483648;
    float mDisableGAIN=-88;
    bool mMuteAffectGain=false;
    bool mGainAffectMute=false;
    void save(QAnyStringView key, const QVariant& value);
private slots:
    void in_mTimer_timeout();
    void in_widgetPushButtonGroup1_buttonStateChanged(PushButtonS1M4::ButtonID button, bool state);
    void in_widgetDial_valueChanged(float value);
    void in_widgetPushButtonGroup2_buttonStateChanged(PushButtonS1M7::ButtonID button, bool state);
    void on_lineEdit_textChanged(const QString& arg1);
    void on_lineEdit_editingFinished();
    void on_pushButtonClose_clicked();
};


#endif // EFFECTS1M1_H

