#ifndef ORIGINBASE_H
#define ORIGINBASE_H


#include <QWidget>


class OriginBase : public QWidget
{
    Q_OBJECT
public:
    explicit OriginBase(QWidget* parent=nullptr) : QWidget(parent) { }
    virtual ~OriginBase() = default;
    OriginBase& setChannelName(QString name);
    OriginBase& setWidgetEnable(bool state=true);
    OriginBase& setWidgetEnableWithUpdate(bool state=true);
    OriginBase& setWidgetMovable(bool state=true);
    OriginBase& setWidgetReady(bool state=true);
    OriginBase& setWidgetEmitAction(bool state=true);
    QString getChannelName() { return mChannelName; }
    bool isWidgetEnable() { return mEnable; }
    bool isWidgetMovable() { return mMovable; }
    bool isWidgetReady() { return mReady; }
    bool isWidgetEmitAction() { return mEmitAction; }
protected:
    virtual void updateAttribute() = 0;
private:
    QString mChannelName="";
    bool mEnable=false;
    bool mMovable=false;
    bool mReady=false;
    bool mEmitAction=false;
signals:
    void attributeChanged(QString objectName, QString attribute, QString value);
};


#endif // ORIGINBASE_H

