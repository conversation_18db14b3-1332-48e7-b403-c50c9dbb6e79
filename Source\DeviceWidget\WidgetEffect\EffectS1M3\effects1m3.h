#ifndef EffectS1M3_H
#define EffectS1M3_H


#include <QFont>
#include "effectbase.h"
#include "workspace.h"
#include "appsettings.h"


namespace Ui {
class EffectS1M3;
}


class EffectS1M3 : public EffectBase, public WorkspaceObserver, public AppSettingsObserver
{
    Q_OBJECT
    
public:
    explicit EffectS1M3(QWidget* parent=nullptr, QString name="");
    ~EffectS1M3();
    EffectS1M3& setName(QString name);
    EffectS1M3& setFont(QFont font);
    void setNCBypassIcon(bool enabled);
    void setNCIcon(const QString& text);
    QString getValueNoiseReductionKey(QString type);
    int getValueNoiseReduction(QString type);
    EffectS1M3& setValueNoiseReduction(int value);
    EffectS1M3& setValueNoiseReductionRange(float min, float max);
    EffectS1M3& setValueNoiseReductionType(QString type);
    EffectS1M3& updateNoiseReductionType(QString type);
    EffectS1M3& setNoiseReductionTitle(const QString& title);

protected:
    void resizeEvent(QResizeEvent* e) override;
    void updateStyle();
    void updateAttribute() override;
    void loadSettings() override;
    void AppSettingsChanged(QString objectName, QString attribute, QString value) override;
    void save(const QString& key, const QVariant& value);

private:
    Ui::EffectS1M3* ui;
    QFont mFont;
    QString mNoiseReductionTypePre;
    QString mNoiseReductionType;
    int mNoiseReductionNC1=-2147483648;
    int mNoiseReductionNC2=-2147483648;
    int mNoiseReductionBypass=-2147483648;
};


#endif // EffectS1M3_H

