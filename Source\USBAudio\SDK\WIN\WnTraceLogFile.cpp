/************************************************************************
 *
 *  Module:       WnTraceLogFile.cpp
 *
 *  Description:  LogFile wrapper
 *
 *  Runtime Env.: Win32
 *
 *  Author(s):
 *    <PERSON>, <PERSON><PERSON>@thesycon.de
 *    <PERSON><PERSON>,  Udo.E<PERSON><EMAIL>
 *
 *  Companies:
 *    Thesycon GmbH, Germany      http://www.thesycon.de
 *
 ************************************************************************/

#include "libwn_min_global.h"

// Module is empty if .h file was not included (category turned off).
#ifdef __WnTraceLogFile_h__

#if !defined(UNDER_CE)
// file sharing modes for sopen
#include <share.h>
#endif

#include "WnStringUtils_impl.h"

// optionally put everything into a namespace
#ifdef LIBWN_NAMESPACE
namespace LIBWN_NAMESPACE {
#endif

WnTraceLogFile::WnTraceLogFile()
{
    mFile = NULL;
    TB_ZERO_ARRAY(mFileName);
    TB_ZERO_ARRAY(mFileNameBackupExt);

    mCurrentFileSize = 0;
    mMaxFileSize = cDefaultLogFileSizeLimit;
    mCRDetected = false;

    // default extension for backup file
    TbStringNCopyToArray(mFileNameBackupExt, TEXT(".bak"));

    // default settings:
    mAppendToExistingFile = true;
    mFlushAfterEachWrite = true;
}


WnTraceLogFile::~WnTraceLogFile()
{
    CloseFile();
}



void
WnTraceLogFile::CloseFile()
{
    if ( mFile != NULL ) {
        fclose(mFile);
        mFile = NULL;
    }
}


bool
WnTraceLogFile::OpenFile(
    const TCHAR* fileName,
    bool appendToExistingFile,
    bool insertPidIntoFileName
    )
{
#ifdef UNDER_CE
    TB_UNUSED_PARAM(insertPidIntoFileName)
#else
    if ( insertPidIntoFileName ) {
        // current process ID
        DWORD procId = ::GetCurrentProcessId();
        TCHAR procIdStr[16];
        WnStringPrintf_impl(procIdStr,TB_ARRAY_ELEMENTS(procIdStr), TEXT("%u"), procId);

        TCHAR drive[_MAX_DRIVE] = {0};
        TCHAR path[_MAX_DIR] = {0};
        TCHAR fname[_MAX_FNAME] = {0};
        TCHAR ext[_MAX_EXT] = {0};

        // split filename in components
        _tsplitpath_s(
            fileName, // in
            drive,
            path,
            fname,
            ext
            );

        // append PID to base name
        TbStringNCatToArray(fname,procIdStr);

        // make new path
        errno_t err = _tmakepath_s(
                              mFileName,
                              drive,
                              path,
                              fname,
                              ext
                              );
        if ( err != 0 ) {
            WnDbgOutput(__FUNCTION__": ERROR: _tmakepath_s failed, err=%d\n", err);
            return false;
        }
    } else {
#endif
        // save file name
        TbStringNCopyToArray(mFileName, fileName);
#ifndef UNDER_CE
    }
#endif

    // init
    mAppendToExistingFile = appendToExistingFile;
    mCRDetected = false;

    if ( mAppendToExistingFile ) {
        // append mode: keep existing files, if any
    } else {
        // rename any existing log file to bak
        CreateBackupFile();
    }

    // open file
    return OpenLogFile();

} //OpenFile



// called with lock held
bool
WnTraceLogFile::WriteToFileImpl(
    const char* ptr,
    unsigned int byteCount
    )
{
    if ( !IsOpen() ) {
        WnDbgOutput(__FUNCTION__": ERROR: file not open\n" );
        return false;
    }

    // Note: fwrite is thread-safe
    size_t n = fwrite(ptr, 1, byteCount, mFile);
    if ( n != byteCount ) {
        // failed
        WnDbgOutput(__FUNCTION__": ERROR: fwrite failed, err=0x%08X\n", ::GetLastError());
        return false;
    }

    if ( mFlushAfterEachWrite ) {
        fflush(mFile);
    }

    // Note: we opened the file in binary mode, so CRT does not perform a LF to CR/LF translation.
    // So we can maintain our own file size.
    mCurrentFileSize += byteCount;

    if ( mMaxFileSize != 0 ) {
        // check size limit
        if ( mCurrentFileSize >= mMaxFileSize ) {
            // close current file
            CloseFile();
            // create backup file
            CreateBackupFile();
            // re-open
            OpenLogFile();
        }
    } else {
        // no size limit
    }

    return true;

}//WriteToFileImpl


bool
WnTraceLogFile::WriteToFile(
    const char* ptr,
    unsigned int byteCount
    )
{
    return WriteToFileImpl(ptr, byteCount);
}

bool
WnTraceLogFile::TranslateLFandWriteToFile(
    const char* ptr,
    unsigned int byteCount
    )
{
    bool result = true;

    // temp buffer
    // worst case: every byte needs translation
    char buf[cTranslateTempBufferSize*2];

    do {

        // bytes to process
        unsigned int n = TB_MIN(byteCount, cTranslateTempBufferSize);
        byteCount -= n;
        char* dest = buf;

        while ( 0 != n-- ) {
            char c = *ptr++;
            if ( '\r' == c ) {
                mCRDetected = true;
            } else {
                if ( '\n' == c && !mCRDetected ) {
                    *dest++ = '\r';
                }
                mCRDetected = false;
            }
            *dest++ = c;
        }

        // number of bytes in buf
        unsigned int cnt = (unsigned int)(dest - buf);
        if ( cnt > 0 ) {
            bool succ = WriteToFileImpl(buf,cnt);
            if ( !succ ) {
                result = false;
            }
        }

    } while ( byteCount > 0 );

    return result;

} //TranslateLFandWriteToFile



void
WnTraceLogFile::CreateBackupFile()
{
    // backup file name
    TCHAR bakname[MAX_PATH];
    WnStringPrintf_impl(bakname, TB_ARRAY_ELEMENTS(bakname), TEXT("%s%s"), mFileName, mFileNameBackupExt);

    // reset attributes to remove read-only attribute
    // and delete backup file, if any exists,
    // ignore errors that may happen if the bak file does not exist
    ::SetFileAttributes(bakname, FILE_ATTRIBUTE_NORMAL);
    ::DeleteFile(bakname);

    // rename our current file
    // ignore errors that may happen if the log file does not exist
    ::MoveFile(mFileName, bakname);

} //CreateBackupFile



bool
WnTraceLogFile::OpenLogFile()
{
    // open mode
    const TCHAR* mode = mAppendToExistingFile ? TEXT("ab") : TEXT("wb");

    // try to open our log file
    // note: we use shared write mode
#if defined(UNDER_CE)
    mFile = _tfopen(mFileName, mode);
#else
    mFile = _tfsopen(mFileName, mode, _SH_DENYNO);
#endif
    if ( NULL == mFile ) {
        // failed
        #ifdef  UNICODE
            WnDbgOutput(__FUNCTION__": ERROR: open file '%S' failed, err=0x%08X\n", mFileName, ::GetLastError());
        #else
            WnDbgOutput(__FUNCTION__": ERROR: open file '%s' failed, err=0x%08X\n", mFileName, ::GetLastError());
        #endif
        return false;
    }

    // determine current file size, ignore error
    UpdateCurrentFileSize();

    return true;

} //OpenLogFile


bool
WnTraceLogFile::UpdateCurrentFileSize()
{
    // reset
    mCurrentFileSize = 0;

    //note: we can seek here, the next write access to the file
    // will reposition the file pointer to end of file.

    int ret = fseek(mFile, 0, SEEK_END);
    if ( ret != 0 ) {
        // failed
        WnDbgOutput(__FUNCTION__": ERROR: fseek failed, err=0x%08X\n", ::GetLastError());
        return false;
    }

    long pos = ftell(mFile);
    if ( pos < 0 ) {
        // failed
        WnDbgOutput(__FUNCTION__": ERROR: ftell failed, err=0x%08X\n", ::GetLastError());
        return false;
    }

    mCurrentFileSize = (unsigned int)pos;

    return true;

} //UpdateCurrentFileSize


void
WnTraceLogFile::SetLogFileSizeLimit(
    unsigned int maxSizeInBytes
    )
{
    if ( maxSizeInBytes > cMaxLogFileSizeLimit ) {
        maxSizeInBytes = cMaxLogFileSizeLimit;
    }
    mMaxFileSize = maxSizeInBytes;
}


void
WnTraceLogFile::SetFlushAfterEachWrite(
    bool enable
    )
{
    mFlushAfterEachWrite = enable;
}

#ifdef LIBWN_NAMESPACE
}
#endif

#endif

/*************************** EOF **************************************/
