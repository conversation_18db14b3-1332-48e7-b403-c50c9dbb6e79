#include "origins1m1.h"
#include "globalfont.h"
#include "ui_origins1m1.h"


OriginS1M1::OriginS1M1(QWidget* parent, QString name)
    : OriginBase(parent)
    , WorkspaceObserver(name)
    , AppSettingsObserver(name)
    , ui(new Ui::OriginS1M1)
{
    ui->setupUi(this);
    installEventFilter(this);
    resize(minimumWidth(), minimumHeight());
    QString style;
    style = "QFrame {"
            "   background-color: #161616;"
            "   border-radius: 8px;"
            "}";
    ui->frame->setStyleSheet(style);
    style = "QLineEdit {"
            "   color: rgb(161, 161, 161);"
            "   background-color: rgb(46, 46, 46);"
            "   border-radius: 5px;"
            "   selection-color: rgb(0, 121, 107);"
            "   selection-background-color: rgb(224, 247, 250);"
            "}";
    ui->lineEdit->setStyleSheet(style);
    ui->lineEdit->setAttribute(Qt::WA_Hover);
    ui->lineEdit->installEventFilter(this);
    style = "QPushButton {"
            "   background-color: transparent;"
            "   image: url(:/Icon/WidgetCloseBlack.png);"
            "}"
            "QPushButton:hover {"
            "   border: 2px solid rgb(46, 46, 46);"
            "   border-radius: 3px;"
            "}";
    ui->pushButtonClose->setStyleSheet(style);
    ui->pushButtonClose->setParent(this);
    ui->pushButtonClose->setFocusPolicy(Qt::NoFocus);
    ui->pushButtonClose->hide();
    ui->pushButtonClose->setAttribute(Qt::WA_Hover);
    ui->pushButtonClose->installEventFilter(this);
    ui->widgetVolumeMeter->setWidthRatio(18, 30, 10, 36);
    ui->widgetVSlider->setRange(0, 88).setDefault(0).showInfinitesimal(false);
    mTimer.setSingleShot(true);
    mTimer.setInterval(100);
    connect(&mTimer, SIGNAL(timeout()), this, SLOT(in_mTimer_timeout()), Qt::UniqueConnection);
    connect(ui->widgetVSlider, SIGNAL(valueChanged(int)), this, SLOT(in_widgetVSlider_valueChanged(int)), Qt::UniqueConnection);
    connect(ui->widgetPushButtonGroup1, SIGNAL(buttonStateChanged(PushButtonS1M8::ButtonID, bool)), this, SLOT(in_widgetPushButtonGroup1_buttonStateChanged(PushButtonS1M8::ButtonID, bool)), Qt::UniqueConnection);
}
OriginS1M1::~OriginS1M1()
{
    delete ui;
}


// override
bool OriginS1M1::eventFilter(QObject* obj, QEvent* e)
{
    if((obj == ui->lineEdit || obj == ui->pushButtonClose) && isWidgetEnable() && isWidgetMovable())
    {
        if(e->type() == QEvent::HoverEnter)
        {
            if(ui->pushButtonClose->isHidden())
            {
                mTimer.start();
            }
            return true;
        }
        else if(e->type() == QEvent::HoverLeave)
        {
            QTimer::singleShot(0, [this](){
                if(!ui->lineEdit->underMouse() && !ui->pushButtonClose->underMouse())
                {
                    mTimer.stop();
                    ui->pushButtonClose->hide();
                }
            });
        }
        if(obj == ui->lineEdit && e->type() == QEvent::MouseButtonPress && ui->lineEdit->isEnabled())
        {
            mTimer.stop();
            ui->pushButtonClose->hide();
        }
    }
    else if(obj == this && isWidgetEnable() && isWidgetMovable())
    {
        if(e->type() == QEvent::Move)
        {
            QTimer::singleShot(0, [this](){
                if(ui->lineEdit->geometry().contains(ui->frame->mapFromGlobal(QCursor::pos())))
                {
                    mTimer.start();
                }
            });
        }
    }
    return QWidget::eventFilter(obj, e);
}
void OriginS1M1::resizeEvent(QResizeEvent* e)
{
    Q_UNUSED(e);
    // W
    float wPixelPerRatio=size().width() / 100.0;
    int wSpace1=wPixelPerRatio * 19;
    int wMeter=wPixelPerRatio * 32;
    int wSpace2=wPixelPerRatio * 10;
    int wVSlider=wPixelPerRatio * 20;
    int xMeter=wSpace1;
    int xVSlider=wSpace1 + wMeter + wSpace2;
    int xButtonGroup1=wPixelPerRatio * 10;
    // H
    float hPixelPerRatio = size().height() / 100.0;
    int hLineEdit=hPixelPerRatio * 8;
    int hSpace1=hPixelPerRatio * 2;
    int hMeter=hPixelPerRatio * 60;
    int hVSlider=hPixelPerRatio * 56;
    int hSpace2=hPixelPerRatio * 1;
    int hButtonGroup1=hPixelPerRatio * 50;
    int hPushButtonClose=hLineEdit / 100.0 * 50;
    ui->lineEdit->setGeometry(0, 0, size().width(), hLineEdit);
    ui->pushButtonClose->setGeometry(size().width() - hPushButtonClose * 1.3, (hLineEdit - hPushButtonClose) / 2, hPushButtonClose, hPushButtonClose);
    ui->widgetVolumeMeter->setGeometry(xMeter, hLineEdit + hSpace1, wMeter, hMeter);
    ui->widgetVSlider->setGeometry(xVSlider, hLineEdit + hSpace1 + (hMeter - hVSlider) * 0.7, wVSlider, hVSlider);
    ui->widgetPushButtonGroup1->setGeometry(xButtonGroup1, hLineEdit + hSpace1 + hMeter + hSpace2, size().width() - xButtonGroup1 * 1.2, hButtonGroup1);
    mFont.setPointSize(GLBFHandle.getSuitablePointSize(mFont, ui->lineEdit->height()) - 3);
    if(mFont.pointSize() < 8)
    {
        mFont.setPointSize(mFont.pointSize());
    }
    else if(mFont.pointSize() < 12)
    {
        mFont.setPointSize(mFont.pointSize() - 1);
    }
    else if(mFont.pointSize() < 17)
    {
        mFont.setPointSize(mFont.pointSize() - 2);
    }
    else if(mFont.pointSize() < 22)
    {
        mFont.setPointSize(mFont.pointSize() - 3);
    }
    mFont.setPointSize(mFont.pointSize() - 1);
    ui->lineEdit->setFont(mFont);
    int radius=size().width() * 0.04;
    QString style;
    style = QString("QFrame {"
                    "   background-color: #161616;"
                    "   border-radius: %1px;"
                    "}").arg(radius);
    ui->frame->setStyleSheet(style);
    style = QString("QLineEdit {"
                    "   color: rgb(161, 161, 161);"
                    "   background-color: rgb(46, 46, 46);"
                    "   border-top-left-radius: %1px; border-top-right-radius: %1px;"
                    "   selection-color: rgb(0, 121, 107);"
                    "   selection-background-color: rgb(224, 247, 250);"
                    "}").arg(radius);
    ui->lineEdit->setStyleSheet(style);
}
void OriginS1M1::updateAttribute()
{
    if(isWidgetReady())
    {
        float gain;
        if(isWidgetEnable())
        {
            if(mMuteAffectGain)
            {
                gain = ui->widgetPushButtonGroup1->getPushButtonStateMUTE() ? (mDisableGAIN) : (ui->widgetVSlider->getValue());
            }
            else
            {
                gain = ui->widgetVSlider->getValue();
            }
            if(mPreMIC != WorkspaceObserver::value("MIC").toString())
            {
                mPreMIC = WorkspaceObserver::value("MIC").toString();
                emit attributeChanged(this->objectName(), "MIC", mPreMIC);
            }
            if(mPre48V != static_cast<int>(ui->widgetPushButtonGroup1->getPushButtonState48V()))
            {
                mPre48V = static_cast<int>(ui->widgetPushButtonGroup1->getPushButtonState48V());
                emit attributeChanged(this->objectName(), "48V", QString::number(mPre48V));
            }
            if(mPreGAIN != gain)
            {
                mPreGAIN = gain;
                emit attributeChanged(this->objectName(), "GAIN", QString::number(mPreGAIN));
            }
            if(mPreMUTE != static_cast<int>(ui->widgetPushButtonGroup1->getPushButtonStateMUTE()))
            {
                mPreMUTE = static_cast<int>(ui->widgetPushButtonGroup1->getPushButtonStateMUTE());
                emit attributeChanged(this->objectName(), "MUTE", QString::number(mPreMUTE));
            }
        }
        else
        {
            if(mMuteAffectGain)
            {
                gain = mDisableGAIN;
            }
            else
            {
                gain = ui->widgetVSlider->getValue();
            }
            if(mPreGAIN != gain)
            {
                mPreGAIN = gain;
                emit attributeChanged(this->objectName(), "GAIN", QString::number(mPreGAIN));
            }
            if(mPreMUTE != static_cast<int>(true))
            {
                mPreMUTE = static_cast<int>(true);
                emit attributeChanged(this->objectName(), "MUTE", QString::number(mPreMUTE));
            }
        }
    }
}
void OriginS1M1::loadSettings()
{
    mPreMIC = "";
    mPre48V = -2147483648;
    mPreMUTE = -2147483648;
    mPreGAIN = -2147483648;
    setWidgetReady(false);
    WorkspaceObserver::getSettings()->beginGroup(objectName());
    bool flag=WorkspaceObserver::getSettings()->contains(objectName());
    WorkspaceObserver::getSettings()->endGroup();
    if(!flag)
    {
        WorkspaceObserver::setValue(objectName(), true);
        WorkspaceObserver::setValue("ChannelName", getChannelName());
        WorkspaceObserver::setValue("MIC", "Mic1");
        WorkspaceObserver::setValue("48V", false);
        WorkspaceObserver::setValue("MUTE", false);
        WorkspaceObserver::setValue("GAIN", ui->widgetVSlider->getDefault());
    }
    ui->lineEdit->setText(WorkspaceObserver::value("ChannelName").toString());
    if(WorkspaceObserver::value("MIC").toString() == "Mic1")
    {
        ui->widgetPushButtonGroup1->setPushButtonStateMic1(true);
    }
    else if(WorkspaceObserver::value("MIC").toString() == "Mic35")
    {
        ui->widgetPushButtonGroup1->setPushButtonStateMic35(true);
    }
    else if(WorkspaceObserver::value("MIC").toString() == "MicHP")
    {
        ui->widgetPushButtonGroup1->setPushButtonStateMicHP(true);
    }
    else
    {
        ui->widgetPushButtonGroup1->setPushButtonStateMic1(true);
    }
    ui->widgetPushButtonGroup1->setPushButtonState48V(WorkspaceObserver::value("48V").toBool());
    ui->widgetVSlider->setValue(WorkspaceObserver::value("GAIN").toFloat());
    if(isWidgetEmitAction())
    {
        emit attributeChanged(this->objectName(), "Save_ChannelName", WorkspaceObserver::value("ChannelName").toString());
        emit attributeChanged(this->objectName(), "Save_Enable", QString::number(isWidgetEnable()));
        emit attributeChanged(this->objectName(), "Save_MIC", WorkspaceObserver::value("MIC").toString());
        emit attributeChanged(this->objectName(), "Save_48V", QString::number(WorkspaceObserver::value("48V").toBool()));
        emit attributeChanged(this->objectName(), "Save_MUTE", QString::number(WorkspaceObserver::value("MUTE").toBool()));
        emit attributeChanged(this->objectName(), "Save_GAIN", WorkspaceObserver::value("GAIN").toString());
    }
    setWidgetReady(true);
    updateAttribute();
}
void OriginS1M1::AppSettingsChanged(QString objectName, QString attribute, QString value)
{
    Q_UNUSED(objectName);
    Q_UNUSED(attribute);
    Q_UNUSED(value);
}


// slot
void OriginS1M1::in_mTimer_timeout()
{
    ui->pushButtonClose->show();
}
void OriginS1M1::in_widgetVSlider_valueChanged(int value)
{
    save("GAIN", value);
    updateAttribute();
    if(mGainAffectMute && ui->widgetPushButtonGroup1->getPushButtonStateMUTE())
    {
        ui->widgetPushButtonGroup1->setPushButtonClickedMUTE(false);
    }
}
void OriginS1M1::in_widgetPushButtonGroup1_buttonStateChanged(PushButtonS1M8::ButtonID button, bool state)
{
    switch(button)
    {
        case PushButtonS1M8::buttonMic1:
            save("MIC", "Mic1");
            break;
        case PushButtonS1M8::buttonMic35:
            save("MIC", "Mic35");
            break;
        case PushButtonS1M8::buttonMicHP:
            save("MIC", "MicHP");
            break;
        case PushButtonS1M8::button48V:
            save("48V", state);
            break;
        case PushButtonS1M8::buttonMUTE:
            save("MUTE", state);
            break;
        default:
            break;
    }
    updateAttribute();
}
void OriginS1M1::on_lineEdit_textChanged(const QString& arg1)
{
    QFont font=ui->lineEdit->font();
    font.setPointSize(5);
    if(!GLBFHandle.isSuitable(font, arg1, minimumWidth() - 6))
    {
        ui->lineEdit->setText(arg1.chopped(1));
    }
}
void OriginS1M1::on_lineEdit_editingFinished()
{
    if(ui->lineEdit->text().isEmpty())
    {
        ui->lineEdit->setText(getChannelName());
    }
    ui->lineEdit->clearFocus();
    save("ChannelName", ui->lineEdit->text());
}
void OriginS1M1::on_pushButtonClose_clicked()
{
    emit attributeChanged(getChannelName(), "Hide", QString::number(static_cast<int>(true)));
    ui->pushButtonClose->hide();
}


// setter & getter
void OriginS1M1::save(QAnyStringView key, const QVariant& value)
{
    WorkspaceObserver::setValue(key, value);
    if(isWidgetEmitAction())
    {
        emit attributeChanged(this->objectName(), "Save_" + key.toString(), value.typeId() == QMetaType::Bool ? (QString::number(value.toBool())) : (value.toString()));
    }
}
OriginS1M1& OriginS1M1::setName(QString name)
{
    setObjectName(name);
    WorkspaceObserver::setObserverName(name);
    AppSettingsObserver::setObserverName(name);
    return *this;
}
OriginS1M1& OriginS1M1::setFont(QFont font)
{
    mFont = font;
    ui->widgetVolumeMeter->setFont(font);
    ui->widgetVSlider->setFont(font);
    ui->widgetPushButtonGroup1->setFont(font);
    QResizeEvent e(size(), size());
    resizeEvent(&e);
    update();
    return *this;
}
OriginS1M1& OriginS1M1::setVolumeMeter(int value)
{
    ui->widgetVolumeMeter->setValue(value);
    return *this;
}
OriginS1M1& OriginS1M1::setVolumeMeterClear()
{
    ui->widgetVolumeMeter->setMeterClear();
    return *this;
}
OriginS1M1& OriginS1M1::setVolumeMeterSlip()
{
    ui->widgetVolumeMeter->setMeterSlip();
    return *this;
}
OriginS1M1& OriginS1M1::setGain(float value)
{
    ui->widgetVSlider->setValue(value);
    return *this;
}
OriginS1M1& OriginS1M1::setGainLock(bool state)
{
    ui->widgetVSlider->setEnabled(!state);
    return *this;
}
OriginS1M1& OriginS1M1::setMuteAffectGain(bool state)
{
    mMuteAffectGain = state;
    return *this;
}
OriginS1M1& OriginS1M1::setGainAffectMute(bool state)
{
    mGainAffectMute = state;
    return *this;
}
OriginS1M1& OriginS1M1::setGainRange(float min, float max)
{
    ui->widgetVSlider->setRange(min, max);
    return *this;
}
OriginS1M1& OriginS1M1::setGainDefault(float value)
{
    ui->widgetVSlider->setDefault(value);
    return *this;
}
OriginS1M1& OriginS1M1::setGainWidgetDisable(float value)
{
    mDisableGAIN = value;
    return *this;
}
OriginS1M1& OriginS1M1::setChannelNameEditable(bool state)
{
    ui->lineEdit->setEnabled(state);
    return *this;
}
OriginS1M1& OriginS1M1::setValueMIC(QString mic)
{
    mPreMIC = mic;
    if(mPreMIC == "Mic1")
    {
        ui->widgetPushButtonGroup1->setPushButtonStateMic1(true);
    }
    else if(mPreMIC == "Mic35")
    {
        ui->widgetPushButtonGroup1->setPushButtonStateMic35(true);
    }
    else if(mPreMIC == "MicHP")
    {
        ui->widgetPushButtonGroup1->setPushButtonStateMicHP(true);
    }
    else
    {
        ui->widgetPushButtonGroup1->setPushButtonStateMic1(true);
    }
    save("MIC", mPreMIC);
    return *this;
}
OriginS1M1& OriginS1M1::setValue48V(bool state)
{
    mPre48V = state;
    ui->widgetPushButtonGroup1->setPushButtonState48V(mPre48V);
    save("48V", mPre48V);
    return *this;
}
OriginS1M1& OriginS1M1::setValueGAIN(float value)
{
    mPreGAIN = value;
    ui->widgetVSlider->setValue(mPreGAIN);
    save("GAIN", mPreGAIN);
    return *this;
}
OriginS1M1& OriginS1M1::setValueMUTE(bool state)
{
    mPreMUTE = state;
    ui->widgetPushButtonGroup1->setPushButtonStateMUTE(mPreMUTE);
    save("MUTE", mPreMUTE);
    return *this;
}

