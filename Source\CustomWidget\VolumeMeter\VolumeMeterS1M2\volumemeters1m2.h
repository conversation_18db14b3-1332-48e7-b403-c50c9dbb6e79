#ifndef VOLUMEMETERS1M2_H
#define VOLUMEMETERS1M2_H


#include <QFont>
#include <QRect>
#include <QTimer>
#include <QWidget>
#include <QPainter>
#include <QMouseEvent>
#include <QPaintEvent>
#include <QResizeEvent>


class VolumeMeterS1M2 : public QWidget
{
    Q_OBJECT
public:
    explicit VolumeMeterS1M2(QWidget* parent=nullptr);
    ~VolumeMeterS1M2();
    void setFont(QFont font);
    void setColorBG(QColor color);
    void setValueLeft(int value);
    void setValueRight(int value);
    void setMeterLeftClear();
    void setMeterLeftSlip();
    void setMeterRightClear();
    void setMeterRightSlip();
    void setWidthRatio(int space1, int space2, int meter, int scale);
    void setHeightRatio(int text, int space1, int clip, int space2, int volume, int space3);
    void setScaleLineHidden(bool hidden=true);
protected:
    void resizeEvent(QResizeEvent* e) override;
    void paintEvent(QPaintEvent* e) override;
    void mouseDoubleClickEvent(QMouseEvent* e) override;
private:
    struct RectMeter
    {
        int volumeValue=-60;
        int volumeMax=-60;
        bool clipStatus=false;
        QRect text;
        QRect clip;
        QRect volume;
        QTimer timerText;
        QTimer timerClip;
    };
    QTimer mTimerMeterLeft;
    QTimer mTimerMeterRight;
    RectMeter mRectMeterLeft;
    RectMeter mRectMeterRight;
    QRect mRectScale;
    QFont mFont;
    QColor mColorBG=QColor(22, 22, 22);
    int mSpace1=5;
    int mSpace2=16;
    int mMeter=18;
    int mScale=22;
    int mHText=10;
    int mHSpace1=2;
    int mHClip=2;
    int mHSpace2=1;
    int mHVolume=83;
    int mHSpace3=2;
    bool mScaleLineHidden=false;
    void drawBG(QPainter* painter);
    void drawElement(QPainter* painter);
private slots:
    void in_timerTextLeft_timeout();
    void in_timerTextRight_timeout();
    void in_timerClipLeft_timeout();
    void in_timerClipRight_timeout();
    void in_mTimerMeterLeft_timeout();
    void in_mTimerMeterRight_timeout();
};


#endif // VOLUMEMETERS1M2_H

