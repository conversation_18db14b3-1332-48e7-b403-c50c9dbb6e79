#ifndef DEVICECONNECTORVIEWBASE_H
#define DEVICECONNECTORVIEWBASE_H


#include <QWidget>


class DeviceConnectorViewBase : public QWidget
{
    Q_OBJECT
public:
    explicit DeviceConnectorViewBase(QWidget* parent=nullptr);
    virtual ~DeviceConnectorViewBase();
    virtual void setFont(QFont font) = 0;
    virtual void setLanguage(QString language) = 0;
    virtual void modifyVersion(QString current, QString updatable) = 0;
    virtual void modifyUpdateMessage(QString text) = 0;
    virtual void modifyUpdateProgress(QString text) = 0;
    virtual void modifyUpdateProgress(int progress) = 0;
    virtual int currentPage() = 0;
    virtual void showPage(int page) = 0;
signals:
    void attributeChanged(QString objectName, QString attribute, QString value);
};


#endif // DEVICECONNECTORVIEWBASE_H

