<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>EffectS1M4</class>
 <widget class="QWidget" name="EffectS1M4">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>311</width>
    <height>644</height>
   </rect>
  </property>
  <property name="sizePolicy">
   <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
    <horstretch>0</horstretch>
    <verstretch>0</verstretch>
   </sizepolicy>
  </property>
  <property name="minimumSize">
   <size>
    <width>100</width>
    <height>274</height>
   </size>
  </property>
  <property name="maximumSize">
   <size>
    <width>16777215</width>
    <height>16777215</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <widget class="QLabel" name="labelWet">
   <property name="geometry">
    <rect>
     <x>220</x>
     <y>70</y>
     <width>54</width>
     <height>16</height>
    </rect>
   </property>
   <property name="text">
    <string>Wet</string>
   </property>
   <property name="alignment">
    <set>Qt::AlignmentFlag::AlignCenter</set>
   </property>
  </widget>
  <widget class="QLabel" name="labelDry">
   <property name="geometry">
    <rect>
     <x>30</x>
     <y>70</y>
     <width>54</width>
     <height>16</height>
    </rect>
   </property>
   <property name="text">
    <string>Dry </string>
   </property>
   <property name="alignment">
    <set>Qt::AlignmentFlag::AlignCenter</set>
   </property>
  </widget>
  <widget class="DialS1M5" name="dialDryWet" native="true">
   <property name="geometry">
    <rect>
     <x>110</x>
     <y>60</y>
     <width>90</width>
     <height>41</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="minimumSize">
    <size>
     <width>0</width>
     <height>0</height>
    </size>
   </property>
  </widget>
  <widget class="DialS1M1" name="dialDecay" native="true">
   <property name="geometry">
    <rect>
     <x>120</x>
     <y>330</y>
     <width>61</width>
     <height>71</height>
    </rect>
   </property>
  </widget>
  <widget class="QLabel" name="labelReverbDecayMin">
   <property name="geometry">
    <rect>
     <x>70</x>
     <y>410</y>
     <width>22</width>
     <height>15</height>
    </rect>
   </property>
   <property name="text">
    <string>Max</string>
   </property>
   <property name="alignment">
    <set>Qt::AlignmentFlag::AlignCenter</set>
   </property>
  </widget>
  <widget class="QLabel" name="labelReverbRoomSmall">
   <property name="geometry">
    <rect>
     <x>50</x>
     <y>200</y>
     <width>41</width>
     <height>51</height>
    </rect>
   </property>
   <property name="text">
    <string>Small</string>
   </property>
   <property name="alignment">
    <set>Qt::AlignmentFlag::AlignCenter</set>
   </property>
  </widget>
  <widget class="QLabel" name="labelReverbDecayMax">
   <property name="geometry">
    <rect>
     <x>190</x>
     <y>410</y>
     <width>101</width>
     <height>20</height>
    </rect>
   </property>
   <property name="text">
    <string>Min</string>
   </property>
   <property name="alignment">
    <set>Qt::AlignmentFlag::AlignCenter</set>
   </property>
  </widget>
  <widget class="QWidget" name="rbWidget" native="true">
   <property name="geometry">
    <rect>
     <x>60</x>
     <y>460</y>
     <width>171</width>
     <height>161</height>
    </rect>
   </property>
   <layout class="QGridLayout" name="gridLayout_3" rowstretch="8,14,6,14,6,14,6,14,8" columnstretch="11,38,11">
    <property name="leftMargin">
     <number>0</number>
    </property>
    <property name="topMargin">
     <number>0</number>
    </property>
    <property name="rightMargin">
     <number>0</number>
    </property>
    <property name="bottomMargin">
     <number>0</number>
    </property>
    <property name="spacing">
     <number>0</number>
    </property>
    <item row="0" column="1">
     <spacer name="verticalSpacer_6">
      <property name="orientation">
       <enum>Qt::Orientation::Vertical</enum>
      </property>
      <property name="sizeHint" stdset="0">
       <size>
        <width>0</width>
        <height>0</height>
       </size>
      </property>
     </spacer>
    </item>
    <item row="1" column="1">
     <widget class="QPushButton" name="pushButtonReverbStudio">
      <property name="sizePolicy">
       <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="checkable">
       <bool>false</bool>
      </property>
     </widget>
    </item>
    <item row="2" column="1">
     <spacer name="verticalSpacer_7">
      <property name="orientation">
       <enum>Qt::Orientation::Vertical</enum>
      </property>
      <property name="sizeHint" stdset="0">
       <size>
        <width>0</width>
        <height>0</height>
       </size>
      </property>
     </spacer>
    </item>
    <item row="3" column="1">
     <widget class="QPushButton" name="pushButtonReverbLive">
      <property name="sizePolicy">
       <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="checkable">
       <bool>false</bool>
      </property>
     </widget>
    </item>
    <item row="4" column="0">
     <spacer name="horizontalSpacer_5">
      <property name="orientation">
       <enum>Qt::Orientation::Horizontal</enum>
      </property>
      <property name="sizeHint" stdset="0">
       <size>
        <width>0</width>
        <height>0</height>
       </size>
      </property>
     </spacer>
    </item>
    <item row="4" column="1">
     <spacer name="verticalSpacer_8">
      <property name="orientation">
       <enum>Qt::Orientation::Vertical</enum>
      </property>
      <property name="sizeHint" stdset="0">
       <size>
        <width>0</width>
        <height>0</height>
       </size>
      </property>
     </spacer>
    </item>
    <item row="4" column="2">
     <spacer name="horizontalSpacer_6">
      <property name="orientation">
       <enum>Qt::Orientation::Horizontal</enum>
      </property>
      <property name="sizeHint" stdset="0">
       <size>
        <width>0</width>
        <height>0</height>
       </size>
      </property>
     </spacer>
    </item>
    <item row="5" column="1">
     <widget class="QPushButton" name="pushButtonReverbHall">
      <property name="sizePolicy">
       <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="checkable">
       <bool>false</bool>
      </property>
     </widget>
    </item>
    <item row="6" column="1">
     <spacer name="verticalSpacer_9">
      <property name="orientation">
       <enum>Qt::Orientation::Vertical</enum>
      </property>
      <property name="sizeHint" stdset="0">
       <size>
        <width>0</width>
        <height>0</height>
       </size>
      </property>
     </spacer>
    </item>
    <item row="7" column="1">
     <widget class="QPushButton" name="pushButtonBypassReverb">
      <property name="sizePolicy">
       <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="checkable">
       <bool>true</bool>
      </property>
     </widget>
    </item>
    <item row="8" column="1">
     <spacer name="verticalSpacer_10">
      <property name="orientation">
       <enum>Qt::Orientation::Vertical</enum>
      </property>
      <property name="sizeHint" stdset="0">
       <size>
        <width>0</width>
        <height>0</height>
       </size>
      </property>
     </spacer>
    </item>
   </layout>
  </widget>
  <widget class="QLineEdit" name="lineEditReverb">
   <property name="geometry">
    <rect>
     <x>100</x>
     <y>10</y>
     <width>91</width>
     <height>21</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true"/>
   </property>
   <property name="text">
    <string>Reverb</string>
   </property>
   <property name="alignment">
    <set>Qt::AlignmentFlag::AlignCenter</set>
   </property>
  </widget>
  <widget class="QLabel" name="labelReverbRoomLarge">
   <property name="geometry">
    <rect>
     <x>180</x>
     <y>200</y>
     <width>81</width>
     <height>31</height>
    </rect>
   </property>
   <property name="text">
    <string>Large</string>
   </property>
   <property name="alignment">
    <set>Qt::AlignmentFlag::AlignCenter</set>
   </property>
  </widget>
  <widget class="QLabel" name="labelRoomSize">
   <property name="geometry">
    <rect>
     <x>110</x>
     <y>130</y>
     <width>62</width>
     <height>16</height>
    </rect>
   </property>
   <property name="text">
    <string>Room Size</string>
   </property>
   <property name="alignment">
    <set>Qt::AlignmentFlag::AlignCenter</set>
   </property>
  </widget>
  <widget class="QLabel" name="labelDecayRate">
   <property name="geometry">
    <rect>
     <x>100</x>
     <y>300</y>
     <width>65</width>
     <height>15</height>
    </rect>
   </property>
   <property name="text">
    <string>Decay Rate</string>
   </property>
   <property name="alignment">
    <set>Qt::AlignmentFlag::AlignCenter</set>
   </property>
  </widget>
  <widget class="DialS1M1" name="dialRoom" native="true">
   <property name="geometry">
    <rect>
     <x>100</x>
     <y>190</y>
     <width>61</width>
     <height>71</height>
    </rect>
   </property>
  </widget>
 </widget>
 <customwidgets>
  <customwidget>
   <class>DialS1M5</class>
   <extends>QWidget</extends>
   <header location="global">dials1m5.h</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>DialS1M1</class>
   <extends>QWidget</extends>
   <header>dials1m1.h</header>
   <container>1</container>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
