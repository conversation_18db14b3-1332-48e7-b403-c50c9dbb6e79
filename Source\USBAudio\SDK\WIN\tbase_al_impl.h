/************************************************************************
 *
 *  Module:       tbase_al_impl.h
 *  Description:
 *     libbase abstraction layer implementation for Windows user mode
 *
 *  Author(s):
 *    Udo <PERSON>hardt
 *
 *  Companies:
 *    Thesycon GmbH, Germany      http://www.thesycon.de
 *
 ************************************************************************/

#ifndef __tbase_al_impl_h__
#define __tbase_al_impl_h__

// generic implementation of TbCopyMemory etc.
#include "tbase_al_impl_generic.h"


//
// Apple MacOS
//
#ifdef TBASE_COMPILER_APPLE

/* on MacOS these GNU specific intrinsics are available */
#define TbAtomicIncrementInt32(target) __sync_add_and_fetch((target), 1)
#define TbAtomicDecrementInt32(target) __sync_add_and_fetch((target), -1)

#endif //TBASE_COMPILER_APPLE



//
// GNU/Linux
//
#ifdef TBASE_COMPILER_GNU_LINUX

/* on Linux these GNU specific intrinsics are available */
#define TbAtomicIncrementInt32(target) __sync_add_and_fetch((target), 1);
#define TbAtomicDecrementInt32(target) __sync_sub_and_fetch((target), 1);

#endif



#endif  /* __tbase_al_impl_h__ */

/*************************** EOF **************************************/
