#include "globalfont.h"
#include "volumemeters1m1.h"


VolumeMeterS1M1::VolumeMeterS1M1(QWidget* parent)
    : QWidget(parent)
{
    mRectMeter.timerText.setSingleShot(true);
    mRectMeter.timerClip.setSingleShot(true);
    connect(&mRectMeter.timerText, SIGNAL(timeout()), this, SLOT(in_timerText_timeout()), Qt::UniqueConnection);
    connect(&mRectMeter.timerClip, SIGNAL(timeout()), this, SLOT(in_timerClip_timeout()), Qt::UniqueConnection);
    connect(&mTimerMeter, SIGNAL(timeout()), this, SLOT(in_mTimerMeter_timeout()), Qt::UniqueConnection);
}
VolumeMeterS1M1::~VolumeMeterS1M1()
{

}


// override
void VolumeMeterS1M1::resizeEvent(QResizeEvent* e)
{
    Q_UNUSED(e);
    // W
    float wPixelPerRatio=size().width() / 100.0;
    int wSpace1=wPixelPerRatio * mSpace1;
    int wMeter=wPixelPerRatio * mMeter;
    int wSpace2=wPixelPerRatio * mSpace2;
    int wScale=wPixelPerRatio * mScale;
    int wRemain=size().width() - wSpace1 - wMeter - wSpace2 - wScale;
    int xMeter=0 + wRemain / 2 + wSpace1;
    int xScale=xMeter + wMeter + wSpace2;
    // H
    float pixelPerRatio = size().height() / 100.0;
    int hText=pixelPerRatio * mHText;
    int hSpace1=pixelPerRatio * mHSpace1;
    int hClip=pixelPerRatio * mHClip;
    int hSpace2=pixelPerRatio * mHSpace2;
    int hVolume=pixelPerRatio * mHVolume;
    int hSpace3=pixelPerRatio * mHSpace3;
    int hRemain=size().height() - hText - hSpace1 - hClip - hSpace2 - hVolume - hSpace3;
    int yText=0 + hRemain / 2;
    int yTextEnd=yText + hText;
    int yClip=yTextEnd + hSpace1;
    int yClipEnd=yClip + hClip;
    int yVolume=yClipEnd + hSpace2;
    mRectMeter.text.setRect(xMeter, yText, wMeter, hText);
    mRectMeter.clip.setRect(xMeter, yClip, wMeter, hClip);
    mRectMeter.volume.setRect(xMeter, yVolume, wMeter, hVolume);
    mRectScale.setRect(xScale, 0, wScale, size().height());
}
void VolumeMeterS1M1::paintEvent(QPaintEvent* e)
{
    Q_UNUSED(e);
    QPainter painter(this);
    painter.setRenderHints(QPainter::Antialiasing | QPainter::TextAntialiasing);
    drawBG(&painter);
    drawElement(&painter);
}
void VolumeMeterS1M1::mouseDoubleClickEvent(QMouseEvent* e)
{
    if(e->button() == Qt::LeftButton)
    {
        if(mRectMeter.clip.contains(e->pos()))
        {
            mRectMeter.clipStatus = false;
            update();
        }
    }
}
void VolumeMeterS1M1::drawBG(QPainter* painter)
{
    painter->save();
    painter->setPen(Qt::NoPen);
    painter->setBrush(mColorBG);
    painter->drawRect(rect());
    painter->restore();
}
void VolumeMeterS1M1::drawElement(QPainter* painter)
{
    float pixelPerScale;
    QPointF textPoint;
    QRect rectMeter;
    painter->save();
    // font
    mFont.setPointSize(GLBFHandle.getSuitablePointSize(mFont, "-60", mRectScale.width()));
    painter->setFont(mFont);
    // text
    QColor textColor(161, 161, 161);
    mRectMeter.volumeMax >= -8 ? (textColor.setRgb(186,113,68)) : ((void) 0);
    mRectMeter.volumeMax == 0 ? (textColor.setRgb(246,72,71)) : ((void) 0);
    painter->setPen(textColor);
    painter->setBrush(Qt::NoBrush);
    QString str = QString("%1").arg((double) mRectMeter.volumeMax, 0, 'f', 0);
    textPoint.setX(mRectMeter.text.x() + (mRectMeter.text.width() - painter->fontMetrics().horizontalAdvance(str)) / 2);
    textPoint.setY(mRectMeter.text.y() + mRectMeter.text.height());
    painter->drawText(textPoint, str);
    // clip
    painter->setPen(Qt::NoPen);
    mRectMeter.clipStatus ? (painter->setBrush(QBrush(QColor(246, 72, 71)))) : (painter->setBrush(QBrush(QColor(60, 60, 60))));
    painter->drawRoundedRect(mRectMeter.clip, mRectMeter.clip.height() * 0.1, mRectMeter.clip.height() * 0.1);
    // volume
    painter->setBrush(QBrush(QColor(60, 60, 60)));
    painter->drawRoundedRect(mRectMeter.volume, mRectMeter.volume.width() * 0.1, mRectMeter.volume.width() * 0.1);

    painter->setPen(Qt::NoPen);
    QLinearGradient gradient(mRectMeter.volume.topLeft(), mRectMeter.volume.bottomLeft());
    gradient.setColorAt(0.0, QColor("#f64847"));
    gradient.setColorAt(0.1, QColor("#a77d43"));
    gradient.setColorAt(0.2, QColor("#0f9640"));
    gradient.setColorAt(1.0, QColor("#009641"));
    painter->setBrush(gradient);
    pixelPerScale = mRectMeter.volume.height() / 60.0;
    rectMeter = mRectMeter.volume;
    rectMeter.setY(rectMeter.y() + pixelPerScale * (0 - mRectMeter.volumeValue));
    rectMeter.setHeight(rectMeter.bottomLeft().y() - rectMeter.y());
    if(mRectMeter.volumeValue != -60)
    {
        painter->drawRoundedRect(rectMeter, rectMeter.width() * 0.1, rectMeter.width() * 0.1);
    }
    // scale
    int wScaleLine=mRectMeter.volume.width() / 4;
    painter->setPen(QColor(161, 161, 161));
    painter->setBrush(Qt::NoBrush);
    textPoint.setX(mRectScale.x() + (mRectScale.width() - painter->fontMetrics().horizontalAdvance("0")) / 2);
    textPoint.setY(mRectMeter.volume.y() + 0 * pixelPerScale);
    if(!mScaleLineHidden) painter->drawLine(mRectMeter.volume.x() + mRectMeter.volume.width(), textPoint.y() + 1, mRectMeter.volume.x() + mRectMeter.volume.width() + wScaleLine, textPoint.y() + 1);
    painter->drawText(textPoint, "0");
    textPoint.setX(mRectScale.x() + (mRectScale.width() - painter->fontMetrics().horizontalAdvance("-6")) / 2);
    textPoint.setY(mRectMeter.volume.y() + 6 * pixelPerScale);
    if(!mScaleLineHidden) painter->drawLine(mRectMeter.volume.x() + mRectMeter.volume.width(), textPoint.y() + 1, mRectMeter.volume.x() + mRectMeter.volume.width() + wScaleLine, textPoint.y() + 1);
    painter->drawText(textPoint, "-6");
    textPoint.setX(mRectScale.x() + (mRectScale.width() - painter->fontMetrics().horizontalAdvance("-12")) / 2);
    textPoint.setY(mRectMeter.volume.y() + 12 * pixelPerScale);
    if(!mScaleLineHidden) painter->drawLine(mRectMeter.volume.x() + mRectMeter.volume.width(), textPoint.y() + 1, mRectMeter.volume.x() + mRectMeter.volume.width() + wScaleLine, textPoint.y() + 1);
    painter->drawText(textPoint, "-12");
    textPoint.setX(mRectScale.x() + (mRectScale.width() - painter->fontMetrics().horizontalAdvance("-24")) / 2);
    textPoint.setY(mRectMeter.volume.y() + 24 * pixelPerScale);
    if(!mScaleLineHidden) painter->drawLine(mRectMeter.volume.x() + mRectMeter.volume.width(), textPoint.y() + 1, mRectMeter.volume.x() + mRectMeter.volume.width() + wScaleLine, textPoint.y() + 1);
    painter->drawText(textPoint, "-24");
    textPoint.setX(mRectScale.x() + (mRectScale.width() - painter->fontMetrics().horizontalAdvance("-36")) / 2);
    textPoint.setY(mRectMeter.volume.y() + 36 * pixelPerScale);
    if(!mScaleLineHidden) painter->drawLine(mRectMeter.volume.x() + mRectMeter.volume.width(), textPoint.y() + 1, mRectMeter.volume.x() + mRectMeter.volume.width() + wScaleLine, textPoint.y() + 1);
    painter->drawText(textPoint, "-36");
    textPoint.setX(mRectScale.x() + (mRectScale.width() - painter->fontMetrics().horizontalAdvance("-60")) / 2);
    textPoint.setY(mRectMeter.volume.y() + mRectMeter.volume.height());
    if(!mScaleLineHidden) painter->drawLine(mRectMeter.volume.x() + mRectMeter.volume.width(), textPoint.y() + 1, mRectMeter.volume.x() + mRectMeter.volume.width() + wScaleLine, textPoint.y() + 1);
    painter->drawText(textPoint, "-60");
    painter->restore();
}


// slot
void VolumeMeterS1M1::in_timerText_timeout()
{
    mRectMeter.volumeMax = mRectMeter.volumeValue;
    update();
}
void VolumeMeterS1M1::in_timerClip_timeout()
{
    mRectMeter.clipStatus = false;
    update();
}
void VolumeMeterS1M1::in_mTimerMeter_timeout()
{
    if(mRectMeter.volumeValue > -60)
    {
        mRectMeter.volumeValue = -60 >= mRectMeter.volumeValue ? (-60) : (qMax(-60, mRectMeter.volumeValue - 4));
        update();
    }
    else
    {
        mTimerMeter.stop();
    }
}


// setter & getter
void VolumeMeterS1M1::setFont(QFont font)
{
    mFont = font;
    update();
}
void VolumeMeterS1M1::setColorBG(QColor color)
{
    mColorBG = color;
    update();
}
void VolumeMeterS1M1::setValue(int value)
{
    if(value > 1)
    {
        return;
    }
    if(mTimerMeter.isActive())
    {
        mTimerMeter.stop();
    }
    if(value != 1)
    {
        value = qMax(-600, value);
        if(value % 10)
        {
            value = value / 10 - 1;
        }
        else
        {
            value = value / 10;
        }
    }
    if(value == 1)
    {
        mRectMeter.clipStatus = true;
        value = 0;
        mRectMeter.timerClip.start(5000);
    }
    mRectMeter.volumeValue = value >= mRectMeter.volumeValue ? (value) : (qMax(-60, mRectMeter.volumeValue - 4));
    if(mRectMeter.volumeValue > mRectMeter.volumeMax)
    {
        mRectMeter.volumeMax = mRectMeter.volumeValue;
        mRectMeter.timerText.start(2000);
    }
    else
    {
        if(!mRectMeter.timerText.isActive())
        {
            mRectMeter.volumeMax = mRectMeter.volumeValue;
        }
    }
    update();
}
void VolumeMeterS1M1::setMeterClear()
{
    mTimerMeter.stop();
    mRectMeter.timerClip.stop();
    mRectMeter.timerText.stop();
    mRectMeter.volumeValue = -60;
    mRectMeter.volumeMax = -60;
    mRectMeter.clipStatus = false;
    update();
}
void VolumeMeterS1M1::setMeterSlip()
{
    mTimerMeter.start(55);
}
void VolumeMeterS1M1::setWidthRatio(int space1, int meter, int space2, int scale)
{
    mSpace1 = space1;
    mMeter = meter;
    mSpace2 = space2;
    mScale = scale;
    QResizeEvent e(size(), size());
    resizeEvent(&e);
    update();
}
void VolumeMeterS1M1::setHeightRatio(int text, int space1, int clip, int space2, int volume, int space3)
{
    mHText = text;
    mHSpace1 = space1;
    mHClip = clip;
    mHSpace2 = space2;
    mHVolume = volume;
    mHSpace3 = space3;
    QResizeEvent e(size(), size());
    resizeEvent(&e);
    update();
}
void VolumeMeterS1M1::setScaleLineHidden(bool hidden)
{
    mScaleLineHidden = hidden;
    update();
}

