#ifndef PUSHBUTTONGROUPS1M7_H
#define PUSHBUTTONGROUPS1M7_H


#include <QHash>
#include <QWidget>
#include <QResizeEvent>


namespace Ui {
class PushButtonGroupS1M7;
}


class PushButtonGroupS1M7 : public QWidget
{
    Q_OBJECT
public:
    explicit PushButtonGroupS1M7(QWidget* parent=nullptr);
    ~PushButtonGroupS1M7();
    PushButtonGroupS1M7& setFont(QFont font);
    PushButtonGroupS1M7& setLanguage(QString language);
    PushButtonGroupS1M7& setState(QString button, QString state, bool needEmit=true);
    QString getState(QString button);
protected:
    void resizeEvent(QResizeEvent* e) override;
    void showEvent(QShowEvent*) override { resizeEvent(nullptr); }
private:
    Ui::PushButtonGroupS1M7* ui;
    QFont mFont;
    unsigned int mBitmap=0;
    QHash<unsigned int, QString> mStyle;
private slots:
    void on_PushButtonANTI_clicked(bool checked);
    void on_PushButtonSOLO_clicked(bool checked);
    void on_PushButtonMUTE_clicked(bool checked);
signals:
    void stateChanged(QString button, QString state);
};


#endif // PUSHBUTTONGROUPS1M7_H

