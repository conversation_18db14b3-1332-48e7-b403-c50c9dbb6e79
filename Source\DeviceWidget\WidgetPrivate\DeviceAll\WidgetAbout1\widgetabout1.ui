<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>WidgetAbout1</class>
 <widget class="QWidget" name="WidgetAbout1">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>712</width>
    <height>410</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>WidgetSytem1</string>
  </property>
  <layout class="QGridLayout" name="gridLayout" rowstretch="24,266,83" columnstretch="60,480,60">
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <property name="spacing">
    <number>0</number>
   </property>
   <item row="0" column="0" colspan="3">
    <spacer name="verticalSpacer_7">
     <property name="orientation">
      <enum>Qt::Orientation::Vertical</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>20</width>
       <height>20</height>
      </size>
     </property>
    </spacer>
   </item>
   <item row="1" column="2">
    <spacer name="horizontalSpacer_2">
     <property name="orientation">
      <enum>Qt::Orientation::Horizontal</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>20</width>
       <height>20</height>
      </size>
     </property>
    </spacer>
   </item>
   <item row="2" column="0" colspan="3">
    <spacer name="verticalSpacer">
     <property name="orientation">
      <enum>Qt::Orientation::Vertical</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>20</width>
       <height>20</height>
      </size>
     </property>
    </spacer>
   </item>
   <item row="1" column="1">
    <layout class="QVBoxLayout" name="verticalLayout" stretch="26,22,26,22,26,22,26,22,26">
     <property name="spacing">
      <number>0</number>
     </property>
     <item>
      <widget class="QWidget" name="widget1" native="true">
       <widget class="QLabel" name="label1">
        <property name="geometry">
         <rect>
          <x>30</x>
          <y>1</y>
          <width>57</width>
          <height>16</height>
         </rect>
        </property>
        <property name="text">
         <string>Model</string>
        </property>
       </widget>
       <widget class="QLabel" name="labelModel">
        <property name="geometry">
         <rect>
          <x>480</x>
          <y>10</y>
          <width>53</width>
          <height>15</height>
         </rect>
        </property>
        <property name="text">
         <string>M62</string>
        </property>
        <property name="alignment">
         <set>Qt::AlignmentFlag::AlignRight|Qt::AlignmentFlag::AlignTrailing|Qt::AlignmentFlag::AlignVCenter</set>
        </property>
       </widget>
      </widget>
     </item>
     <item>
      <spacer name="verticalSpacer_3">
       <property name="orientation">
        <enum>Qt::Orientation::Vertical</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>20</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QWidget" name="widget2" native="true">
       <widget class="QLabel" name="label2">
        <property name="geometry">
         <rect>
          <x>30</x>
          <y>1</y>
          <width>47</width>
          <height>16</height>
         </rect>
        </property>
        <property name="text">
         <string>Hardware version</string>
        </property>
       </widget>
       <widget class="QLabel" name="labelHard">
        <property name="geometry">
         <rect>
          <x>490</x>
          <y>0</y>
          <width>53</width>
          <height>15</height>
         </rect>
        </property>
        <property name="text">
         <string>V1.00</string>
        </property>
        <property name="alignment">
         <set>Qt::AlignmentFlag::AlignRight|Qt::AlignmentFlag::AlignTrailing|Qt::AlignmentFlag::AlignVCenter</set>
        </property>
       </widget>
      </widget>
     </item>
     <item>
      <spacer name="verticalSpacer_2">
       <property name="orientation">
        <enum>Qt::Orientation::Vertical</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>20</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QWidget" name="widget3" native="true">
       <widget class="QLabel" name="label3">
        <property name="geometry">
         <rect>
          <x>30</x>
          <y>1</y>
          <width>201</width>
          <height>16</height>
         </rect>
        </property>
        <property name="text">
         <string>Firmware version</string>
        </property>
       </widget>
       <widget class="QLabel" name="labelFirmwareTip">
        <property name="geometry">
         <rect>
          <x>400</x>
          <y>0</y>
          <width>53</width>
          <height>15</height>
         </rect>
        </property>
        <property name="text">
         <string>TextLabel</string>
        </property>
        <property name="alignment">
         <set>Qt::AlignmentFlag::AlignRight|Qt::AlignmentFlag::AlignTrailing|Qt::AlignmentFlag::AlignVCenter</set>
        </property>
       </widget>
       <widget class="QPushButton" name="button3">
        <property name="geometry">
         <rect>
          <x>470</x>
          <y>0</y>
          <width>80</width>
          <height>23</height>
         </rect>
        </property>
        <property name="styleSheet">
         <string notr="true"/>
        </property>
        <property name="text">
         <string/>
        </property>
       </widget>
      </widget>
     </item>
     <item>
      <spacer name="verticalSpacer_4">
       <property name="orientation">
        <enum>Qt::Orientation::Vertical</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>20</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QWidget" name="widget4" native="true">
       <widget class="QLabel" name="label4">
        <property name="geometry">
         <rect>
          <x>30</x>
          <y>1</y>
          <width>124</width>
          <height>16</height>
         </rect>
        </property>
        <property name="text">
         <string>Software version</string>
        </property>
       </widget>
       <widget class="QPushButton" name="button4">
        <property name="geometry">
         <rect>
          <x>480</x>
          <y>0</y>
          <width>80</width>
          <height>23</height>
         </rect>
        </property>
        <property name="styleSheet">
         <string notr="true"/>
        </property>
        <property name="text">
         <string/>
        </property>
       </widget>
       <widget class="QLabel" name="labelSoftwareTip">
        <property name="geometry">
         <rect>
          <x>400</x>
          <y>0</y>
          <width>53</width>
          <height>15</height>
         </rect>
        </property>
        <property name="text">
         <string>TextLabel</string>
        </property>
        <property name="alignment">
         <set>Qt::AlignmentFlag::AlignRight|Qt::AlignmentFlag::AlignTrailing|Qt::AlignmentFlag::AlignVCenter</set>
        </property>
       </widget>
      </widget>
     </item>
     <item>
      <spacer name="verticalSpacer_5">
       <property name="orientation">
        <enum>Qt::Orientation::Vertical</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>20</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QWidget" name="widget5" native="true">
       <widget class="QLabel" name="label5">
        <property name="geometry">
         <rect>
          <x>30</x>
          <y>1</y>
          <width>136</width>
          <height>16</height>
         </rect>
        </property>
        <property name="text">
         <string>Official website</string>
        </property>
       </widget>
       <widget class="QPushButton" name="button5">
        <property name="geometry">
         <rect>
          <x>470</x>
          <y>0</y>
          <width>80</width>
          <height>23</height>
         </rect>
        </property>
        <property name="styleSheet">
         <string notr="true">text-decoration: underline;
background:transparent;
text-align: right;</string>
        </property>
        <property name="text">
         <string>Topping.pro</string>
        </property>
       </widget>
      </widget>
     </item>
    </layout>
   </item>
   <item row="1" column="0">
    <spacer name="horizontalSpacer">
     <property name="orientation">
      <enum>Qt::Orientation::Horizontal</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>20</width>
       <height>20</height>
      </size>
     </property>
    </spacer>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
