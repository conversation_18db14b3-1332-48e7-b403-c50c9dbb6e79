#ifndef FIELDLOOPBACKS1M1_H
#define FIELDLOOPBACKS1M1_H


#include <QWidget>
#include <QVector>
#include <QString>

#include "workspace.h"
#include "appsettings.h"
#include "loopbackbase.h"
#include "fieldloopbackbase1.h"


class FieldLoopbackS1M1 : public FieldLoopbackBase1, public WorkspaceObserver, public AppSettingsObserver
{
    Q_OBJECT
public:
    explicit FieldLoopbackS1M1(QWidget* parent=nullptr, QString name="");
    ~FieldLoopbackS1M1();
    FieldLoopbackS1M1& setName(QString name);
    FieldLoopbackS1M1& modifyWidgetList(QVector<LoopbackBase*> list);
    FieldLoopbackS1M1& setVisibleListDefault(QVector<LoopbackBase*> list);
protected:
    void loadSettings() override;
    void AppSettingsChanged(QString objectName, QString attribute, QString value) override;
private:
    QVector<LoopbackBase*> mWidgetList;
    QVariantList mVisibleListDefault;
private slots:
    void in_widgetBase_attributeChanged(QString objectName, QString attribute, QString value);
    void in_widgetList_attributeChanged(QString objectName, QString attribute, QString value);
signals:
    void attributeChanged(QString objectName, QString attribute, QString value);
};


#endif // FIELDLOOPBACKS1M1_H

