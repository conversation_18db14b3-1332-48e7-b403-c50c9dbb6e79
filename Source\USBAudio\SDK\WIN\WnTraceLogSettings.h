/************************************************************************
 *
 *  Module:       WnTraceLogSettings.h
 *
 *  Description:  Trace and log settings
 *
 *  Runtime Env.: Win32
 *
 *  Author(s):
 *    Udo <PERSON>hardt,  <EMAIL>
 *
 *  Companies:
 *    Thesycon GmbH, Germany      http://www.thesycon.de
 *
 ************************************************************************/

#ifndef __WnTraceLogSettings_h__
#define __WnTraceLogSettings_h__

// optionally put everything into a namespace
#ifdef LIBWN_NAMESPACE
namespace LIBWN_NAMESPACE {
#endif

//
// default registry location under HKEY_LOCAL_MACHINE or HKEY_CURRENT_USER
//
#define WNTRACELOG_DEFAULT_REGISTRY_PATH  TEXT("Software\\Thesycon\\Debug")



//
// This class reads/writes trace settings from/to registry
//
class WnTraceLogSettings
{
public:
    // ctor
    WnTraceLogSettings(
        HMODULE hModule = NULL
        );

    // dtor
    //~WnTraceLogSettings();


/////////////////////////////////////////
// Methods
/////////////////////////////////////////
public:

    void
    SetModuleHandle(
        HMODULE hModule
        )
            { mModuleHandle = hModule; }


    // load settings, init traceLogContext
    // registryPath is relative to registryRootKey, context name will be appended
    WNERR
    LoadSettingsFromRegistry(
        WnTraceLogContext& traceLogContext,
        HKEY registryRootKey, // HKEY_LOCAL_MACHINE or HKEY_CURRENT_USER
        const TCHAR* registryPath = WNTRACELOG_DEFAULT_REGISTRY_PATH
        );

    // save current settings from traceLogContext
    // registryPath is relative to registryRootKey, context name will be appended
    WNERR
    SaveSettingsToRegistry(
        WnTraceLogContext& traceLogContext,
        HKEY registryRootKey, // HKEY_LOCAL_MACHINE or HKEY_CURRENT_USER
        const TCHAR* registryPath = WNTRACELOG_DEFAULT_REGISTRY_PATH
        );



/////////////////////////////////////////
// Implementation
/////////////////////////////////////////
protected:

    void
    GetDebugRegistryKey(
        WnTraceLogContext& traceLogContext,
        const TCHAR* registryPath,
        WnModuleFileName& moduleFileName,
        TCHAR* keyName,
        unsigned int maxChars
        );

/////////////////////////////////////////
// Data Members
/////////////////////////////////////////
protected:

    // module handle, or NULL
    HMODULE mModuleHandle;

    // module name components
    WnModuleFileName mModuleFileName;


// make copy constructor and assignment operator unavailable
PRIVATE_COPY_CONSTRUCTOR(WnTraceLogSettings)
PRIVATE_ASSIGNMENT_OPERATOR(WnTraceLogSettings)

};//class WnTraceLogSettings

#ifdef LIBWN_NAMESPACE
}
#endif

#endif // __WnTraceLogSettings_h__

/*************************** EOF **************************************/
