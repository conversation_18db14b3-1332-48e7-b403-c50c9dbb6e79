#ifndef BATTERYDRAWSTRATEGY_H
#define BATTERYDRAWSTRATEGY_H

#include <QPainter>
#include <QRectF>
#include <QObject>

struct BatteryColors
{
    QColor electricQuantityNormal = QColor(229, 229, 229);
    QColor electricQuantityCharging = QColor(0, 255, 0);
    QColor lightning = QColor(132, 132, 132);
    QColor textNormal = QColor(255, 255, 255);
    QColor textCharging = QColor(255, 255, 255);
};

class BatteryDrawStrategy : public QObject
{
    Q_OBJECT
public:
    explicit BatteryDrawStrategy(QObject* parent = nullptr) : QObject(parent) {}
    virtual ~BatteryDrawStrategy() = default;

    virtual void drawPower(QPainter* painter, const QRectF& powerRect,
                          int value, int lowValue, bool charging, const BatteryColors& colors) = 0;

    virtual void drawLightning(QPainter* painter, const QRectF& powerRect, const QColor& lightningColor)
    {
        painter->save();
        painter->setPen(Qt::NoPen);
        painter->setBrush(lightningColor);

        QPolygonF lightning;
        qreal centerX = powerRect.center().x();
        qreal centerY = powerRect.center().y();
        qreal height = powerRect.height();

        lightning << QPointF(centerX - height, centerY)
                << QPointF(centerX + height/24, centerY - height/2)
                << QPointF(centerX + height/3, centerY)
                << QPointF(centerX + height, centerY)
                << QPointF(centerX - height/24, centerY + height/2)
                << QPointF(centerX - height/3, centerY)
                << QPointF(centerX - height, centerY);
        painter->drawPolygon(lightning);
        painter->restore();
    }

    virtual void drawText(QPainter* painter, const QRectF& batteryRect, int value, const QFont& font, const QColor& textColor)
    {
        painter->save();
        painter->setPen(textColor);
        painter->setFont(font);
        painter->drawText(batteryRect, Qt::AlignCenter, QString("%1%").arg(value));
        painter->restore();
    }

    virtual void startChargingAnimation() {}

    virtual void stopChargingAnimation() {}

    virtual void setUpdateCallback(std::function<void()> callback) { mUpdateCallback = callback; }

protected:
    std::function<void()> mUpdateCallback;
    int mCurrentValue = 0;
    QTimer* mAnimationTimer = nullptr;
    int mAnimationValue = 0;
    int mAnimationInterval = 30;
};

class ContinuousDrawStrategy : public BatteryDrawStrategy
{
    Q_OBJECT
public:
    explicit ContinuousDrawStrategy(QObject* parent = nullptr);
    ~ContinuousDrawStrategy();

    void drawPower(QPainter* painter, const QRectF& powerRect,
                   int value, int lowValue, bool charging, const BatteryColors& colors) override;

    void startChargingAnimation() override;
    void stopChargingAnimation() override;

private:
    void onAnimationTimer();
};

class SegmentedBlinkStrategy : public BatteryDrawStrategy
{
    Q_OBJECT
public:
    explicit SegmentedBlinkStrategy(QObject* parent = nullptr,
                                   int segmentCount = 5, double segmentSpacingPercent = 0.05, int blinkInterval = 500);
    ~SegmentedBlinkStrategy();

    void drawPower(QPainter* painter, const QRectF& powerRect,
                   int value, int lowValue, bool charging, const BatteryColors& colors) override;

    void startChargingAnimation() override;
    void stopChargingAnimation() override;

    void setSegmentCount(int count);
    void setSegmentSpacingPercent(double spacingPercent);
    void setBlinkInterval(int intervalMs);

private:
    void onBlinkTimer();

private:
    int getCurrentSegment(int value, int lowValue, bool charging) const;
    QRectF getSegmentRect(int segment, const QRectF& powerRect) const;
    bool shouldSegmentShow(int segment,  int currentSegment, bool charging) const;

private:
    int mSegmentCount;
    double mSegmentSpacingPercent;
};

#endif // BATTERYDRAWSTRATEGY_H
