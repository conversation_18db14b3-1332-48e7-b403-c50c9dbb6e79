#include "solo.h"


void Solo::doGlobalSoloChanged(int state)
{
    Q_UNUSED(state);
    if(*mSoloState)
    {
        if(getMuteState() != !getSoloState())
        {
            setMuteClicked(!getSoloState());
        }
        if(getMuteStateLeft() != !getSoloStateLeft())
        {
            setMuteClickedLeft(!getSoloStateLeft());
        }
        if(getMuteStateRight() != !getSoloStateRight())
        {
            setMuteClickedRight(!getSoloStateRight());
        }
    }
    else
    {
        if(getMuteState() != mMuteState)
        {
            setMuteClicked(mMuteState);
        }
        if(getMuteStateLeft() != mMuteStateLeft)
        {
            setMuteClickedLeft(mMuteStateLeft);
        }
        if(getMuteStateRight() != mMuteStateRight)
        {
            setMuteClickedRight(mMuteStateRight);
        }
    }
}
bool Solo::doSolo()
{
    emit soloStateChanged(objectName(), getSoloState());
    if(*mSoloState)
    {
        if(getMuteState() != !getSoloState())
        {
            setMuteClicked(!getSoloState());
        }
    }
    else
    {
        if(getMuteState() != mMuteState)
        {
            setMuteClicked(mMuteState);
        }
    }
    return true;
}
bool Solo::doSoloLeft()
{
    emit soloStateChanged(objectName(), getSoloStateLeft());
    if(*mSoloState)
    {
        if(getMuteStateLeft() != !getSoloStateLeft())
        {
            setMuteClickedLeft(!getSoloStateLeft());
        }
    }
    else
    {
        if(getMuteStateLeft() != mMuteStateLeft)
        {
            setMuteClickedLeft(mMuteStateLeft);
        }
    }
    return true;
}
bool Solo::doSoloRight()
{
    emit soloStateChanged(objectName(), getSoloStateRight());
    if(*mSoloState)
    {
        if(getMuteStateRight() != !getSoloStateRight())
        {
            setMuteClickedRight(!getSoloStateRight());
        }
    }
    else
    {
        if(getMuteStateRight() != mMuteStateRight)
        {
            setMuteClickedRight(mMuteStateRight);
        }
    }
    return true;
}
bool Solo::doMute()
{
    bool ret=false;
    if(*mSoloState)
    {
        if(getMuteState() == getSoloState())
        {
            // if(*mSoloState == 1 && getSoloState())
            // {
                mMuteState = getMuteState();
                ret = true;
            // }
            setSoloClicked(!getMuteState());
        }
    }
    else
    {
        mMuteState = getMuteState();
        ret = true;
    }
    return ret;
}
bool Solo::doMuteLeft()
{
    bool ret=false;
    if(*mSoloState)
    {
        if(getMuteStateLeft() == getSoloStateLeft())
        {
            // if(*mSoloState == 1 && getSoloStateLeft())
            // {
                mMuteStateLeft = getMuteStateLeft();
                ret = true;
            // }
            setSoloClickedLeft(!getMuteStateLeft());
        }
    }
    else
    {
        mMuteStateLeft = getMuteStateLeft();
        ret = true;
    }
    return ret;
}
bool Solo::doMuteRight()
{
    bool ret=false;
    if(*mSoloState)
    {
        if(getMuteStateRight() == getSoloStateRight())
        {
            // if(*mSoloState == 1 && getSoloStateRight())
            // {
                mMuteStateRight = getMuteStateRight();
                ret = true;
            // }
            setSoloClickedRight(!getMuteStateRight());
        }
    }
    else
    {
        mMuteStateRight = getMuteStateRight();
        ret = true;
    }
    return ret;
}
void Solo::setLinkState(bool state)
{
    mLinkState = state;
    if(mLinkState)
    {
        if(getSoloState())
        {
            setSoloClicked(true);
        }
        if(getSoloStateLeft())
        {
            emit soloStateChanged(objectName(), false);
        }
        if(getSoloStateRight())
        {
            emit soloStateChanged(objectName(), false);
        }
    }
    else
    {
        if(getSoloStateLeft())
        {
            setSoloClickedLeft(true);
        }
        if(getSoloStateRight())
        {
            setSoloClickedRight(true);
        }
        if(getSoloState())
        {
            emit soloStateChanged(objectName(), false);
        }
    }
}
void Solo::loadSoloMuteState(bool stateLink, bool stateSolo, bool stateSoloLeft, bool stateSoloRight, bool stateMute, bool stateMuteLeft, bool stateMuteRight)
{
    mLinkState = stateLink;
    mMuteState = stateMute;
    mMuteStateLeft = stateMuteLeft;
    mMuteStateRight = stateMuteRight;
    setSoloState(stateSolo);
    setSoloStateLeft(stateSoloLeft);
    setSoloStateRight(stateSoloRight);
    if(*mSoloState)
    {
        stateSolo ? (setMuteState(false)) : (setMuteState(true));
        stateSoloLeft ? (setMuteStateLeft(false)) : (setMuteStateLeft(true));
        stateSoloRight ? (setMuteStateRight(false)) : (setMuteStateRight(true));
    }
    else
    {
        setMuteState(mMuteState);
        setMuteStateLeft(mMuteStateLeft);
        setMuteStateRight(mMuteStateRight);
    }
}

