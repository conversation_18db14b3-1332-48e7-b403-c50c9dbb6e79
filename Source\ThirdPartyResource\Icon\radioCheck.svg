<svg width="38" height="38" viewBox="0 0 38 38" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_25_545)">
<circle cx="19" cy="20" r="6" fill="#43CF7C"/>
</g>
<ellipse cx="3" cy="2" rx="3" ry="2" transform="matrix(1 0 0 -1 16 19)" fill="url(#paint0_linear_25_545)"/>
<defs>
<filter id="filter0_d_25_545" x="0.7" y="0.7" width="36.6" height="36.6" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-1"/>
<feGaussianBlur stdDeviation="6.15"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.262745 0 0 0 0 0.811765 0 0 0 0 0.486275 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_25_545"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_25_545" result="shape"/>
</filter>
<linearGradient id="paint0_linear_25_545" x1="3" y1="4" x2="3" y2="0" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
</defs>
</svg>
