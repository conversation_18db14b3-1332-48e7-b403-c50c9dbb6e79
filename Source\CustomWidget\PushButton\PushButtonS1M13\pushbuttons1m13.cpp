#include "globalfont.h"
#include "pushbuttons1m13.h"


PushButtonS1M13::PushButtonS1M13(QWidget* parent)
    : QWidget(parent)
{
    mPushButtonSOLOLeft.setParent(this);
    mPushButtonSOLORight.setParent(this);
    mPushButtonMUTELeft.setParent(this);
    mPushButtonMUTERight.setParent(this);
    mPushButtonANTILeft.setParent(this);
    mPushButtonANTIRight.setParent(this);
    mPushButtonSOLOLeft.setText("SOLO");
    mPushButtonSOLORight.setText("SOLO");
    mPushButtonMUTELeft.setText("MUTE");
    mPushButtonMUTERight.setText("MUTE");
    mPushButtonANTILeft.setText("∅");
    mPushButtonANTIRight.setText("∅");
    connect(&mPushButtonSOLOLeft, SIGNAL(clicked()), this, SLOT(in_mPushButtonSOLOLeft_clicked()), Qt::UniqueConnection);
    connect(&mPushButtonSOLORight, SIGNAL(clicked()), this, SLOT(in_mPushButtonSOLORight_clicked()), Qt::UniqueConnection);
    connect(&mPushButtonMUTELeft, SIGNAL(clicked()), this, SLOT(in_mPushButtonMUTELeft_clicked()), Qt::UniqueConnection);
    connect(&mPushButtonMUTERight, SIGNAL(clicked()), this, SLOT(in_mPushButtonMUTERight_clicked()), Qt::UniqueConnection);
    connect(&mPushButtonANTILeft, SIGNAL(clicked()), this, SLOT(in_mPushButtonANTILeft_clicked()), Qt::UniqueConnection);
    connect(&mPushButtonANTIRight, SIGNAL(clicked()), this, SLOT(in_mPushButtonANTIRight_clicked()), Qt::UniqueConnection);
}
PushButtonS1M13::~PushButtonS1M13()
{

}


// override
void PushButtonS1M13::resizeEvent(QResizeEvent* e)
{
    Q_UNUSED(e);
    // W
    float wPixelPerRatio=size().width() / 100.0;
    int wPushButton=wPixelPerRatio * mWeightWidth;
    int xPushButtonLeft=(size().width() - wPushButton * 2) / 4;
    int xPushButtonRight=size().width() / 2 + xPushButtonLeft;
    // H
    float hPushButton=size().height() / 8.0;
    mPushButtonANTILeft.setGeometry(xPushButtonLeft, 0, wPushButton, hPushButton * 2);
    mPushButtonANTIRight.setGeometry(xPushButtonRight, 0, wPushButton, hPushButton * 2);
    mPushButtonSOLOLeft.setGeometry(xPushButtonLeft, hPushButton * 3, wPushButton, hPushButton * 2);
    mPushButtonSOLORight.setGeometry(xPushButtonRight, hPushButton * 3, wPushButton, hPushButton * 2);
    mPushButtonMUTELeft.setGeometry(xPushButtonLeft, hPushButton * 6, wPushButton, hPushButton * 2);
    mPushButtonMUTERight.setGeometry(xPushButtonRight, hPushButton * 6, wPushButton, hPushButton * 2);
    mFont.setPointSize(GLBFHandle.getSuitablePointSize(mFont, mPushButtonSOLOLeft.text(), mPushButtonSOLOLeft.rect()));
    mPushButtonSOLOLeft.setFont(mFont);
    mPushButtonSOLORight.setFont(mFont);
    mPushButtonMUTELeft.setFont(mFont);
    mPushButtonMUTERight.setFont(mFont);
    mFont.setPointSize(mFont.pointSize() + 2);
    mPushButtonANTILeft.setFont(mFont);
    mPushButtonANTIRight.setFont(mFont);
    mRadius = hPushButton * 0.8;
    setPushButtonStateSOLOLeft(mPushButtonStateSOLOLeft);
    setPushButtonStateSOLORight(mPushButtonStateSOLORight);
    setPushButtonStateMUTELeft(mPushButtonStateMUTELeft);
    setPushButtonStateMUTERight(mPushButtonStateMUTERight);
    setPushButtonStateANTILeft(mPushButtonStateANTILeft);
    setPushButtonStateANTIRight(mPushButtonStateANTIRight);
}


// slot
void PushButtonS1M13::in_mPushButtonSOLOLeft_clicked()
{
    QString style;
    mPushButtonStateSOLOLeft = !mPushButtonStateSOLOLeft;
    if(mPushButtonStateSOLOLeft)
    {
        style = QString("QPushButton {"
                "   border-radius: %1px;"
                "   color: rgb(229, 229, 229);"
                "   background-color: rgb(217, 169, 61);"
                "}"
                "QPushButton:hover {"
                "   border: 2px solid gray;"
                "   border-radius: %1px;"
                "}").arg(mRadius);
    }
    else
    {
        style = QString("QPushButton {"
                "   border-radius: %1px;"
                "   color: rgb(161, 161, 161);"
                "   background-color: rgb(60, 60, 60);"
                "}"
                "QPushButton:hover {"
                "   border: 2px solid gray;"
                "   border-radius: %1px;"
                "}").arg(mRadius);
    }
    mPushButtonSOLOLeft.setStyleSheet(style);
    emit buttonStateChanged(buttonSOLOLeft, mPushButtonStateSOLOLeft);
}
void PushButtonS1M13::in_mPushButtonSOLORight_clicked()
{
    QString style;
    mPushButtonStateSOLORight = !mPushButtonStateSOLORight;
    if(mPushButtonStateSOLORight)
    {
        style = QString("QPushButton {"
                "   border-radius: %1px;"
                "   color: rgb(229, 229, 229);"
                "   background-color: rgb(217, 169, 61);"
                "}"
                "QPushButton:hover {"
                "   border: 2px solid gray;"
                "   border-radius: %1px;"
                "}").arg(mRadius);
    }
    else
    {
        style = QString("QPushButton {"
                "   border-radius: %1px;"
                "   color: rgb(161, 161, 161);"
                "   background-color: rgb(60, 60, 60);"
                "}"
                "QPushButton:hover {"
                "   border: 2px solid gray;"
                "   border-radius: %1px;"
                "}").arg(mRadius);
    }
    mPushButtonSOLORight.setStyleSheet(style);
    emit buttonStateChanged(buttonSOLORight, mPushButtonStateSOLORight);
}
void PushButtonS1M13::in_mPushButtonMUTELeft_clicked()
{
    QString style;
    mPushButtonStateMUTELeft = !mPushButtonStateMUTELeft;
    if(mPushButtonStateMUTELeft)
    {
        style = QString("QPushButton {"
                "   border-radius: %1px;"
                "   color: rgb(229, 229, 229);"
                "   background-color: rgb(149, 40, 37);"
                "}"
                "QPushButton:hover {"
                "   border: 2px solid gray;"
                "   border-radius: %1px;"
                "}").arg(mRadius);
    }
    else
    {
        style = QString("QPushButton {"
                "   border-radius: %1px;"
                "   color: rgb(161, 161, 161);"
                "   background-color: rgb(60, 60, 60);"
                "}"
                "QPushButton:hover {"
                "   border: 2px solid gray;"
                "   border-radius: %1px;"
                "}").arg(mRadius);
    }
    mPushButtonMUTELeft.setStyleSheet(style);
    emit buttonStateChanged(buttonMUTELeft, mPushButtonStateMUTELeft);
}
void PushButtonS1M13::in_mPushButtonMUTERight_clicked()
{
    QString style;
    mPushButtonStateMUTERight = !mPushButtonStateMUTERight;
    if(mPushButtonStateMUTERight)
    {
        style = QString("QPushButton {"
                "   border-radius: %1px;"
                "   color: rgb(229, 229, 229);"
                "   background-color: rgb(149, 40, 37);"
                "}"
                "QPushButton:hover {"
                "   border: 2px solid gray;"
                "   border-radius: %1px;"
                "}").arg(mRadius);
    }
    else
    {
        style = QString("QPushButton {"
                "   border-radius: %1px;"
                "   color: rgb(161, 161, 161);"
                "   background-color: rgb(60, 60, 60);"
                "}"
                "QPushButton:hover {"
                "   border: 2px solid gray;"
                "   border-radius: %1px;"
                "}").arg(mRadius);
    }
    mPushButtonMUTERight.setStyleSheet(style);
    emit buttonStateChanged(buttonMUTERight, mPushButtonStateMUTERight);
}
void PushButtonS1M13::in_mPushButtonANTILeft_clicked()
{
    QString style;
    mPushButtonStateANTILeft = !mPushButtonStateANTILeft;
    if(mPushButtonStateANTILeft)
    {
        style = QString("QPushButton {"
                "   border-radius: %1px;"
                "   color: rgb(229, 229, 229);"
                "   background-color: rgb(104, 188, 212);"
                "}"
                "QPushButton:hover {"
                "   border: 2px solid gray;"
                "   border-radius: %1px;"
                "}").arg(mRadius);
    }
    else
    {
        style = QString("QPushButton {"
                "   border-radius: %1px;"
                "   color: rgb(161, 161, 161);"
                "   background-color: rgb(60, 60, 60);"
                "}"
                "QPushButton:hover {"
                "   border: 2px solid gray;"
                "   border-radius: %1px;"
                "}").arg(mRadius);
    }
    mPushButtonANTILeft.setStyleSheet(style);
    emit buttonStateChanged(buttonANTILeft, mPushButtonStateANTILeft);
}
void PushButtonS1M13::in_mPushButtonANTIRight_clicked()
{
    QString style;
    mPushButtonStateANTIRight = !mPushButtonStateANTIRight;
    if(mPushButtonStateANTIRight)
    {
        style = QString("QPushButton {"
                "   border-radius: %1px;"
                "   color: rgb(229, 229, 229);"
                "   background-color: rgb(104, 188, 212);"
                "}"
                "QPushButton:hover {"
                "   border: 2px solid gray;"
                "   border-radius: %1px;"
                "}").arg(mRadius);
    }
    else
    {
        style = QString("QPushButton {"
                "   border-radius: %1px;"
                "   color: rgb(161, 161, 161);"
                "   background-color: rgb(60, 60, 60);"
                "}"
                "QPushButton:hover {"
                "   border: 2px solid gray;"
                "   border-radius: %1px;"
                "}").arg(mRadius);
    }
    mPushButtonANTIRight.setStyleSheet(style);
    emit buttonStateChanged(buttonANTIRight, mPushButtonStateANTIRight);
}


// setter & getter
PushButtonS1M13& PushButtonS1M13::setFont(QFont font)
{
    mFont = font;
    QResizeEvent e(size(), size());
    resizeEvent(&e);
    update();
    return *this;
}
PushButtonS1M13& PushButtonS1M13::setPushButtonWeightWidth(int weight)
{
    mWeightWidth = weight;
    QResizeEvent e(size(), size());
    resizeEvent(&e);
    update();
    return *this;
}
PushButtonS1M13& PushButtonS1M13::setPushButtonStateSOLOLeft(bool state)
{
    QString style;
    mPushButtonStateSOLOLeft = state;
    if(mPushButtonStateSOLOLeft)
    {
        style = QString("QPushButton {"
                "   border-radius: %1px;"
                "   color: rgb(229, 229, 229);"
                "   background-color: rgb(217, 169, 61);"
                "}"
                "QPushButton:hover {"
                "   border: 2px solid gray;"
                "   border-radius: %1px;"
                "}").arg(mRadius);
    }
    else
    {
        style = QString("QPushButton {"
                "   border-radius: %1px;"
                "   color: rgb(161, 161, 161);"
                "   background-color: rgb(60, 60, 60);"
                "}"
                "QPushButton:hover {"
                "   border: 2px solid gray;"
                "   border-radius: %1px;"
                "}").arg(mRadius);
    }
    mPushButtonSOLOLeft.setStyleSheet(style);
    return *this;
}
PushButtonS1M13& PushButtonS1M13::setPushButtonStateSOLORight(bool state)
{
    QString style;
    mPushButtonStateSOLORight = state;
    if(mPushButtonStateSOLORight)
    {
        style = QString("QPushButton {"
                "   border-radius: %1px;"
                "   color: rgb(229, 229, 229);"
                "   background-color: rgb(217, 169, 61);"
                "}"
                "QPushButton:hover {"
                "   border: 2px solid gray;"
                "   border-radius: %1px;"
                "}").arg(mRadius);
    }
    else
    {
        style = QString("QPushButton {"
                "   border-radius: %1px;"
                "   color: rgb(161, 161, 161);"
                "   background-color: rgb(60, 60, 60);"
                "}"
                "QPushButton:hover {"
                "   border: 2px solid gray;"
                "   border-radius: %1px;"
                "}").arg(mRadius);
    }
    mPushButtonSOLORight.setStyleSheet(style);
    return *this;
}
PushButtonS1M13& PushButtonS1M13::setPushButtonStateMUTELeft(bool state)
{
    QString style;
    mPushButtonStateMUTELeft = state;
    if(mPushButtonStateMUTELeft)
    {
        style = QString("QPushButton {"
                "   border-radius: %1px;"
                "   color: rgb(229, 229, 229);"
                "   background-color: rgb(149, 40, 37);"
                "}"
                "QPushButton:hover {"
                "   border: 2px solid gray;"
                "   border-radius: %1px;"
                "}").arg(mRadius);
    }
    else
    {
        style = QString("QPushButton {"
                "   border-radius: %1px;"
                "   color: rgb(161, 161, 161);"
                "   background-color: rgb(60, 60, 60);"
                "}"
                "QPushButton:hover {"
                "   border: 2px solid gray;"
                "   border-radius: %1px;"
                "}").arg(mRadius);
    }
    mPushButtonMUTELeft.setStyleSheet(style);
    return *this;
}
PushButtonS1M13& PushButtonS1M13::setPushButtonStateMUTERight(bool state)
{
    QString style;
    mPushButtonStateMUTERight = state;
    if(mPushButtonStateMUTERight)
    {
        style = QString("QPushButton {"
                "   border-radius: %1px;"
                "   color: rgb(229, 229, 229);"
                "   background-color: rgb(149, 40, 37);"
                "}"
                "QPushButton:hover {"
                "   border: 2px solid gray;"
                "   border-radius: %1px;"
                "}").arg(mRadius);
    }
    else
    {
        style = QString("QPushButton {"
                "   border-radius: %1px;"
                "   color: rgb(161, 161, 161);"
                "   background-color: rgb(60, 60, 60);"
                "}"
                "QPushButton:hover {"
                "   border: 2px solid gray;"
                "   border-radius: %1px;"
                "}").arg(mRadius);
    }
    mPushButtonMUTERight.setStyleSheet(style);
    return *this;
}
PushButtonS1M13& PushButtonS1M13::setPushButtonStateANTILeft(bool state)
{
    QString style;
    mPushButtonStateANTILeft = state;
    if(mPushButtonStateANTILeft)
    {
        style = QString("QPushButton {"
                "   border-radius: %1px;"
                "   color: rgb(229, 229, 229);"
                "   background-color: rgb(104, 188, 212);"
                "}"
                "QPushButton:hover {"
                "   border: 2px solid gray;"
                "   border-radius: %1px;"
                "}").arg(mRadius);
    }
    else
    {
        style = QString("QPushButton {"
                "   border-radius: %1px;"
                "   color: rgb(161, 161, 161);"
                "   background-color: rgb(60, 60, 60);"
                "}"
                "QPushButton:hover {"
                "   border: 2px solid gray;"
                "   border-radius: %1px;"
                "}").arg(mRadius);
    }
    mPushButtonANTILeft.setStyleSheet(style);
    return *this;
}
PushButtonS1M13& PushButtonS1M13::setPushButtonStateANTIRight(bool state)
{
    QString style;
    mPushButtonStateANTIRight = state;
    if(mPushButtonStateANTIRight)
    {
        style = QString("QPushButton {"
                "   border-radius: %1px;"
                "   color: rgb(229, 229, 229);"
                "   background-color: rgb(104, 188, 212);"
                "}"
                "QPushButton:hover {"
                "   border: 2px solid gray;"
                "   border-radius: %1px;"
                "}").arg(mRadius);
    }
    else
    {
        style = QString("QPushButton {"
                "   border-radius: %1px;"
                "   color: rgb(161, 161, 161);"
                "   background-color: rgb(60, 60, 60);"
                "}"
                "QPushButton:hover {"
                "   border: 2px solid gray;"
                "   border-radius: %1px;"
                "}").arg(mRadius);
    }
    mPushButtonANTIRight.setStyleSheet(style);
    return *this;
}
PushButtonS1M13& PushButtonS1M13::setPushButtonClickedSOLOLeft(bool state)
{
    mPushButtonStateSOLOLeft = !state;
    in_mPushButtonSOLOLeft_clicked();
    return *this;
}
PushButtonS1M13& PushButtonS1M13::setPushButtonClickedSOLORight(bool state)
{
    mPushButtonStateSOLORight = !state;
    in_mPushButtonSOLORight_clicked();
    return *this;
}
PushButtonS1M13& PushButtonS1M13::setPushButtonClickedMUTELeft(bool state)
{
    mPushButtonStateMUTELeft = !state;
    in_mPushButtonMUTELeft_clicked();
    return *this;
}
PushButtonS1M13& PushButtonS1M13::setPushButtonClickedMUTERight(bool state)
{
    mPushButtonStateMUTERight = !state;
    in_mPushButtonMUTERight_clicked();
    return *this;
}
PushButtonS1M13& PushButtonS1M13::setPushButtonClickedANTILeft(bool state)
{
    mPushButtonStateANTILeft = !state;
    in_mPushButtonANTILeft_clicked();
    return *this;
}
PushButtonS1M13& PushButtonS1M13::setPushButtonClickedANTIRight(bool state)
{
    mPushButtonStateANTIRight = !state;
    in_mPushButtonANTIRight_clicked();
    return *this;
}
bool PushButtonS1M13::getPushButtonStateSOLOLeft()
{
    return mPushButtonStateSOLOLeft;
}
bool PushButtonS1M13::getPushButtonStateSOLORight()
{
    return mPushButtonStateSOLORight;
}
bool PushButtonS1M13::getPushButtonStateMUTELeft()
{
    return mPushButtonStateMUTELeft;
}
bool PushButtonS1M13::getPushButtonStateMUTERight()
{
    return mPushButtonStateMUTERight;
}
bool PushButtonS1M13::getPushButtonStateANTILeft()
{
    return mPushButtonStateANTILeft;
}
bool PushButtonS1M13::getPushButtonStateANTIRight()
{
    return mPushButtonStateANTIRight;
}

