#ifndef CHART_H
#define CHART_H


#include <QTimer>
#include <QtCharts>
#include <QGraphicsLineItem>
#include <QGraphicsRectItem>


class Chart : public QChartView
{
    Q_OBJECT
public:
    explicit Chart(QWidget* parent=nullptr);
    ~Chart();
    // Enum
    enum ChartAxis
    {
        axisXT=0,
        axisXB,
        axisYL,
        axisYR,
        axisEnd
    };
    // Chart
    Chart& setChartDefaultEqualizer();
    Chart& setChartFont(QFont font);
    Chart& setChartColor(QColor color);
    Chart& setChartPlotAreaColor(QColor color);
    Chart& setChartGridVisible(bool visible=true);
    Chart& setChartGridVisible(ChartAxis axis, bool visible=true);
    Chart& setChartAxisVisible(bool visible=true);
    Chart& setChartAxisVisible(ChartAxis axis, bool visible=true);
    Chart& setChartRectFrameVisible(bool visible=true);
    Chart& setChartRectFrameColor(QColor color);
    Chart& setChartRectFrameWidth(int width);
    // Axis
    Chart& setAxis(ChartAxis axis, QAbstractAxis* abstractAxis);
    QAbstractAxis& getAxis(ChartAxis axis);
    // Handle
    Chart& addHandle(QString name, QPointF point, const QString& imageFile);
    Chart& removeHandle(QString name);
    Chart& setHandlePosition(QString name, QPointF point);
    Chart& setHandleVisible(QString name, bool visible=true);
    Chart& setHandleAxisVisible(QString name, bool visible=true);
    Chart& setHandleAxisVisibleX(QString name, bool visible=true);
    Chart& setHandleAxisVisibleY(QString name, bool visible=true);
    Chart& setHandleAxisStyle(QString name, Qt::PenStyle style);
    Chart& setHandleAxisStyleX(QString name, Qt::PenStyle style);
    Chart& setHandleAxisStyleY(QString name, Qt::PenStyle style);
    Chart& setHandleAxisWidth(QString name, int width);
    Chart& setHandleAxisWidthX(QString name, int width);
    Chart& setHandleAxisWidthY(QString name, int width);
    Chart& setHandleAxisColor(QString name, QColor color);
    Chart& setHandleAxisColorX(QString name, QColor color);
    Chart& setHandleAxisColorY(QString name, QColor color);
    Chart& setHandleAxisLock(QString name, bool lock=true);
    Chart& setHandleAxisLockX(QString name, bool lock=true);
    Chart& setHandleAxisLockY(QString name, bool lock=true);
    // ScrollLineSeries
    Chart& addScrollLineSeries(QString name, qreal defaultData);
    Chart& removeScrollLineSeries(QString name);
    Chart& addScrollLineSeriesData(QString name, qreal data);
    Chart& clearScrollLineSeries(QString name);
    Chart& setScrollLineSeriesStart(QString name);
    Chart& setScrollLineSeriesStop(QString name);
    Chart& setScrollLineSeriesWidth(QString name, int width);
    Chart& setScrollLineSeriesColor(QString name, QColor color);
    Chart& setScrollLineSeriesRefreshParam(QString name, qreal stepValue, int nHz); //stepValue(0, 1]  nHz[1, 1000/(1/stepValue)]  stepValue需要对1整除
    qreal getScrollLineSeriesData(QString name);
    // ScrollSplineSeries
    Chart& addScrollSplineSeries(QString name, qreal defaultData);
    Chart& removeScrollSplineSeries(QString name);
    Chart& addScrollSplineSeriesData(QString name, qreal data);
    Chart& clearScrollSplineSeries(QString name);
    Chart& setScrollSplineSeriesStart(QString name);
    Chart& setScrollSplineSeriesStop(QString name);
    Chart& setScrollSplineSeriesWidth(QString name, int width);
    Chart& setScrollSplineSeriesColor(QString name, QColor color);
    Chart& setScrollSplineSeriesRefreshParam(QString name, qreal stepValue, int nHz); //stepValue(0, 1]  nHz[1, 1000/(1/stepValue)]  stepValue需要对1整除
    qreal getScrollSplineSeriesData(QString name);
    // ScrollAreaSeries
    Chart& addScrollAreaSeries(QString name, qreal defaultData);
    Chart& removeScrollAreaSeries(QString name);
    Chart& addScrollAreaSeriesData(QString name, qreal data);
    Chart& clearScrollAreaSeries(QString name);
    Chart& setScrollAreaSeriesStart(QString name);
    Chart& setScrollAreaSeriesStop(QString name);
    Chart& setScrollAreaSeriesWidth(QString name, int width);
    Chart& setScrollAreaSeriesColor(QString name, QColor color);
    Chart& setScrollAreaSeriesRefreshParam(QString name, qreal stepValue, int nHz); //stepValue(0, 1]  nHz[1, 1000/(1/stepValue)]  stepValue需要对1整除
    qreal getScrollAreaSeriesData(QString name);
    // SplineSeries
    Chart& addSplineSeries(QString name, QColor color, qreal width=2, Qt::PenStyle style=Qt::SolidLine);
    Chart& removeSplineSeries(QString name);
    Chart& modifySplineSeriesData(QString name, QVector<QPointF>& data);
    Chart& clearSplineSeries(QString name);
    Chart& setSplineSeriesWidth(QString name, int width);
    Chart& setSplineSeriesColor(QString name, QColor color);
    Chart& setSplineSeriesStyle(QString name, Qt::PenStyle style=Qt::SolidLine);
    Chart& setSplineSeriesVisible(QString name, bool visible=true);
protected:
    void mousePressEvent(QMouseEvent* e) override;
    void mouseMoveEvent(QMouseEvent* e) override;
    void mouseReleaseEvent(QMouseEvent* e) override;
    void paintEvent(QPaintEvent* e) override;
private:
    // Struct
    struct CustomHandle
    {
        QScatterSeries point;
        QLineSeries axisX;
        QLineSeries axisY;
    };
    struct ScrollLineSeries
    {
        QLineSeries series;
        QTimer timer;
    };
    struct ScrollSplineSeries
    {
        QSplineSeries series;
        QTimer timer;
    };
    struct ScrollAreaSeries
    {
        QAreaSeries series;
        QTimer timer;
    };
    // Property
    QChart mChart;
    QFont mFont;
    QGraphicsRectItem mRectFrame;
    QMap<QString, CustomHandle*> mHandle;
    bool mHandleIsMoving;
    float mHandleMovingStep;
    QString mMovingHandle;
    QMap<QString, ScrollLineSeries*> mScrollLineSeries;
    QMap<QString, ScrollSplineSeries*> mScrollSplineSeries;
    QMap<QString, ScrollAreaSeries*> mScrollAreaSeries;
    QMap<QString, QSplineSeries*> mSplineSeries;
private slots:
    void in_ScrollLineSeriesTimers_timeout();
    void in_ScrollSplineSeriesTimers_timeout();
    void in_ScrollAreaSeriesTimers_timeout();
signals:
    void handleMovement(QString name, QPointF point);
};


#endif // CHART_H

