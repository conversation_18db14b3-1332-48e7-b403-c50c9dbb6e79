#include "globalfont.h"
#include "pushbuttons1m12.h"


PushButtonS1M12::PushButtonS1M12(QWidget* parent)
    : QWidget(parent)
{
    mPushButton48V.setParent(this);
    mPushButtonAUTO.setParent(this);
    QString style;
    style = "QPushButton {"
            "   border-radius: 3px;"
            "   color: rgb(161, 161, 161);"
            "   background-color: rgb(60, 60, 60);"
            "}"
            "QPushButton:hover {"
            "   border: 2px solid gray;"
            "   border-radius: 3px;"
            "}";
    mPushButton48V.setStyleSheet(style);
    mPushButtonAUTO.setStyleSheet(style);
    mPushButton48V.setText("48V");
    mPushButtonAUTO.setText("AUTO");
    connect(&mPushButton48V, SIGNAL(clicked()), this, SLOT(in_mPushButton48V_clicked()), Qt::UniqueConnection);
    connect(&mPushButtonAUTO, SIGNAL(clicked()), this, SLOT(in_mPushButtonAUTO_clicked()), Qt::UniqueConnection);
}
PushButtonS1M12::~PushButtonS1M12()
{

}


// override
void PushButtonS1M12::resizeEvent(QResizeEvent* e)
{
    Q_UNUSED(e);
    // W
    // float wPixelPerRatio=size().width() / 100.0;
    // int wLabel=wPixelPerRatio * 6;
    // int wSpace1=wPixelPerRatio * 6;
    int wPushButton=size().width();
    int xLabel=0;
    // H
    float hPixelPerRatio=size().height() / 100.0;
    int hSpace1=hPixelPerRatio * 15;
    int hSpace2=hPixelPerRatio * 3;
    int hSpace3=hPixelPerRatio * 10;
    int hSpace4=hPixelPerRatio * 5;
    int hPushButton=(size().height() - hSpace1 - hSpace2 * 3 - hSpace3 - hSpace4) / 6;
    mPushButton48V.setGeometry(xLabel, hSpace1 + hSpace2 * 2 + hSpace3 + hPushButton * 3, wPushButton, hPushButton);
    mPushButtonAUTO.setGeometry(xLabel, hSpace1 + hSpace2 * 3 + hSpace3 + hPushButton * 4, wPushButton, hPushButton);
    mFont.setPointSize(GLBFHandle.getSuitablePointSize(mFont, mPushButtonAUTO.text(), mPushButtonAUTO.rect()) - 1);
    mPushButton48V.setFont(mFont);
    mPushButtonAUTO.setFont(mFont);
    mRadius = hPushButton / 2 * 0.8;
    setPushButtonState48V(mPushButtonState48V);
    setPushButtonStateAUTO(mPushButtonStateAUTO);
}


// slot
void PushButtonS1M12::in_mPushButton48V_clicked()
{
    QString style;
    mPushButtonState48V = !mPushButtonState48V;
    if(mPushButtonState48V)
    {
        style = QString("QPushButton {"
                        "   border-radius: %1px;"
                        "   color: rgb(229, 229, 229);"
                        "   background-color: rgb(234, 78, 80);"
                        "}"
                        "QPushButton:hover {"
                        "   border: 2px solid gray;"
                        "   border-radius: %1px;"
                        "}").arg(mRadius);
    }
    else
    {
        style = QString("QPushButton {"
                        "   border-radius: %1px;"
                        "   color: rgb(161, 161, 161);"
                        "   background-color: rgb(60, 60, 60);"
                        "}"
                        "QPushButton:hover {"
                        "   border: 2px solid gray;"
                        "   border-radius: %1px;"
                        "}").arg(mRadius);
    }
    mPushButton48V.setStyleSheet(style);
    emit buttonStateChanged(button48V, mPushButtonState48V);
}
void PushButtonS1M12::in_mPushButtonAUTO_clicked()
{
    QString style;
    mPushButtonStateAUTO = !mPushButtonStateAUTO;
    if(mPushButtonStateAUTO)
    {
        style = QString("QPushButton {"
                        "   border-radius: %1px;"
                        "   color: rgb(229, 229, 229);"
                        "   background-color: rgb(129, 171, 84);"
                        "}"
                        "QPushButton:hover {"
                        "   border: 2px solid gray;"
                        "   border-radius: %1px;"
                        "}").arg(mRadius);
    }
    else
    {
        style = QString("QPushButton {"
                        "   border-radius: %1px;"
                        "   color: rgb(161, 161, 161);"
                        "   background-color: rgb(60, 60, 60);"
                        "}"
                        "QPushButton:hover {"
                        "   border: 2px solid gray;"
                        "   border-radius: %1px;"
                        "}").arg(mRadius);
    }
    mPushButtonAUTO.setStyleSheet(style);
    emit buttonStateChanged(buttonAUTO, mPushButtonStateAUTO);
}


// setter & getter
PushButtonS1M12& PushButtonS1M12::setFont(QFont font)
{
    mFont = font;
    QResizeEvent e(size(), size());
    resizeEvent(&e);
    update();
    return *this;
}
PushButtonS1M12& PushButtonS1M12::setPushButtonState48V(bool state)
{
    QString style;
    mPushButtonState48V = state;
    if(mPushButtonState48V)
    {
        style = QString("QPushButton {"
                        "   border-radius: %1px;"
                        "   color: rgb(229, 229, 229);"
                        "   background-color: rgb(234, 78, 80);"
                        "}"
                        "QPushButton:hover {"
                        "   border: 2px solid gray;"
                        "   border-radius: %1px;"
                        "}").arg(mRadius);
    }
    else
    {
        style = QString("QPushButton {"
                        "   border-radius: %1px;"
                        "   color: rgb(161, 161, 161);"
                        "   background-color: rgb(60, 60, 60);"
                        "}"
                        "QPushButton:hover {"
                        "   border: 2px solid gray;"
                        "   border-radius: %1px;"
                        "}").arg(mRadius);
    }
    mPushButton48V.setStyleSheet(style);
    return *this;
}
PushButtonS1M12& PushButtonS1M12::setPushButtonStateAUTO(bool state)
{
    QString style;
    mPushButtonStateAUTO = state;
    if(mPushButtonStateAUTO)
    {
        style = QString("QPushButton {"
                        "   border-radius: %1px;"
                        "   color: rgb(229, 229, 229);"
                        "   background-color: rgb(129, 171, 84);"
                        "}"
                        "QPushButton:hover {"
                        "   border: 2px solid gray;"
                        "   border-radius: %1px;"
                        "}").arg(mRadius);
    }
    else
    {
        style = QString("QPushButton {"
                        "   border-radius: %1px;"
                        "   color: rgb(161, 161, 161);"
                        "   background-color: rgb(60, 60, 60);"
                        "}"
                        "QPushButton:hover {"
                        "   border: 2px solid gray;"
                        "   border-radius: %1px;"
                        "}").arg(mRadius);
    }
    mPushButtonAUTO.setStyleSheet(style);
    return *this;
}
bool PushButtonS1M12::getPushButtonState48V()
{
    return mPushButtonState48V;
}
bool PushButtonS1M12::getPushButtonStateAUTO()
{
    return mPushButtonStateAUTO;
}

