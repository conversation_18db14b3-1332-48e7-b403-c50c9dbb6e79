/************************************************************************
 *
 *  Module:       TbUtils.h
 *  Description:  generic utilities
 *
 *  Runtime Env.: any
 *  Author(s):    <PERSON><PERSON>, <PERSON>
 *  Company:      Thesycon GmbH, Ilmenau
 ************************************************************************/

#ifndef __TbUtils_h__
#define __TbUtils_h__

// optionally put everything into a namespace
#ifdef LIBTB_NAMESPACE
namespace LIBTB_NAMESPACE {
#endif


//
// issue a compile time warning message,
// formatted properly so that VS and the WDK build utility can interpret it.
// usage example:
// #ifdef FOO
// #pragma TB_COMPILE_TIME_WARNING("FOO active.")
// #endif
//
#define TB_COMPILE_TIME_WARNING(txt) message(__FILE__ "(" TB_TO_STRING(__LINE__) ")" " : warning : " txt)



// test status value for SUCCESS
#define SUCC(status)  ( (status)==0 )


// this macro takes a pointer and its type and compares the Magic field with a constant
#define PTRVALID(ptr,type)  ( (ptr)!=nullptr && (ptr)->Magic==type##_MAGIC )



// handy macro to enable code in debug builds only
#if TB_DEBUG
#define IFDBG(x) { x; }
#else
#define IFDBG(x)
#endif


// shortcut macro for new (std::nothrow)
// new operator returns nullptr pointer if allocation failed.
#define TB_NEW new (std::nothrow)


// zero a given object
template<typename T>
inline
void
TbZeroObject(T& obj)
{
    memset((void*)&obj, 0, sizeof(T));
}

// minimum of two integer values
template<typename T>
inline
T
TbMin(T x, T y)
{
    return ( x < y ? x : y);
}

// maximum of two integer values
template<typename T>
inline
T
TbMax(T x, T y)
{
    return ( x > y ? x : y);
}


//
// logical XOR
//
//    a      b       return value
//  false  false     false
//  false  true      true
//  true   false     true
//  true   true      false
//
inline
bool
TbLogicalXor(bool a, bool b)
{
    return ( (!a && b) || (a && !b) );
}



// declare a private default constructor
#define PRIVATE_DEFAULT_CONSTRUCTOR(x)  private: x();

// declare a private copy constructor
#define PRIVATE_COPY_CONSTRUCTOR(x) private: x(const x&);

// declare a private move constructor
#define PRIVATE_MOVE_CONSTRUCTOR(x) private: x(x&&);

// declare a private assignment operator
#define PRIVATE_ASSIGNMENT_OPERATOR(x)  private: x& operator=(const x&);


// delete default constructor
#define DELETE_DEFAULT_CONSTRUCTOR(x) x() = delete;

// Since C++11 we can specify that a function is deleted (i.e. does not exist).
// By convention deleted functions should be declared public. This improves compiler error messages.
#define DELETE_COPY_CONSTRUCTOR(x) x(const x&) = delete;
#define DELETE_MOVE_CONSTRUCTOR(x) x(x&&) = delete;

#define DELETE_COPY_ASSIGNMENT_OPERATOR(x) x& operator=(const x&) = delete;
#define DELETE_MOVE_ASSIGNMENT_OPERATOR(x) x& operator=(x&&) = delete;




#ifdef LIBTB_NAMESPACE
}
#endif

#endif // __TbUtils_h__

/******************************** EOF ***********************************/
