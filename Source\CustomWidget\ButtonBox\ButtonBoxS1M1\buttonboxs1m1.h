#ifndef BUTTONBOXS1M1_H
#define BUTTONBOXS1M1_H


#include <QEvent>
#include <QTimer>
#include <QObject>
#include <QVector>
#include <QWidget>
#include <QPushButton>
#include <QResizeEvent>


namespace Ui {
class ButtonBoxS1M1;
}


class ButtonBoxS1M1 : public QWidget
{
    Q_OBJECT
public:
    explicit ButtonBoxS1M1(QWidget* parent=nullptr);
    ~ButtonBoxS1M1();
    ButtonBoxS1M1& setFont(QFont font);
    ButtonBoxS1M1& modifyButtonList(QVector<QString> list);
    ButtonBoxS1M1& setVisibleList(QVector<QString> list);
    ButtonBoxS1M1& setButtonVisible(QString button);
    ButtonBoxS1M1& setButtonWeight(int weightWidth, int weightHeight);
    int getVisibleCount() { return mVisibleCount; }
protected:
    bool eventFilter(QObject* obj, QEvent* e) override;
    void resizeEvent(QResizeEvent* e) override;
private:
    struct Button
    {
        bool visible;
        QPushButton button;
    };
    Ui::ButtonBoxS1M1* ui;
    QFont mFont;
    QPushButton mButtonAddition;
    QTimer mTimer;
    QVector<Button*> mButtonList;
    int mVisibleCount;
    QString mLongestButton="";
    int mWeightWidth=90;
    int mWeightHeight=20;
private slots:
    void in_mButtonAddition_clicked();
    void in_mTimer_timeout();
    void in_mButtonListAll_clicked();
signals:
    void attributeChanged(QString objectName, QString attribute, QString value);
};


#endif // BUTTONBOXS1M1_H

