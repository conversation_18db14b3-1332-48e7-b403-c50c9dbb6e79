#include "devicebase.h"


DeviceBase::~DeviceBase()
{
    stop();
    USBHHandle.closeDevice();
}


// override
void DeviceBase::run()
{
    while(!mStoped)
    {
        if(readFrame() == -1)
        {
            mStoped = true;
            emit deviceDisconnected();
            break;
        }
    }
}


// setter & getter
void DeviceBase::start()
{
    mStoped = false;
    QThread::start();
    mSender.start();
}
void DeviceBase::stop()
{
    mStoped = true;
    mSender.stop();
    QThread::quit();
    mSender.quit();
    QThread::wait();
    mSender.wait();
}
void DeviceBase::setDevice(unsigned int deviceVendorID, unsigned int deviceProductID)
{
    mVendorID = deviceVendorID;
    mProductID = deviceProductID;
}
void DeviceBase::sendPacket(QByteArray packet)
{
    mSender.send(packet);
}

