#include "globalfont.h"
#include "pushbuttons1m14.h"


PushButtonS1M14::PushButtonS1M14(QWidget* parent)
    : QWidget(parent)
{
    mPushButtonMUTELeft.setParent(this);
    mPushButtonMUTERight.setParent(this);
    mPushButtonMUTELeft.setText("MUTE");
    mPushButtonMUTERight.setText("MUTE");
    connect(&mPushButtonMUTELeft, SIGNAL(clicked()), this, SLOT(in_mPushButtonMUTELeft_clicked()), Qt::UniqueConnection);
    connect(&mPushButtonMUTERight, SIGNAL(clicked()), this, SLOT(in_mPushButtonMUTERight_clicked()), Qt::UniqueConnection);
}
PushButtonS1M14::~PushButtonS1M14()
{

}


// override
void PushButtonS1M14::resizeEvent(QResizeEvent* e)
{
    Q_UNUSED(e);
    // W
    float wPixelPerRatio=size().width() / 100.0;
    int wPushButton=wPixelPerRatio * mWeightWidth;
    int xPushButtonLeft=(size().width() - wPushButton * 2) / 4;
    int xPushButtonRight=size().width() / 2 + xPushButtonLeft;
    // H
    float hPushButton=size().height() / 2.0;
    mPushButtonMUTELeft.setGeometry(xPushButtonLeft, 0, wPushButton, hPushButton * 2);
    mPushButtonMUTERight.setGeometry(xPushButtonRight, 0, wPushButton, hPushButton * 2);
    mFont.setPointSize(GLBFHandle.getSuitablePointSize(mFont, mPushButtonMUTELeft.text(), mPushButtonMUTELeft.rect()));
    mPushButtonMUTELeft.setFont(mFont);
    mPushButtonMUTERight.setFont(mFont);
    mRadius = hPushButton * 0.8;
    setPushButtonStateMUTELeft(mPushButtonStateMUTELeft);
    setPushButtonStateMUTERight(mPushButtonStateMUTERight);
}


// slot
void PushButtonS1M14::in_mPushButtonMUTELeft_clicked()
{
    QString style;
    mPushButtonStateMUTELeft = !mPushButtonStateMUTELeft;
    if(mPushButtonStateMUTELeft)
    {
        style = QString("QPushButton {"
                        "   border-radius: %1px;"
                        "   color: rgb(229, 229, 229);"
                        "   background-color: rgb(149, 40, 37);"
                        "}"
                        "QPushButton:hover {"
                        "   border: 2px solid gray;"
                        "   border-radius: %1px;"
                        "}").arg(mRadius);
    }
    else
    {
        style = QString("QPushButton {"
                        "   border-radius: %1px;"
                        "   color: rgb(161, 161, 161);"
                        "   background-color: rgb(60, 60, 60);"
                        "}"
                        "QPushButton:hover {"
                        "   border: 2px solid gray;"
                        "   border-radius: %1px;"
                        "}").arg(mRadius);
    }
    mPushButtonMUTELeft.setStyleSheet(style);
    emit buttonStateChanged(buttonMUTELeft, mPushButtonStateMUTELeft);
}
void PushButtonS1M14::in_mPushButtonMUTERight_clicked()
{
    QString style;
    mPushButtonStateMUTERight = !mPushButtonStateMUTERight;
    if(mPushButtonStateMUTERight)
    {
        style = QString("QPushButton {"
                        "   border-radius: %1px;"
                        "   color: rgb(229, 229, 229);"
                        "   background-color: rgb(149, 40, 37);"
                        "}"
                        "QPushButton:hover {"
                        "   border: 2px solid gray;"
                        "   border-radius: %1px;"
                        "}").arg(mRadius);
    }
    else
    {
        style = QString("QPushButton {"
                        "   border-radius: %1px;"
                        "   color: rgb(161, 161, 161);"
                        "   background-color: rgb(60, 60, 60);"
                        "}"
                        "QPushButton:hover {"
                        "   border: 2px solid gray;"
                        "   border-radius: %1px;"
                        "}").arg(mRadius);
    }
    mPushButtonMUTERight.setStyleSheet(style);
    emit buttonStateChanged(buttonMUTERight, mPushButtonStateMUTERight);
}


// setter & getter
PushButtonS1M14& PushButtonS1M14::setFont(QFont font)
{
    mFont = font;
    QResizeEvent e(size(), size());
    resizeEvent(&e);
    update();
    return *this;
}
PushButtonS1M14& PushButtonS1M14::setPushButtonWeightWidth(int weight)
{
    mWeightWidth = weight;
    QResizeEvent e(size(), size());
    resizeEvent(&e);
    update();
    return *this;
}
PushButtonS1M14& PushButtonS1M14::setPushButtonStateMUTELeft(bool state)
{
    QString style;
    mPushButtonStateMUTELeft = state;
    if(mPushButtonStateMUTELeft)
    {
        style = QString("QPushButton {"
                        "   border-radius: %1px;"
                        "   color: rgb(229, 229, 229);"
                        "   background-color: rgb(149, 40, 37);"
                        "}"
                        "QPushButton:hover {"
                        "   border: 2px solid gray;"
                        "   border-radius: %1px;"
                        "}").arg(mRadius);
    }
    else
    {
        style = QString("QPushButton {"
                        "   border-radius: %1px;"
                        "   color: rgb(161, 161, 161);"
                        "   background-color: rgb(60, 60, 60);"
                        "}"
                        "QPushButton:hover {"
                        "   border: 2px solid gray;"
                        "   border-radius: %1px;"
                        "}").arg(mRadius);
    }
    mPushButtonMUTELeft.setStyleSheet(style);
    return *this;
}
PushButtonS1M14& PushButtonS1M14::setPushButtonStateMUTERight(bool state)
{
    QString style;
    mPushButtonStateMUTERight = state;
    if(mPushButtonStateMUTERight)
    {
        style = QString("QPushButton {"
                        "   border-radius: %1px;"
                        "   color: rgb(229, 229, 229);"
                        "   background-color: rgb(149, 40, 37);"
                        "}"
                        "QPushButton:hover {"
                        "   border: 2px solid gray;"
                        "   border-radius: %1px;"
                        "}").arg(mRadius);
    }
    else
    {
        style = QString("QPushButton {"
                        "   border-radius: %1px;"
                        "   color: rgb(161, 161, 161);"
                        "   background-color: rgb(60, 60, 60);"
                        "}"
                        "QPushButton:hover {"
                        "   border: 2px solid gray;"
                        "   border-radius: %1px;"
                        "}").arg(mRadius);
    }
    mPushButtonMUTERight.setStyleSheet(style);
    return *this;
}
PushButtonS1M14& PushButtonS1M14::setPushButtonClickedMUTELeft(bool state)
{
    mPushButtonStateMUTELeft = !state;
    in_mPushButtonMUTELeft_clicked();
    return *this;
}
PushButtonS1M14& PushButtonS1M14::setPushButtonClickedMUTERight(bool state)
{
    mPushButtonStateMUTERight = !state;
    in_mPushButtonMUTERight_clicked();
    return *this;
}
bool PushButtonS1M14::getPushButtonStateMUTELeft()
{
    return mPushButtonStateMUTELeft;
}
bool PushButtonS1M14::getPushButtonStateMUTERight()
{
    return mPushButtonStateMUTERight;
}

