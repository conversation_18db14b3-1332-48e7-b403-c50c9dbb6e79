/************************************************************************
 *  Module:       TUsbAudioApiDll.h
 *  Long name:    TUsbAudioApi.dll wrapper
 *  Description:  Implements an interface to load and unload the
 *                TUsbAudioApi.dll and to access its API.
 *  Runtime Env.: Win32, Win64
 *  Author(s):    Ren?M鰈ler
 *  Company:      Thesycon GmbH, Ilmenau
 ************************************************************************/

#ifndef __TUsbAudioApiDll_H__
#define __TUsbAudioApiDll_H__


// interface exported by TUSBAUDIO driver API DLL
#include "tusbaudioapi.h"


//default file name of the driver API DLL
#ifndef DEFAULT_DRV_API_DLL_FILE_NAME
#define DEFAULT_DRV_API_DLL_FILE_NAME   _T("tusbaudioapi.dll")
#endif


//
// This class implements an interface to dynamically load and unload
// the driver API DLL and to access API functions.
//
class TUsbAudioApiDll
{
// construction/destruction
public:
    //
    // default constructor
    //
    TUsbAudioApiDll();

    //
    // destructor
    //
    virtual
    ~TUsbAudioApiDll();

    // copy is not allowed
    TUsbAudioApiDll(const TUsbAudioApiDll&) = delete;
    TUsbAudioApiDll& operator=(TUsbAudioApiDll&&) = delete;


// methods
public:

    //
    // Load the DLL by full path and name.
    //
    // The function also checks whether the API version of
    // the loaded DLL is compatible with the application.
    //
    // parameters:
    //    pathAndName   path and file name of the DLL to load
    //    checkApiCompatibility  specifies whether API version will be checked or not
    //    expectedMajorApiVersion  
    //    expectedMinorApiVersion  the function checks if the DLL supports this API version or higher
    //
    // return:
    //    ERROR_SUCCESS if successful, an error code otherwise
    //
    DWORD
    LoadByPathAndName(
        const TCHAR* pathAndName,
        bool checkApiCompatibility = true,
        unsigned int expectedMajorApiVersion = TUSBAUDIO_API_VERSION_MJ,
        unsigned int expectedMinorApiVersion = TUSBAUDIO_API_VERSION_MN
        );

    //
    // Load the DLL by file name.
    // The specified DLL is expected to reside in the same directory as this application's .exe file.
    //
    // The function also checks whether the API version of
    // the loaded DLL is compatible with the application.
    //
    // parameters:
    //    pathAndName   path and file name of the DLL to load
    //    checkApiCompatibility  specifies whether API version will be checked or not
    //    expectedMajorApiVersion  
    //    expectedMinorApiVersion  the function checks if the DLL supports this API version or higher
    //
    // return:
    //    ERROR_SUCCESS if successful, an error code otherwise
    //
    DWORD
    LoadByName(
        const TCHAR* pathAndName,
        bool checkApiCompatibility = true,
        unsigned int expectedMajorApiVersion = TUSBAUDIO_API_VERSION_MJ,
        unsigned int expectedMinorApiVersion = TUSBAUDIO_API_VERSION_MN
        );


    //
    // Load the DLL by GUID.
    // The DLL path and file name will be taken from the registry
    // where it has been stored during driver installation.
    //
    // The function also checks whether the API version of
    // the loaded DLL is compatible with the application.
    //
    // parameters:
    //    guidString   The GUID that is specified as InterfaceGUID= in custom.ini,
    //                 or DRIVER_INTERFACE_GUID= in set_env.cmd, respectively.
    //                 Points to a null-terminated string,  e.g.: "{c200e360-38c5-11ce-ae62-08002b2b79ef}"
    //
    //    checkApiCompatibility  specifies whether API version will be checked or not
    //    expectedMajorApiVersion  
    //    expectedMinorApiVersion  the function checks if the DLL supports this API version or higher
    //
    // return:
    //    ERROR_SUCCESS if successful, an error code otherwise
    //
    DWORD
    LoadByGUID(
        const TCHAR* guidString,
        bool checkApiCompatibility = true,
        unsigned int expectedMajorApiVersion = TUSBAUDIO_API_VERSION_MJ,
        unsigned int expectedMinorApiVersion = TUSBAUDIO_API_VERSION_MN
        );


    //
    // Unload the currently loaded DLL. If no DLL is loaded the
    // method does nothing. Note that the method is also implicitly
    // called by the destructor.
    //
    void
    Unload();


    //
    // return true if currently a DLL is loaded, false otherwise
    //
    bool
    IsLoaded() const
        { return (mDllHandle != NULL); }

    unsigned int
    ApiMajorVersion() const
        { return (mDllVersion >> 16); }

    unsigned int
    ApiMinorVersion() const
        { return (mDllVersion & 0xFFFF); }

    bool
    IsApiVersionEqualOrGreater(
        unsigned int major,
        unsigned int minor
        ) const;


    //
    // functions that are available in all API versions
    //

    unsigned int
    TUSBAUDIO_GetApiVersion()
            {
                if (nullptr == mFunctionPointers.mTUSBAUDIO_GetApiVersion) { return 0; }
                return mFunctionPointers.mTUSBAUDIO_GetApiVersion();
            }

    int
    TUSBAUDIO_CheckApiVersion(
        unsigned int majorVersion,
        unsigned int minorVersion
        )
            {
                if (nullptr == mFunctionPointers.mTUSBAUDIO_CheckApiVersion) { return 0; }
                return mFunctionPointers.mTUSBAUDIO_CheckApiVersion(majorVersion, minorVersion);
            }

    //
    // functions that are available in API version 4.8 and higher
    //

    TUsbAudioStatus
    TUSBAUDIO_RegisterPnpNotification(
        HANDLE deviceArrivalEvent,  // optional, can be nullptr
        HANDLE deviceRemovedEvent,  // optional, can be nullptr
        void* windowHandle,         // optional, can be nullptr
        unsigned int windowMsgCode,
        unsigned int flags          // reserved for future use
        )
            {
                if (nullptr == mFunctionPointers.mTUSBAUDIO_RegisterPnpNotification) { return TSTATUS_NOT_SUPPORTED; }
                return mFunctionPointers.mTUSBAUDIO_RegisterPnpNotification(deviceArrivalEvent, deviceRemovedEvent, windowHandle, windowMsgCode, flags);
            }

    void
    TUSBAUDIO_UnregisterPnpNotification()
            {
                if (nullptr == mFunctionPointers.mTUSBAUDIO_UnregisterPnpNotification) { return; }
                mFunctionPointers.mTUSBAUDIO_UnregisterPnpNotification();
            }

    TUsbAudioStatus
    TUSBAUDIO_EnumerateDevices()
            {
                if (nullptr == mFunctionPointers.mTUSBAUDIO_EnumerateDevices) { return TSTATUS_NOT_SUPPORTED; }
                return mFunctionPointers.mTUSBAUDIO_EnumerateDevices();
            }

    unsigned int
    TUSBAUDIO_GetDeviceCount()
            {
                if (nullptr == mFunctionPointers.mTUSBAUDIO_GetDeviceCount) { return 0; }
                return mFunctionPointers.mTUSBAUDIO_GetDeviceCount();
            }

    TUsbAudioStatus
    TUSBAUDIO_OpenDeviceByIndex(
        unsigned int deviceIndex,
        TUsbAudioHandle* deviceHandle
        )
            {
                if (nullptr == mFunctionPointers.mTUSBAUDIO_OpenDeviceByIndex) { return TSTATUS_NOT_SUPPORTED; }
                return mFunctionPointers.mTUSBAUDIO_OpenDeviceByIndex(deviceIndex, deviceHandle);
            }

    TUsbAudioStatus
    TUSBAUDIO_OpenDeviceByChannelIdString(
        const char* channelIdString,
        TUsbAudioHandle* deviceHandle
        )
            {
                if (nullptr == mFunctionPointers.mTUSBAUDIO_OpenDeviceByChannelIdString) { return TSTATUS_NOT_SUPPORTED; }
                return mFunctionPointers.mTUSBAUDIO_OpenDeviceByChannelIdString(channelIdString, deviceHandle);
            }

    TUsbAudioStatus
    TUSBAUDIO_CloseDevice(
        TUsbAudioHandle deviceHandle
        )
            {
                if (nullptr == mFunctionPointers.mTUSBAUDIO_CloseDevice) { return TSTATUS_NOT_SUPPORTED; }
                return mFunctionPointers.mTUSBAUDIO_CloseDevice(deviceHandle);
            }

    TUsbAudioStatus
    TUSBAUDIO_GetDeviceProperties(
        TUsbAudioHandle deviceHandle,
        TUsbAudioDeviceProperties* properties
        )
            {
                if (nullptr == mFunctionPointers.mTUSBAUDIO_GetDeviceProperties) { return TSTATUS_NOT_SUPPORTED; }
                return mFunctionPointers.mTUSBAUDIO_GetDeviceProperties(deviceHandle, properties);
            }

    TUsbAudioStatus
    TUSBAUDIO_GetDeviceInstanceIdString(
        TUsbAudioHandle deviceHandle,
        WCHAR stringBuffer[],
        unsigned int stringBufferMaxChars
        )
            {
                if (nullptr == mFunctionPointers.mTUSBAUDIO_GetDeviceInstanceIdString) { return TSTATUS_NOT_SUPPORTED; }
                return mFunctionPointers.mTUSBAUDIO_GetDeviceInstanceIdString(deviceHandle, stringBuffer, stringBufferMaxChars);
            }

    TUsbAudioStatus
    TUSBAUDIO_GetDeviceContainerIdString(
        TUsbAudioHandle deviceHandle,
        WCHAR stringBuffer[],
        unsigned int stringBufferMaxChars
        )
            {
                if (nullptr == mFunctionPointers.mTUSBAUDIO_GetDeviceContainerIdString) { return TSTATUS_NOT_SUPPORTED; }
                return mFunctionPointers.mTUSBAUDIO_GetDeviceContainerIdString(deviceHandle, stringBuffer, stringBufferMaxChars);
            }

    TUsbAudioStatus
    TUSBAUDIO_GetChannelIndexForChannelIdString(
        TUsbAudioHandle deviceHandle,
        const char* channelIdString,
        unsigned int* inputChannel,        // 1 = input channel, 0 = output channel
        unsigned int* channelIndex
        )
            {
                if (nullptr == mFunctionPointers.mTUSBAUDIO_GetChannelIndexForChannelIdString) { return TSTATUS_NOT_SUPPORTED; }
                return mFunctionPointers.mTUSBAUDIO_GetChannelIndexForChannelIdString(deviceHandle, channelIdString, inputChannel, channelIndex);
            }

    TUsbAudioStatus
    TUSBAUDIO_GetChannelIdString(
        TUsbAudioHandle deviceHandle,
        unsigned int inputChannel,        // 1 = input channel, 0 = output channel
        unsigned int channelIndex,
        char* channelIdString,
        unsigned int channelIdMaxChars
        )
            {
                if (nullptr == mFunctionPointers.mTUSBAUDIO_GetChannelIdString) { return TSTATUS_NOT_SUPPORTED; }
                return mFunctionPointers.mTUSBAUDIO_GetChannelIdString(deviceHandle, inputChannel, channelIndex, channelIdString, channelIdMaxChars);
            }

    TUsbAudioStatus
    TUSBAUDIO_RegisterDeviceNotification(
        TUsbAudioHandle deviceHandle,
        unsigned int categoryFilter,  // filter: bitwise OR of TUSBAUDIO_NOTIFY_CATEGORY_xxx constants
        HANDLE sharedEvent,         // nullptr or auto-reset event, signaled when messages are available
        unsigned int flags          // reserved for future use, set to zero
        )
            {
                if (nullptr == mFunctionPointers.mTUSBAUDIO_RegisterDeviceNotification) { return TSTATUS_NOT_SUPPORTED; }
                return mFunctionPointers.mTUSBAUDIO_RegisterDeviceNotification(deviceHandle, categoryFilter, sharedEvent, flags);
            }

    TUsbAudioStatus
    TUSBAUDIO_ReadDeviceNotification(
        TUsbAudioHandle deviceHandle,
        TUsbAudioNotifyEvent* eventType,
        unsigned char* dataBuffer,
        unsigned int dataBufferSize,
        unsigned int* dataBytesReturned
        )
            {
                if (nullptr == mFunctionPointers.mTUSBAUDIO_ReadDeviceNotification) { return TSTATUS_NOT_SUPPORTED; }
                return mFunctionPointers.mTUSBAUDIO_ReadDeviceNotification(deviceHandle, eventType, dataBuffer, dataBufferSize, dataBytesReturned);
            }

    TUsbAudioStatus
    TUSBAUDIO_GetSupportedSampleRates(
        TUsbAudioHandle deviceHandle,
        unsigned int sampleRateMaxCount,
        unsigned int sampleRateArray[],
        unsigned int* sampleRateCount
        )
            {
                if (nullptr == mFunctionPointers.mTUSBAUDIO_GetSupportedSampleRates) { return TSTATUS_NOT_SUPPORTED; }
                return mFunctionPointers.mTUSBAUDIO_GetSupportedSampleRates(deviceHandle, sampleRateMaxCount, sampleRateArray, sampleRateCount);
            }

    TUsbAudioStatus
    TUSBAUDIO_GetCurrentSampleRate(
        TUsbAudioHandle deviceHandle,
        unsigned int* sampleRate
        )
            {
                if (nullptr == mFunctionPointers.mTUSBAUDIO_GetCurrentSampleRate) { return TSTATUS_NOT_SUPPORTED; }
                return mFunctionPointers.mTUSBAUDIO_GetCurrentSampleRate(deviceHandle, sampleRate);
            }

    TUsbAudioStatus
    TUSBAUDIO_SetSampleRate(
        TUsbAudioHandle deviceHandle,
        unsigned int sampleRate
        )
            {
                if (nullptr == mFunctionPointers.mTUSBAUDIO_SetSampleRate) { return TSTATUS_NOT_SUPPORTED; }
                return mFunctionPointers.mTUSBAUDIO_SetSampleRate(deviceHandle, sampleRate);
            }

    TUsbAudioStatus
    TUSBAUDIO_GetSupportedClockSources(
        TUsbAudioHandle deviceHandle,
        unsigned int clockSourceMaxCount,
        TUsbAudioClockSource clockSourceArray[],
        unsigned int* clockSourceCount
        )
            {
                if (nullptr == mFunctionPointers.mTUSBAUDIO_GetSupportedClockSources) { return TSTATUS_NOT_SUPPORTED; }
                return mFunctionPointers.mTUSBAUDIO_GetSupportedClockSources(deviceHandle, clockSourceMaxCount, clockSourceArray, clockSourceCount);
            }

    TUsbAudioStatus
    TUSBAUDIO_GetClockSourceStatus(
        TUsbAudioHandle deviceHandle,
        unsigned int clockSourceId,
        TUsbAudioClockSource* clockSource
        )
            {
                if (nullptr == mFunctionPointers.mTUSBAUDIO_GetClockSourceStatus) { return TSTATUS_NOT_SUPPORTED; }
                return mFunctionPointers.mTUSBAUDIO_GetClockSourceStatus(deviceHandle, clockSourceId, clockSource);
            }

    TUsbAudioStatus
    TUSBAUDIO_GetCurrentClockSource(
        TUsbAudioHandle deviceHandle,
        TUsbAudioClockSource* clockSource
        )
            {
                if (nullptr == mFunctionPointers.mTUSBAUDIO_GetCurrentClockSource) { return TSTATUS_NOT_SUPPORTED; }
                return mFunctionPointers.mTUSBAUDIO_GetCurrentClockSource(deviceHandle, clockSource);
            }

    TUsbAudioStatus
    TUSBAUDIO_SetCurrentClockSource(
        TUsbAudioHandle deviceHandle,
        unsigned int clockSourceId
        )
            {
                if (nullptr == mFunctionPointers.mTUSBAUDIO_SetCurrentClockSource) { return TSTATUS_NOT_SUPPORTED; }
                return mFunctionPointers.mTUSBAUDIO_SetCurrentClockSource(deviceHandle, clockSourceId);
            }

    TUsbAudioStatus
    TUSBAUDIO_GetSupportedStreamFormats(
        TUsbAudioHandle deviceHandle,
        unsigned int inputStream,   // 1 = input format, 0 = output format
        unsigned int formatMaxCount,
        TUsbAudioStreamFormat formatArray[],
        unsigned int* formatCount
        )
            {
                if (nullptr == mFunctionPointers.mTUSBAUDIO_GetSupportedStreamFormats) { return TSTATUS_NOT_SUPPORTED; }
                return mFunctionPointers.mTUSBAUDIO_GetSupportedStreamFormats(deviceHandle, inputStream, formatMaxCount, formatArray, formatCount);
            }

    TUsbAudioStatus
    TUSBAUDIO_GetCurrentStreamFormat(
        TUsbAudioHandle deviceHandle,
        unsigned int inputStream,   // 1 = input format, 0 = output format
        TUsbAudioStreamFormat* format
        )
            {
                if (nullptr == mFunctionPointers.mTUSBAUDIO_GetCurrentStreamFormat) { return TSTATUS_NOT_SUPPORTED; }
                return mFunctionPointers.mTUSBAUDIO_GetCurrentStreamFormat(deviceHandle, inputStream, format);
            }

    TUsbAudioStatus
    TUSBAUDIO_SetCurrentStreamFormat(
        TUsbAudioHandle deviceHandle,
        unsigned int inputStream,   // 1 = input format, 0 = output format
        unsigned int formatId
        )
            {
                if (nullptr == mFunctionPointers.mTUSBAUDIO_SetCurrentStreamFormat) { return TSTATUS_NOT_SUPPORTED; }
                return mFunctionPointers.mTUSBAUDIO_SetCurrentStreamFormat(deviceHandle, inputStream, formatId);
            }

    TUsbAudioStatus
    TUSBAUDIO_GetStreamFormatSelectionMode(
        TUsbAudioHandle deviceHandle,
        TUsbAudioStreamFormatSelectionMode* selectionMode
        )
            {
                if (nullptr == mFunctionPointers.mTUSBAUDIO_GetStreamFormatSelectionMode) { return TSTATUS_NOT_SUPPORTED; }
                return mFunctionPointers.mTUSBAUDIO_GetStreamFormatSelectionMode(deviceHandle, selectionMode);
            }

    TUsbAudioStatus
    TUSBAUDIO_SetSoundDeviceProfile(
        TUsbAudioHandle deviceHandle,
        TUsbAudioSetSoundDeviceProfile* soundDeviceProfileArray,
        unsigned int soundDeviceProfileCount
        )
            {
                if (nullptr == mFunctionPointers.mTUSBAUDIO_SetSoundDeviceProfile) { return TSTATUS_NOT_SUPPORTED; }
                return mFunctionPointers.mTUSBAUDIO_SetSoundDeviceProfile(deviceHandle, soundDeviceProfileArray, soundDeviceProfileCount);
            }

    TUsbAudioStatus
    TUSBAUDIO_SetVirtualSoundDeviceProfile(
        TUsbAudioHandle deviceHandle,
        TUsbAudioSetVirtualSoundDeviceProfile* soundDeviceProfileArray,
        unsigned int soundDeviceProfileCount
        )
            {
                if (nullptr == mFunctionPointers.mTUSBAUDIO_SetVirtualSoundDeviceProfile) { return TSTATUS_NOT_SUPPORTED; }
                return mFunctionPointers.mTUSBAUDIO_SetVirtualSoundDeviceProfile(deviceHandle, soundDeviceProfileArray, soundDeviceProfileCount);
            }

    TUsbAudioStatus
    TUSBAUDIO_GetControlPanelOptions(
        TUsbAudioHandle deviceHandle,
        TUsbAudioCplOptions* options
        )
            {
                if (nullptr == mFunctionPointers.mTUSBAUDIO_GetControlPanelOptions) { return TSTATUS_NOT_SUPPORTED; }
                return mFunctionPointers.mTUSBAUDIO_GetControlPanelOptions(deviceHandle, options);
            }

    TUsbAudioStatus
    TUSBAUDIO_GetStreamChannelCount(
        TUsbAudioHandle deviceHandle,
        TUsbAudioStreamChannelType channelType,
        unsigned int* channelCount
        )
            {
                if (nullptr == mFunctionPointers.mTUSBAUDIO_GetStreamChannelCount) { return TSTATUS_NOT_SUPPORTED; }
                return mFunctionPointers.mTUSBAUDIO_GetStreamChannelCount(deviceHandle, channelType, channelCount);
            }

    TUsbAudioStatus
    TUSBAUDIO_GetStreamChannelIds(
        TUsbAudioHandle deviceHandle,
        TUsbAudioStreamChannelType channelType,
        unsigned int channelIdMaxCount,
        TUsbAudioStreamChannelId channelIdArray[],
        unsigned int* channelIdCount
        )
            {
                if (nullptr == mFunctionPointers.mTUSBAUDIO_GetStreamChannelIds) { return TSTATUS_NOT_SUPPORTED; }
                return mFunctionPointers.mTUSBAUDIO_GetStreamChannelIds(deviceHandle, channelType, channelIdMaxCount, channelIdArray, channelIdCount);
            }

    TUsbAudioStatus
    TUSBAUDIO_EnablePeakMeters(
        TUsbAudioHandle deviceHandle,
        const TUsbAudioPeakMeterId peakMeterIdArray[],
        unsigned int peakMeterIdCount,
        unsigned int decayInterval
        )
            {
                if (nullptr == mFunctionPointers.mTUSBAUDIO_EnablePeakMeters) { return TSTATUS_NOT_SUPPORTED; }
                return mFunctionPointers.mTUSBAUDIO_EnablePeakMeters(deviceHandle, peakMeterIdArray, peakMeterIdCount, decayInterval);
            }

    TUsbAudioStatus
    TUSBAUDIO_DisablePeakMeters(
        TUsbAudioHandle deviceHandle,
        const TUsbAudioPeakMeterId peakMeterIdArray[],
        unsigned int peakMeterIdCount
        )
            {
                if (nullptr == mFunctionPointers.mTUSBAUDIO_DisablePeakMeters) { return TSTATUS_NOT_SUPPORTED; }
                return mFunctionPointers.mTUSBAUDIO_DisablePeakMeters(deviceHandle, peakMeterIdArray, peakMeterIdCount);
            }

    TUsbAudioStatus
    TUSBAUDIO_GetPeakMeters(
        TUsbAudioHandle deviceHandle,
        const TUsbAudioPeakMeterId peakMeterIdArray[],
        TUsbAudioPeakMeterData peakMeterDataArray[],
        unsigned int numArrayElements
        )
            {
                if (nullptr == mFunctionPointers.mTUSBAUDIO_GetPeakMeters) { return TSTATUS_NOT_SUPPORTED; }
                return mFunctionPointers.mTUSBAUDIO_GetPeakMeters(deviceHandle, peakMeterIdArray, peakMeterDataArray, numArrayElements);
            }

    TUsbAudioStatus
    TUSBAUDIO_GetChannelProperties(
        TUsbAudioHandle deviceHandle,
        unsigned int inputChannels,   // 1 = input channel set, 0 = output channel set
        unsigned int channelMaxCount,
        TUsbAudioChannelProperty channelArray[],
        unsigned int* channelCount
        )
            {
                if (nullptr == mFunctionPointers.mTUSBAUDIO_GetChannelProperties) { return TSTATUS_NOT_SUPPORTED; }
                return mFunctionPointers.mTUSBAUDIO_GetChannelProperties(deviceHandle, inputChannels, channelMaxCount, channelArray, channelCount);
            }

    TUsbAudioStatus
    TUSBAUDIO_GetChannelInfo(
        TUsbAudioHandle deviceHandle,
        int inputChannel,             // 1 = input channel, 0 = output channel
        unsigned int channelNumber,   // 0 = invalid, 1..N = stream channels
        TUsbAudioChannelInfo* channelInfo
        )
            {
                if (nullptr == mFunctionPointers.mTUSBAUDIO_GetChannelInfo) { return TSTATUS_NOT_SUPPORTED; }
                return mFunctionPointers.mTUSBAUDIO_GetChannelInfo(deviceHandle, inputChannel, channelNumber, channelInfo);
            }

    TUsbAudioStatus
    TUSBAUDIO_GetVolumeMuteInfo(
        TUsbAudioHandle deviceHandle,
        int inputChannel,             // 1 = input channel, 0 = output channel
        unsigned int channelNumber,   // 0 = master, 1..N = stream channels
        TUsbAudioVolumeMuteInfo* volumeMuteInfo
        )
            {
                if (nullptr == mFunctionPointers.mTUSBAUDIO_GetVolumeMuteInfo) { return TSTATUS_NOT_SUPPORTED; }
                return mFunctionPointers.mTUSBAUDIO_GetVolumeMuteInfo(deviceHandle, inputChannel, channelNumber, volumeMuteInfo);
            }

    TUsbAudioStatus
    TUSBAUDIO_GetVolume(
        TUsbAudioHandle deviceHandle,
        int inputChannel,             // 1 = input channel, 0 = output channel
        unsigned int channelNumber,   // 0 = master, 1..N = stream channels
        short* volume
        )
            {
                if (nullptr == mFunctionPointers.mTUSBAUDIO_GetVolume) { return TSTATUS_NOT_SUPPORTED; }
                return mFunctionPointers.mTUSBAUDIO_GetVolume(deviceHandle, inputChannel, channelNumber, volume);
            }

    TUsbAudioStatus
    TUSBAUDIO_SetVolume(
        TUsbAudioHandle deviceHandle,
        int inputChannel,             // 1 = input channel, 0 = output channel
        unsigned int channelNumber,   // 0 = master, 1..N = stream channels
        short volume
        )
            {
                if (nullptr == mFunctionPointers.mTUSBAUDIO_SetVolume) { return TSTATUS_NOT_SUPPORTED; }
                return mFunctionPointers.mTUSBAUDIO_SetVolume(deviceHandle, inputChannel, channelNumber, volume);
            }

    TUsbAudioStatus
    TUSBAUDIO_GetMute(
        TUsbAudioHandle deviceHandle,
        int inputChannel,             // 1 = input channel, 0 = output channel
        unsigned int channelNumber,   // 0 = master, 1..N = stream channels
        int* mute
        )
            {
                if (nullptr == mFunctionPointers.mTUSBAUDIO_GetMute) { return TSTATUS_NOT_SUPPORTED; }
                return mFunctionPointers.mTUSBAUDIO_GetMute(deviceHandle, inputChannel, channelNumber, mute);
            }

    TUsbAudioStatus
    TUSBAUDIO_SetMute(
        TUsbAudioHandle deviceHandle,
        int inputChannel,             // 1 = input channel, 0 = output channel
        unsigned int channelNumber,   // 0 = master, 1..N = stream channels
        int mute
        )
            {
                if (nullptr == mFunctionPointers.mTUSBAUDIO_SetMute) { return TSTATUS_NOT_SUPPORTED; }
                return mFunctionPointers.mTUSBAUDIO_SetMute(deviceHandle, inputChannel, channelNumber, mute);
            }

    TUsbAudioStatus
    TUSBAUDIO_GetUsbConfigDescriptor(
        TUsbAudioHandle deviceHandle,
        unsigned char* descBuffer,
        unsigned int bufferSize,
        unsigned int* bytesReturned
        )
            {
                if (nullptr == mFunctionPointers.mTUSBAUDIO_GetUsbConfigDescriptor) { return TSTATUS_NOT_SUPPORTED; }
                return mFunctionPointers.mTUSBAUDIO_GetUsbConfigDescriptor(deviceHandle, descBuffer, bufferSize, bytesReturned);
            }

    TUsbAudioStatus
    TUSBAUDIO_GetUsbStringDescriptorString(
        TUsbAudioHandle deviceHandle,
        unsigned char stringIndex,
        int languageId,
        WCHAR descString[],
        unsigned int descStringMaxChars
        )
            {
                if (nullptr == mFunctionPointers.mTUSBAUDIO_GetUsbStringDescriptorString) { return TSTATUS_NOT_SUPPORTED; }
                return mFunctionPointers.mTUSBAUDIO_GetUsbStringDescriptorString(deviceHandle, stringIndex, languageId, descString, descStringMaxChars);
            }

    TUsbAudioStatus
    TUSBAUDIO_AudioControlRequestSet(
        TUsbAudioHandle deviceHandle,
        unsigned char entityID,
        unsigned char request,
        unsigned char controlSelector,
        unsigned char channelOrMixerControl,
        const void* paramBlock,         // optional, may be nullptr
        unsigned int paramBlockLength,
        unsigned int* bytesTransferred, // optional, may be nullptr
        unsigned int timeoutMillisecs
        )
            {
                if (nullptr == mFunctionPointers.mTUSBAUDIO_AudioControlRequestSet) { return TSTATUS_NOT_SUPPORTED; }
                return mFunctionPointers.mTUSBAUDIO_AudioControlRequestSet(deviceHandle, entityID, request, controlSelector, channelOrMixerControl, paramBlock, paramBlockLength, bytesTransferred, timeoutMillisecs);
            }

    TUsbAudioStatus
    TUSBAUDIO_AudioControlRequestGet(
        TUsbAudioHandle deviceHandle,
        unsigned char entityID,
        unsigned char request,
        unsigned char controlSelector,
        unsigned char channelOrMixerControl,
        void* paramBlock,               // optional, may be nullptr
        unsigned int paramBlockLength,
        unsigned int* bytesTransferred, // optional, may be nullptr
        unsigned int timeoutMillisecs
        )
            {
                if (nullptr == mFunctionPointers.mTUSBAUDIO_AudioControlRequestGet) { return TSTATUS_NOT_SUPPORTED; }
                return mFunctionPointers.mTUSBAUDIO_AudioControlRequestGet(deviceHandle, entityID, request, controlSelector, channelOrMixerControl, paramBlock, paramBlockLength, bytesTransferred, timeoutMillisecs);
            }

    TUsbAudioStatus
    TUSBAUDIO_ClassVendorRequestOut(
        TUsbAudioHandle deviceHandle,
        TUsbAudioControlRequestRecipient recipient,
        unsigned char isClassRequest,
        unsigned char bRequest,
        unsigned short wIndex,
        unsigned short wValue,
        unsigned short wLength,
        const void* dataBuffer,         // optional, may be nullptr
        unsigned int* bytesTransferred, // optional, may be nullptr
        unsigned int timeoutMillisecs
        )
            {
                if (nullptr == mFunctionPointers.mTUSBAUDIO_ClassVendorRequestOut) { return TSTATUS_NOT_SUPPORTED; }
                return mFunctionPointers.mTUSBAUDIO_ClassVendorRequestOut(deviceHandle, recipient, isClassRequest, bRequest, wIndex, wValue, wLength, dataBuffer, bytesTransferred, timeoutMillisecs);
            }

    TUsbAudioStatus
    TUSBAUDIO_ClassVendorRequestIn(
        TUsbAudioHandle deviceHandle,
        TUsbAudioControlRequestRecipient recipient,
        unsigned char isClassRequest,
        unsigned char bRequest,
        unsigned short wIndex,
        unsigned short wValue,
        unsigned short wLength,
        void* dataBuffer,         // optional, may be nullptr
        unsigned int* bytesTransferred, // optional, may be nullptr
        unsigned int timeoutMillisecs
        )
            {
                if (nullptr == mFunctionPointers.mTUSBAUDIO_ClassVendorRequestIn) { return TSTATUS_NOT_SUPPORTED; }
                return mFunctionPointers.mTUSBAUDIO_ClassVendorRequestIn(deviceHandle, recipient, isClassRequest, bRequest, wIndex, wValue, wLength, dataBuffer, bytesTransferred, timeoutMillisecs);
            }

    TUsbAudioStatus
    TUSBAUDIO_GetDriverInfo(
        TUsbAudioDriverInfo* driverInfo
        )
            {
                if (nullptr == mFunctionPointers.mTUSBAUDIO_GetDriverInfo) { return TSTATUS_NOT_SUPPORTED; }
                return mFunctionPointers.mTUSBAUDIO_GetDriverInfo(driverInfo);
            }


    TUsbAudioStatus
    TUSBAUDIO_GetASIOInstanceCount(
        unsigned int* instanceCount
        )
            {
                if (nullptr == mFunctionPointers.mTUSBAUDIO_GetASIOInstanceCount) { return TSTATUS_NOT_SUPPORTED; }
                return mFunctionPointers.mTUSBAUDIO_GetASIOInstanceCount(instanceCount);
            }


    TUsbAudioStatus
    TUSBAUDIO_GetASIOInstanceDetails(
        unsigned int instanceIndex,
        TUsbAudioASIOInstanceDetails* details
        )
            {
                if (nullptr == mFunctionPointers.mTUSBAUDIO_GetASIOInstanceDetails) { return TSTATUS_NOT_SUPPORTED; }
                return mFunctionPointers.mTUSBAUDIO_GetASIOInstanceDetails(instanceIndex, details);
            }


    TUsbAudioStatus
    TUSBAUDIO_GetASIORelation(
        TUsbAudioHandle deviceHandle,
        TUsbAudioASIOInstanceID* asioInstance
        )
            {
                if (nullptr == mFunctionPointers.mTUSBAUDIO_GetASIORelation) { return TSTATUS_NOT_SUPPORTED; }
                return mFunctionPointers.mTUSBAUDIO_GetASIORelation(deviceHandle, asioInstance);
            }


    TUsbAudioStatus
    TUSBAUDIO_GetASIOInstanceAllowed(
        TUsbAudioHandle deviceHandle,
        TUsbAudioASIOInstanceID asioInstance,
        unsigned int* allowed
        )
            {
                if (nullptr == mFunctionPointers.mTUSBAUDIO_GetASIOInstanceAllowed) { return TSTATUS_NOT_SUPPORTED; }
                return mFunctionPointers.mTUSBAUDIO_GetASIOInstanceAllowed(deviceHandle, asioInstance, allowed);
            }


    TUsbAudioStatus
    TUSBAUDIO_GetASIOInstanceInfo(
        TUsbAudioASIOInstanceID asioInstance,
        TUsbAudioASIOInstanceInfo* info
        )
            {
                if (nullptr == mFunctionPointers.mTUSBAUDIO_GetASIOInstanceInfo) { return TSTATUS_NOT_SUPPORTED; }
                return mFunctionPointers.mTUSBAUDIO_GetASIOInstanceInfo(asioInstance, info);
            }

    TUsbAudioStatus
    TUSBAUDIO_SetASIOBufferPreferredSize(
        TUsbAudioASIOInstanceID asioInstance,
        unsigned int referenceSampleRate,
        unsigned int preferredSize,
        unsigned int options
        )
            {
                if (nullptr == mFunctionPointers.mTUSBAUDIO_SetASIOBufferPreferredSize) { return TSTATUS_NOT_SUPPORTED; }
                return mFunctionPointers.mTUSBAUDIO_SetASIOBufferPreferredSize(asioInstance, referenceSampleRate, preferredSize, options);
            }


    TUsbAudioStatus
    TUSBAUDIO_SetPreferredASIODevice(
        TUsbAudioHandle deviceHandle,
        TUsbAudioASIOInstanceID asioInstance
        )
            {
                if (nullptr == mFunctionPointers.mTUSBAUDIO_SetPreferredASIODevice) { return TSTATUS_NOT_SUPPORTED; }
                return mFunctionPointers.mTUSBAUDIO_SetPreferredASIODevice(deviceHandle, asioInstance);
            }

    TUsbAudioStatus
    TUSBAUDIO_ClearPreferredASIODevice(
        TUsbAudioASIOInstanceID asioInstance
        )
            {
                if (nullptr == mFunctionPointers.mTUSBAUDIO_ClearPreferredASIODevice) { return TSTATUS_NOT_SUPPORTED; }
                return mFunctionPointers.mTUSBAUDIO_ClearPreferredASIODevice(asioInstance);
            }



    TUsbAudioStatus
    TUSBAUDIO_GetDeviceUsbMode(
        TUsbAudioHandle deviceHandle,
        TUsbAudioDeviceRunMode* runMode
        )
            {
                if (nullptr == mFunctionPointers.mTUSBAUDIO_GetDeviceUsbMode) { return TSTATUS_NOT_SUPPORTED; }
                return mFunctionPointers.mTUSBAUDIO_GetDeviceUsbMode(deviceHandle, runMode);
            }

    TUsbAudioStatus
    TUSBAUDIO_GetClientInfo(
        TUsbAudioHandle deviceHandle,
        unsigned int* streamingState,   // 0=stopped, 1=active, optional, may be nullptr
        unsigned int* activeKsClients,  // optional, may be nullptr
        unsigned int* activeAsioClients // optional, may be nullptr
        )
            {
                if (nullptr == mFunctionPointers.mTUSBAUDIO_GetClientInfo) { return TSTATUS_NOT_SUPPORTED; }
                return mFunctionPointers.mTUSBAUDIO_GetClientInfo(deviceHandle, streamingState, activeKsClients, activeAsioClients);
            }

    TUsbAudioStatus
    TUSBAUDIO_GetDeviceStreamingMode(
        TUsbAudioHandle deviceHandle,
        TUsbAudioDeviceStreamingMode* streamingMode
        )
            {
                if (nullptr == mFunctionPointers.mTUSBAUDIO_GetDeviceStreamingMode) { return TSTATUS_NOT_SUPPORTED; }
                return mFunctionPointers.mTUSBAUDIO_GetDeviceStreamingMode(deviceHandle, streamingMode);
            }

    TUsbAudioStatus
    TUSBAUDIO_SetDeviceStreamingMode(
        TUsbAudioHandle deviceHandle,
        TUsbAudioDeviceStreamingMode streamingMode,
        unsigned int flags
        )
            {
                if (nullptr == mFunctionPointers.mTUSBAUDIO_SetDeviceStreamingMode) { return TSTATUS_NOT_SUPPORTED; }
                return mFunctionPointers.mTUSBAUDIO_SetDeviceStreamingMode(deviceHandle, streamingMode, flags);
            }

    TUsbAudioStatus
    TUSBAUDIO_LoadFirmwareImageFromBuffer(
        const unsigned char* buffer,
        unsigned int numberOfBytes,
        unsigned int flags    // reserved for future use
        )
            {
                if (nullptr == mFunctionPointers.mTUSBAUDIO_LoadFirmwareImageFromBuffer) { return TSTATUS_NOT_SUPPORTED; }
                return mFunctionPointers.mTUSBAUDIO_LoadFirmwareImageFromBuffer(buffer, numberOfBytes, flags);
            }

    TUsbAudioStatus
    TUSBAUDIO_LoadFirmwareImageFromFile(
        const WCHAR* fileName,
        unsigned int flags    // reserved for future use
        )
            {
                if (nullptr == mFunctionPointers.mTUSBAUDIO_LoadFirmwareImageFromFile) { return TSTATUS_NOT_SUPPORTED; }
                return mFunctionPointers.mTUSBAUDIO_LoadFirmwareImageFromFile(fileName, flags);
            }

    TUsbAudioStatus
    TUSBAUDIO_GetFirmwareImageSize(
        unsigned int* numberOfBytes,
        unsigned int flags
        )
            {
                if (nullptr == mFunctionPointers.mTUSBAUDIO_GetFirmwareImageSize) { return TSTATUS_NOT_SUPPORTED; }
                return mFunctionPointers.mTUSBAUDIO_GetFirmwareImageSize(numberOfBytes, flags);
            }

    TUsbAudioStatus
    TUSBAUDIO_UnloadFirmwareImage()
            {
                if (nullptr == mFunctionPointers.mTUSBAUDIO_UnloadFirmwareImage) { return TSTATUS_NOT_SUPPORTED; }
                return mFunctionPointers.mTUSBAUDIO_UnloadFirmwareImage();
            }

    TUsbAudioStatus
    TUSBAUDIO_GetFirmwareImage(
        unsigned char* buffer,
        unsigned int bufferSize,
        unsigned int* bytesReturned,
        unsigned int flags    // reserved for future use
        )
            {
                if (nullptr == mFunctionPointers.mTUSBAUDIO_GetFirmwareImage) { return TSTATUS_NOT_SUPPORTED; }
                return mFunctionPointers.mTUSBAUDIO_GetFirmwareImage(buffer, bufferSize, bytesReturned, flags);
            }

    TUsbAudioStatus
    TUSBAUDIO_StartDfuDownload(
        unsigned int deviceIndex,
        unsigned int targetIndex,
        unsigned int flags
        )
            {
                if (nullptr == mFunctionPointers.mTUSBAUDIO_StartDfuDownload) { return TSTATUS_NOT_SUPPORTED; }
                return mFunctionPointers.mTUSBAUDIO_StartDfuDownload(deviceIndex, targetIndex, flags);
            }

    TUsbAudioStatus
    TUSBAUDIO_StartDfuUpload(
        unsigned int deviceIndex,
        unsigned int targetIndex,
        unsigned int flags    // reserved for future use
        )
            {
                if (nullptr == mFunctionPointers.mTUSBAUDIO_StartDfuUpload) { return TSTATUS_NOT_SUPPORTED; }
                return mFunctionPointers.mTUSBAUDIO_StartDfuUpload(deviceIndex, targetIndex, flags);
            }

    TUsbAudioStatus
    TUSBAUDIO_StartDfuRevertToFactoryImage(
        unsigned int deviceIndex,
        unsigned int flags    // reserved for future use
        )
            {
                if (nullptr == mFunctionPointers.mTUSBAUDIO_StartDfuRevertToFactoryImage) { return TSTATUS_NOT_SUPPORTED; }
                return mFunctionPointers.mTUSBAUDIO_StartDfuRevertToFactoryImage(deviceIndex, flags);
            }

    TUsbAudioStatus
    TUSBAUDIO_GetDfuStatus(
        TUsbAudioDfuProcState* downloadState,
        unsigned int* currentBytes,       // optional, may be nullptr
        unsigned int* totalBytes,         // optional, may be nullptr
        TUsbAudioStatus* completionStatus // optional, may be nullptr
        )
            {
                if (nullptr == mFunctionPointers.mTUSBAUDIO_GetDfuStatus) { return TSTATUS_NOT_SUPPORTED; }
                return mFunctionPointers.mTUSBAUDIO_GetDfuStatus(downloadState, currentBytes, totalBytes, completionStatus);
            }

    TUsbAudioStatus
    TUSBAUDIO_EndDfuProc()
            {
                if (nullptr == mFunctionPointers.mTUSBAUDIO_EndDfuProc) { return TSTATUS_NOT_SUPPORTED; }
                return mFunctionPointers.mTUSBAUDIO_EndDfuProc();
            }

    TUsbAudioStatus
    TUSBAUDIO_GetDspProperty(
        TUsbAudioHandle deviceHandle,
        void* propertyBuffer,
        unsigned int propertySize
        )
            {
                if (nullptr == mFunctionPointers.mTUSBAUDIO_GetDspProperty) { return TSTATUS_NOT_SUPPORTED; }
                return mFunctionPointers.mTUSBAUDIO_GetDspProperty(deviceHandle, propertyBuffer, propertySize);
            }

    TUsbAudioStatus
    TUSBAUDIO_SetDspProperty(
        TUsbAudioHandle deviceHandle,
        const void* propertyBuffer,
        unsigned int propertySize
        )
            {
                if (nullptr == mFunctionPointers.mTUSBAUDIO_SetDspProperty) { return TSTATUS_NOT_SUPPORTED; }
                return mFunctionPointers.mTUSBAUDIO_SetDspProperty(deviceHandle, propertyBuffer, propertySize);
            }

    TUsbAudioStatus
    TUSBAUDIO_GetExtendedInfo(
        TUsbAudioHandle deviceHandle,
        TUsbAudioExtendedInfoId extendedInfoId,
        const void* inBuffer,
        unsigned int inBufferSize,
        void* outBuffer,
        unsigned int maxOutBufferSize,
        unsigned int* outBufferSize
        )
            {
                if (nullptr == mFunctionPointers.mTUSBAUDIO_GetExtendedInfo) { return TSTATUS_NOT_SUPPORTED; }
                return mFunctionPointers.mTUSBAUDIO_GetExtendedInfo(deviceHandle, extendedInfoId, inBuffer, inBufferSize, outBuffer, maxOutBufferSize, outBufferSize);
            }

    TUsbAudioStatus
    TUSBAUDIO_SetHwChannelHiddenFlags(
        TUsbAudioHandle deviceHandle,
        unsigned int streamFormatId,
        const TUsbAudioHardwareChannelHiddenFlags* hiddenFlagsBitmask
        )
            {
                if (nullptr == mFunctionPointers.mTUSBAUDIO_SetHwChannelHiddenFlags) { return TSTATUS_NOT_SUPPORTED; }
                return mFunctionPointers.mTUSBAUDIO_SetHwChannelHiddenFlags(deviceHandle, streamFormatId, hiddenFlagsBitmask);
            }

    TUsbAudioStatus
    TUSBAUDIO_GetCustomString(
        TUsbAudioHandle deviceHandle,
        TUsbAudioCustomStringScope scope,
        const WCHAR* valueName,
        WCHAR* valueString,
        unsigned int valueStringNumCharacters
        )
            {
                if (nullptr == mFunctionPointers.mTUSBAUDIO_GetCustomString) { return TSTATUS_NOT_SUPPORTED; }
                return mFunctionPointers.mTUSBAUDIO_GetCustomString(deviceHandle, scope, valueName, valueString, valueStringNumCharacters);
            }

    TUsbAudioStatus
    TUSBAUDIO_QueryDriverStatistics(
        TUsbAudioHandle deviceHandle,
        void* dataBuffer,
        unsigned int dataBufferSize
        )
            {
                if (nullptr == mFunctionPointers.mTUSBAUDIO_QueryDriverStatistics) { return TSTATUS_NOT_SUPPORTED; }
                return mFunctionPointers.mTUSBAUDIO_QueryDriverStatistics(deviceHandle, dataBuffer, dataBufferSize);
            }

    TUsbAudioStatus
    TUSBAUDIO_ResetDriverStatistics(
        TUsbAudioHandle deviceHandle
        )
            {
                if (nullptr == mFunctionPointers.mTUSBAUDIO_ResetDriverStatistics) { return TSTATUS_NOT_SUPPORTED; }
                return mFunctionPointers.mTUSBAUDIO_ResetDriverStatistics(deviceHandle);
            }

    TUsbAudioStatus
    TUSBAUDIO_QueryDeviceStatistics(
        TUsbAudioHandle deviceHandle,
        TUsbAudioDeviceStatistics* stats,
        unsigned int statsSize,
        unsigned int reset
        )
            {
                if (nullptr == mFunctionPointers.mTUSBAUDIO_QueryDeviceStatistics) { return TSTATUS_NOT_SUPPORTED; }
                return mFunctionPointers.mTUSBAUDIO_QueryDeviceStatistics(deviceHandle, stats, statsSize, reset);
            }

    const WCHAR*
    TUSBAUDIO_StatusCodeStringW(
        TSTATUS statusCode
        )
            {
                if (nullptr == mFunctionPointers.mTUSBAUDIO_StatusCodeStringW) { return L""; }
                return mFunctionPointers.mTUSBAUDIO_StatusCodeStringW(statusCode);
            }

    const char*
    TUSBAUDIO_StatusCodeStringA(
        TSTATUS statusCode
        )
            {
                if (nullptr == mFunctionPointers.mTUSBAUDIO_StatusCodeStringA) { return ""; }
                return mFunctionPointers.mTUSBAUDIO_StatusCodeStringA(statusCode);
            }

private:

    void
    InitFunctionPointers();


// data
protected:

struct FunctionPointers {
    //pointers to functions of the loaded DLL or NULL if the DLL is not loaded
    F_TUSBAUDIO_GetApiVersion*                      mTUSBAUDIO_GetApiVersion {nullptr};
    F_TUSBAUDIO_CheckApiVersion*                    mTUSBAUDIO_CheckApiVersion {nullptr};
    F_TUSBAUDIO_RegisterPnpNotification*            mTUSBAUDIO_RegisterPnpNotification {nullptr};
    F_TUSBAUDIO_UnregisterPnpNotification*          mTUSBAUDIO_UnregisterPnpNotification {nullptr};
    F_TUSBAUDIO_EnumerateDevices*                   mTUSBAUDIO_EnumerateDevices {nullptr};
    F_TUSBAUDIO_GetDeviceCount*                     mTUSBAUDIO_GetDeviceCount {nullptr};
    F_TUSBAUDIO_OpenDeviceByIndex*                  mTUSBAUDIO_OpenDeviceByIndex {nullptr};
    F_TUSBAUDIO_OpenDeviceByChannelIdString*        mTUSBAUDIO_OpenDeviceByChannelIdString {nullptr};
    F_TUSBAUDIO_CloseDevice*                        mTUSBAUDIO_CloseDevice {nullptr};
    F_TUSBAUDIO_GetDeviceProperties*                mTUSBAUDIO_GetDeviceProperties {nullptr};
    F_TUSBAUDIO_GetDeviceInstanceIdString*          mTUSBAUDIO_GetDeviceInstanceIdString {nullptr};
    F_TUSBAUDIO_GetDeviceContainerIdString*         mTUSBAUDIO_GetDeviceContainerIdString {nullptr};
    F_TUSBAUDIO_GetChannelIndexForChannelIdString*  mTUSBAUDIO_GetChannelIndexForChannelIdString {nullptr};
    F_TUSBAUDIO_GetChannelIdString*                 mTUSBAUDIO_GetChannelIdString {nullptr};
    F_TUSBAUDIO_RegisterDeviceNotification*         mTUSBAUDIO_RegisterDeviceNotification {nullptr};
    F_TUSBAUDIO_ReadDeviceNotification*             mTUSBAUDIO_ReadDeviceNotification {nullptr};
    F_TUSBAUDIO_GetSupportedSampleRates*            mTUSBAUDIO_GetSupportedSampleRates {nullptr};
    F_TUSBAUDIO_GetCurrentSampleRate*               mTUSBAUDIO_GetCurrentSampleRate {nullptr};
    F_TUSBAUDIO_SetSampleRate*                      mTUSBAUDIO_SetSampleRate {nullptr};
    F_TUSBAUDIO_GetSupportedClockSources*           mTUSBAUDIO_GetSupportedClockSources {nullptr};
    F_TUSBAUDIO_GetClockSourceStatus*               mTUSBAUDIO_GetClockSourceStatus {nullptr};
    F_TUSBAUDIO_GetCurrentClockSource*              mTUSBAUDIO_GetCurrentClockSource {nullptr};
    F_TUSBAUDIO_SetCurrentClockSource*              mTUSBAUDIO_SetCurrentClockSource {nullptr};
    F_TUSBAUDIO_GetSupportedStreamFormats*          mTUSBAUDIO_GetSupportedStreamFormats {nullptr};
    F_TUSBAUDIO_GetCurrentStreamFormat*             mTUSBAUDIO_GetCurrentStreamFormat {nullptr};
    F_TUSBAUDIO_SetCurrentStreamFormat*             mTUSBAUDIO_SetCurrentStreamFormat {nullptr};
    F_TUSBAUDIO_GetStreamFormatSelectionMode*       mTUSBAUDIO_GetStreamFormatSelectionMode {nullptr};
    F_TUSBAUDIO_SetSoundDeviceProfile*              mTUSBAUDIO_SetSoundDeviceProfile {nullptr};
    F_TUSBAUDIO_SetVirtualSoundDeviceProfile*       mTUSBAUDIO_SetVirtualSoundDeviceProfile {nullptr};
    F_TUSBAUDIO_GetControlPanelOptions*             mTUSBAUDIO_GetControlPanelOptions {nullptr};
    F_TUSBAUDIO_GetStreamChannelCount*              mTUSBAUDIO_GetStreamChannelCount{nullptr};
    F_TUSBAUDIO_GetStreamChannelIds*                mTUSBAUDIO_GetStreamChannelIds{nullptr};
    F_TUSBAUDIO_EnablePeakMeters*                   mTUSBAUDIO_EnablePeakMeters {nullptr};
    F_TUSBAUDIO_DisablePeakMeters*                  mTUSBAUDIO_DisablePeakMeters {nullptr};
    F_TUSBAUDIO_GetPeakMeters*                      mTUSBAUDIO_GetPeakMeters {nullptr};
    F_TUSBAUDIO_GetChannelProperties*               mTUSBAUDIO_GetChannelProperties {nullptr};
    F_TUSBAUDIO_GetChannelInfo*                     mTUSBAUDIO_GetChannelInfo {nullptr};
    F_TUSBAUDIO_GetVolumeMuteInfo*                  mTUSBAUDIO_GetVolumeMuteInfo {nullptr};
    F_TUSBAUDIO_GetVolume*                          mTUSBAUDIO_GetVolume {nullptr};
    F_TUSBAUDIO_SetVolume*                          mTUSBAUDIO_SetVolume {nullptr};
    F_TUSBAUDIO_GetMute*                            mTUSBAUDIO_GetMute {nullptr};
    F_TUSBAUDIO_SetMute*                            mTUSBAUDIO_SetMute {nullptr};
    F_TUSBAUDIO_GetUsbConfigDescriptor*             mTUSBAUDIO_GetUsbConfigDescriptor {nullptr};
    F_TUSBAUDIO_GetUsbStringDescriptorString*       mTUSBAUDIO_GetUsbStringDescriptorString {nullptr};
    F_TUSBAUDIO_AudioControlRequestSet*             mTUSBAUDIO_AudioControlRequestSet {nullptr};
    F_TUSBAUDIO_AudioControlRequestGet*             mTUSBAUDIO_AudioControlRequestGet {nullptr};
    F_TUSBAUDIO_ClassVendorRequestOut*              mTUSBAUDIO_ClassVendorRequestOut {nullptr};
    F_TUSBAUDIO_ClassVendorRequestIn*               mTUSBAUDIO_ClassVendorRequestIn {nullptr};
    F_TUSBAUDIO_GetDriverInfo*                      mTUSBAUDIO_GetDriverInfo {nullptr};
    F_TUSBAUDIO_GetASIOInstanceCount*               mTUSBAUDIO_GetASIOInstanceCount {nullptr};
    F_TUSBAUDIO_GetASIOInstanceDetails*             mTUSBAUDIO_GetASIOInstanceDetails {nullptr};
    F_TUSBAUDIO_GetASIORelation*                    mTUSBAUDIO_GetASIORelation {nullptr};
    F_TUSBAUDIO_GetASIOInstanceAllowed*             mTUSBAUDIO_GetASIOInstanceAllowed {nullptr};
    F_TUSBAUDIO_GetASIOInstanceInfo*                mTUSBAUDIO_GetASIOInstanceInfo {nullptr};
    F_TUSBAUDIO_SetASIOBufferPreferredSize*         mTUSBAUDIO_SetASIOBufferPreferredSize {nullptr};
    F_TUSBAUDIO_SetPreferredASIODevice*             mTUSBAUDIO_SetPreferredASIODevice {nullptr};
    F_TUSBAUDIO_ClearPreferredASIODevice*           mTUSBAUDIO_ClearPreferredASIODevice {nullptr};
    F_TUSBAUDIO_GetDeviceUsbMode*                   mTUSBAUDIO_GetDeviceUsbMode {nullptr};
    F_TUSBAUDIO_GetClientInfo*                      mTUSBAUDIO_GetClientInfo {nullptr};
    F_TUSBAUDIO_GetDeviceStreamingMode*             mTUSBAUDIO_GetDeviceStreamingMode {nullptr};
    F_TUSBAUDIO_SetDeviceStreamingMode*             mTUSBAUDIO_SetDeviceStreamingMode {nullptr};
    F_TUSBAUDIO_LoadFirmwareImageFromBuffer*        mTUSBAUDIO_LoadFirmwareImageFromBuffer {nullptr};
    F_TUSBAUDIO_LoadFirmwareImageFromFile*          mTUSBAUDIO_LoadFirmwareImageFromFile {nullptr};
    F_TUSBAUDIO_GetFirmwareImageSize*               mTUSBAUDIO_GetFirmwareImageSize {nullptr};
    F_TUSBAUDIO_UnloadFirmwareImage*                mTUSBAUDIO_UnloadFirmwareImage {nullptr};
    F_TUSBAUDIO_GetFirmwareImage*                   mTUSBAUDIO_GetFirmwareImage {nullptr};
    F_TUSBAUDIO_StartDfuDownload*                   mTUSBAUDIO_StartDfuDownload {nullptr};
    F_TUSBAUDIO_StartDfuUpload*                     mTUSBAUDIO_StartDfuUpload {nullptr};
    F_TUSBAUDIO_StartDfuRevertToFactoryImage*       mTUSBAUDIO_StartDfuRevertToFactoryImage {nullptr};
    F_TUSBAUDIO_GetDfuStatus*                       mTUSBAUDIO_GetDfuStatus {nullptr};
    F_TUSBAUDIO_EndDfuProc*                         mTUSBAUDIO_EndDfuProc {nullptr};
    F_TUSBAUDIO_GetDspProperty*                     mTUSBAUDIO_GetDspProperty {nullptr};
    F_TUSBAUDIO_SetDspProperty*                     mTUSBAUDIO_SetDspProperty {nullptr};
    F_TUSBAUDIO_GetExtendedInfo*                    mTUSBAUDIO_GetExtendedInfo {nullptr};
    F_TUSBAUDIO_SetHwChannelHiddenFlags*            mTUSBAUDIO_SetHwChannelHiddenFlags {nullptr};
    F_TUSBAUDIO_GetCustomString*                    mTUSBAUDIO_GetCustomString {nullptr};
    F_TUSBAUDIO_QueryDriverStatistics*              mTUSBAUDIO_QueryDriverStatistics {nullptr};
    F_TUSBAUDIO_ResetDriverStatistics*              mTUSBAUDIO_ResetDriverStatistics {nullptr};
    F_TUSBAUDIO_QueryDeviceStatistics*              mTUSBAUDIO_QueryDeviceStatistics {nullptr};
    F_TUSBAUDIO_StatusCodeStringW*                  mTUSBAUDIO_StatusCodeStringW {nullptr};
    F_TUSBAUDIO_StatusCodeStringA*                  mTUSBAUDIO_StatusCodeStringA {nullptr};
};

    FunctionPointers mFunctionPointers {};

    //handle to the loaded DLL or NULL if the DLL is not loaded
    HMODULE mDllHandle{nullptr};

    unsigned int mDllVersion{0};
};

#endif //__TUsbAudioApiDll_H__
