#include "globalfont.h"
#include "volumemeters1m2.h"


VolumeMeterS1M2::VolumeMeterS1M2(QWidget* parent)
    : QWidget(parent)
{
    mRectMeterLeft.timerText.setSingleShot(true);
    mRectMeterLeft.timerClip.setSingleShot(true);
    mRectMeterRight.timerText.setSingleShot(true);
    mRectMeterRight.timerClip.setSingleShot(true);
    connect(&mRectMeterLeft.timerText, SIGNAL(timeout()), this, SLOT(in_timerTextLeft_timeout()), Qt::UniqueConnection);
    connect(&mRectMeterLeft.timerClip, SIGNAL(timeout()), this, SLOT(in_timerClipLeft_timeout()), Qt::UniqueConnection);
    connect(&mRectMeterRight.timerText, SIGNAL(timeout()), this, SLOT(in_timerTextRight_timeout()), Qt::UniqueConnection);
    connect(&mRectMeterRight.timerClip, SIGNAL(timeout()), this, SLOT(in_timerClipRight_timeout()), Qt::UniqueConnection);
    connect(&mTimerMeterLeft, SIGNAL(timeout()), this, SLOT(in_mTimerMeterLeft_timeout()), Qt::UniqueConnection);
    connect(&mTimerMeterRight, SIGNAL(timeout()), this, SLOT(in_mTimerMeterRight_timeout()), Qt::UniqueConnection);
}
VolumeMeterS1M2::~VolumeMeterS1M2()
{

}


// override
void VolumeMeterS1M2::resizeEvent(QResizeEvent* e)
{
    Q_UNUSED(e);
    // W
    float wPixelPerRatio=size().width() / 100.0;
    int wSpace1=wPixelPerRatio * mSpace1;
    int wMeterLeft=wPixelPerRatio * mMeter;
    int wSpace2=wPixelPerRatio * mSpace2;
    int wScale=wPixelPerRatio * mScale;
    int wSpace3=wPixelPerRatio * mSpace2;
    int wMeterRight=wPixelPerRatio * mMeter;
    int wSpace4=wPixelPerRatio * mSpace1;
    int wRemain=size().width() - wSpace1 - wMeterLeft - wSpace2 - wScale - wSpace3 - wMeterRight - wSpace4;
    int xMeterLeft=0 + wRemain / 2 + wSpace1;
    int xScale=xMeterLeft + wMeterLeft + wSpace2;
    int xMeterRight=xScale + wScale + wSpace3;
    // H
    float hPixelPerRatio=size().height() / 100.0;
    int hText=hPixelPerRatio * mHText;
    int hSpace1=hPixelPerRatio * mHSpace1;
    int hClip=hPixelPerRatio * mHClip;
    int hSpace2=hPixelPerRatio * mHSpace2;
    int hVolume=hPixelPerRatio * mHVolume;
    int hSpace3=hPixelPerRatio * mHSpace3;
    int hRemain=size().height() - hText - hSpace1 - hClip - hSpace2 - hVolume - hSpace3;
    int yText=0 + hRemain / 2;
    int yTextEnd=yText + hText;
    int yClip=yTextEnd + hSpace1;
    int yClipEnd=yClip + hClip;
    int yVolume=yClipEnd + hSpace2;
    mRectMeterLeft.text.setRect(xMeterLeft, yText, wMeterLeft, hText);
    mRectMeterLeft.clip.setRect(xMeterLeft, yClip, wMeterLeft, hClip);
    mRectMeterLeft.volume.setRect(xMeterLeft, yVolume, wMeterLeft, hVolume);
    mRectScale.setRect(xScale, 0, wScale, size().height());
    mRectMeterRight.text.setRect(xMeterRight, yText, wMeterRight, hText);
    mRectMeterRight.clip.setRect(xMeterRight, yClip, wMeterRight, hClip);
    mRectMeterRight.volume.setRect(xMeterRight, yVolume, wMeterRight, hVolume);
}
void VolumeMeterS1M2::paintEvent(QPaintEvent* e)
{
    Q_UNUSED(e);
    QPainter painter(this);
    painter.setRenderHints(QPainter::Antialiasing | QPainter::TextAntialiasing);
    drawBG(&painter);
    drawElement(&painter);
}
void VolumeMeterS1M2::mouseDoubleClickEvent(QMouseEvent* e)
{
    if(e->button() == Qt::LeftButton)
    {
        if(mRectMeterLeft.clip.contains(e->pos()))
        {
            mRectMeterLeft.clipStatus = false;
            update();
        }
        else if(mRectMeterRight.clip.contains(e->pos()))
        {
            mRectMeterRight.clipStatus = false;
            update();
        }
    }
}
void VolumeMeterS1M2::drawBG(QPainter* painter)
{
    painter->save();
    painter->setPen(Qt::NoPen);
    painter->setBrush(mColorBG);
    painter->drawRect(rect());
    painter->restore();
}
void VolumeMeterS1M2::drawElement(QPainter* painter)
{
    float pixelPerScale;
    QPointF textPoint;
    QRect rectMeter;
    painter->save();
    // font
    mFont.setPointSize(GLBFHandle.getSuitablePointSize(mFont, "-60", mRectScale.width()));
    painter->setFont(mFont);
    // text
    QColor textColor(161, 161, 161);
    mRectMeterLeft.volumeMax >= -8 ? (textColor.setRgb(186,113,68)) : ((void) 0);
    mRectMeterLeft.volumeMax == 0 ? (textColor.setRgb(246,72,71)) : ((void) 0);
    painter->setPen(textColor);
    painter->setBrush(Qt::NoBrush);
    QString str = QString("%1").arg((double) mRectMeterLeft.volumeMax, 0, 'f', 0);
    textPoint.setX(mRectMeterLeft.text.x() + (mRectMeterLeft.text.width() - painter->fontMetrics().horizontalAdvance(str)) / 2);
    textPoint.setY(mRectMeterLeft.text.y() + mRectMeterLeft.text.height());
    painter->drawText(textPoint, str);

    textColor.setRgb(161, 161, 161);
    mRectMeterRight.volumeMax >= -8 ? (textColor.setRgb(186,113,68)) : ((void) 0);
    mRectMeterRight.volumeMax == 0 ? (textColor.setRgb(246,72,71)) : ((void) 0);
    painter->setPen(textColor);
    painter->setBrush(Qt::NoBrush);
    str = QString("%1").arg((double) mRectMeterRight.volumeMax, 0, 'f', 0);
    textPoint.setX(mRectMeterRight.text.x() + (mRectMeterRight.text.width() - painter->fontMetrics().horizontalAdvance(str)) / 2);
    textPoint.setY(mRectMeterRight.text.y() + mRectMeterRight.text.height());
    painter->drawText(textPoint, str);
    // clip
    painter->setPen(Qt::NoPen);
    mRectMeterLeft.clipStatus ? (painter->setBrush(QBrush(QColor(246, 72, 71)))) : (painter->setBrush(QBrush(QColor(60, 60, 60))));
    painter->drawRoundedRect(mRectMeterLeft.clip, mRectMeterLeft.clip.height() / 3, mRectMeterLeft.clip.height() / 3);
    mRectMeterRight.clipStatus ? (painter->setBrush(QBrush(QColor(246, 72, 71)))) : (painter->setBrush(QBrush(QColor(60, 60, 60))));
    painter->drawRoundedRect(mRectMeterRight.clip, mRectMeterRight.clip.height() / 3, mRectMeterRight.clip.height() / 3);
    // volume
    painter->setBrush(QBrush(QColor(60, 60, 60)));
    painter->drawRoundedRect(mRectMeterLeft.volume, mRectMeterLeft.volume.width() * 0.1, mRectMeterLeft.volume.width() * 0.1);
    painter->drawRoundedRect(mRectMeterRight.volume, mRectMeterRight.volume.width() * 0.1, mRectMeterRight.volume.width() * 0.1);

    painter->setPen(Qt::NoPen);
    QLinearGradient gradient(mRectMeterLeft.volume.topLeft(), mRectMeterLeft.volume.bottomLeft());
    gradient.setColorAt(0.0, QColor("#f64847"));
    gradient.setColorAt(0.1, QColor("#a77d43"));
    gradient.setColorAt(0.2, QColor("#0f9640"));
    gradient.setColorAt(1.0, QColor("#009641"));
    painter->setBrush(gradient);
    pixelPerScale = mRectMeterLeft.volume.height() / 60.0;
    rectMeter = mRectMeterLeft.volume;
    rectMeter.setY(rectMeter.y() + pixelPerScale * (0 - mRectMeterLeft.volumeValue));
    rectMeter.setHeight(rectMeter.bottomLeft().y() - rectMeter.y());
    if(mRectMeterLeft.volumeValue != -60)
    {
        painter->drawRoundedRect(rectMeter, rectMeter.width() * 0.1, rectMeter.width() * 0.1);
    }
    gradient.setStart(mRectMeterRight.volume.topLeft());
    gradient.setFinalStop(mRectMeterRight.volume.bottomLeft());
    painter->setBrush(gradient);
    pixelPerScale = mRectMeterRight.volume.height() / 60.0;
    rectMeter = mRectMeterRight.volume;
    rectMeter.setY(rectMeter.y() + pixelPerScale * (0 - mRectMeterRight.volumeValue));
    rectMeter.setHeight(rectMeter.bottomLeft().y() - rectMeter.y());
    if(mRectMeterRight.volumeValue != -60)
    {
        painter->drawRoundedRect(rectMeter, rectMeter.width() * 0.1, rectMeter.width() * 0.1);
    }
    // scale
    int wScaleLine=mRectMeterLeft.volume.width() / 4;
    painter->setPen(QColor(161, 161, 161));
    painter->setBrush(Qt::NoBrush);
    textPoint.setX(mRectScale.x() + (mRectScale.width() - painter->fontMetrics().horizontalAdvance("0")) / 2);
    textPoint.setY(mRectMeterLeft.volume.y() + 0 * pixelPerScale);
    if(!mScaleLineHidden) painter->drawLine(mRectMeterLeft.volume.x() + mRectMeterLeft.volume.width(), textPoint.y() + 1, mRectMeterLeft.volume.x() + mRectMeterLeft.volume.width() + wScaleLine, textPoint.y() + 1);
    if(!mScaleLineHidden) painter->drawLine(mRectMeterRight.volume.x(), textPoint.y() + 1, mRectMeterRight.volume.x() - wScaleLine, textPoint.y() + 1);
    painter->drawText(textPoint, "0");
    textPoint.setX(mRectScale.x() + (mRectScale.width() - painter->fontMetrics().horizontalAdvance("-6")) / 2);
    textPoint.setY(mRectMeterLeft.volume.y() + 6 * pixelPerScale);
    if(!mScaleLineHidden) painter->drawLine(mRectMeterLeft.volume.x() + mRectMeterLeft.volume.width(), textPoint.y() + 1, mRectMeterLeft.volume.x() + mRectMeterLeft.volume.width() + wScaleLine, textPoint.y() + 1);
    if(!mScaleLineHidden) painter->drawLine(mRectMeterRight.volume.x(), textPoint.y() + 1, mRectMeterRight.volume.x() - wScaleLine, textPoint.y() + 1);
    painter->drawText(textPoint, "-6");
    textPoint.setX(mRectScale.x() + (mRectScale.width() - painter->fontMetrics().horizontalAdvance("-12")) / 2);
    textPoint.setY(mRectMeterLeft.volume.y() + 12 * pixelPerScale);
    if(!mScaleLineHidden) painter->drawLine(mRectMeterLeft.volume.x() + mRectMeterLeft.volume.width(), textPoint.y() + 1, mRectMeterLeft.volume.x() + mRectMeterLeft.volume.width() + wScaleLine, textPoint.y() + 1);
    if(!mScaleLineHidden) painter->drawLine(mRectMeterRight.volume.x(), textPoint.y() + 1, mRectMeterRight.volume.x() - wScaleLine, textPoint.y() + 1);
    painter->drawText(textPoint, "-12");
    textPoint.setX(mRectScale.x() + (mRectScale.width() - painter->fontMetrics().horizontalAdvance("-24")) / 2);
    textPoint.setY(mRectMeterLeft.volume.y() + 24 * pixelPerScale);
    if(!mScaleLineHidden) painter->drawLine(mRectMeterLeft.volume.x() + mRectMeterLeft.volume.width(), textPoint.y() + 1, mRectMeterLeft.volume.x() + mRectMeterLeft.volume.width() + wScaleLine, textPoint.y() + 1);
    if(!mScaleLineHidden) painter->drawLine(mRectMeterRight.volume.x(), textPoint.y() + 1, mRectMeterRight.volume.x() - wScaleLine, textPoint.y() + 1);
    painter->drawText(textPoint, "-24");
    textPoint.setX(mRectScale.x() + (mRectScale.width() - painter->fontMetrics().horizontalAdvance("-36")) / 2);
    textPoint.setY(mRectMeterLeft.volume.y() + 36 * pixelPerScale);
    if(!mScaleLineHidden) painter->drawLine(mRectMeterLeft.volume.x() + mRectMeterLeft.volume.width(), textPoint.y() + 1, mRectMeterLeft.volume.x() + mRectMeterLeft.volume.width() + wScaleLine, textPoint.y() + 1);
    if(!mScaleLineHidden) painter->drawLine(mRectMeterRight.volume.x(), textPoint.y() + 1, mRectMeterRight.volume.x() - wScaleLine, textPoint.y() + 1);
    painter->drawText(textPoint, "-36");
    textPoint.setX(mRectScale.x() + (mRectScale.width() - painter->fontMetrics().horizontalAdvance("-60")) / 2);
    textPoint.setY(mRectMeterLeft.volume.y() + mRectMeterLeft.volume.height());
    if(!mScaleLineHidden) painter->drawLine(mRectMeterLeft.volume.x() + mRectMeterLeft.volume.width(), textPoint.y() + 1, mRectMeterLeft.volume.x() + mRectMeterLeft.volume.width() + wScaleLine, textPoint.y() + 1);
    if(!mScaleLineHidden) painter->drawLine(mRectMeterRight.volume.x(), textPoint.y() + 1, mRectMeterRight.volume.x() - wScaleLine, textPoint.y() + 1);
    painter->drawText(textPoint, "-60");
    painter->restore();
}


// slot
void VolumeMeterS1M2::in_timerTextLeft_timeout()
{
    mRectMeterLeft.volumeMax = mRectMeterLeft.volumeValue;
    update();
}
void VolumeMeterS1M2::in_timerTextRight_timeout()
{
    mRectMeterRight.volumeMax = mRectMeterRight.volumeValue;
    update();
}
void VolumeMeterS1M2::in_timerClipLeft_timeout()
{
    mRectMeterLeft.clipStatus = false;
    update();
}
void VolumeMeterS1M2::in_timerClipRight_timeout()
{
    mRectMeterRight.clipStatus = false;
    update();
}
void VolumeMeterS1M2::in_mTimerMeterLeft_timeout()
{
    if(mRectMeterLeft.volumeValue > -60)
    {
        mRectMeterLeft.volumeValue = -60 >= mRectMeterLeft.volumeValue ? (-60) : (qMax(-60, mRectMeterLeft.volumeValue - 4));
        update();
    }
    else
    {
        mTimerMeterLeft.stop();
    }
}
void VolumeMeterS1M2::in_mTimerMeterRight_timeout()
{
    if(mRectMeterRight.volumeValue > -60)
    {
        mRectMeterRight.volumeValue = -60 >= mRectMeterRight.volumeValue ? (-60) : (qMax(-60, mRectMeterRight.volumeValue - 4));
        update();
    }
    else
    {
        mTimerMeterRight.stop();
    }
}


// setter & getter
void VolumeMeterS1M2::setFont(QFont font)
{
    mFont = font;
    update();
}
void VolumeMeterS1M2::setColorBG(QColor color)
{
    mColorBG = color;
    update();
}
void VolumeMeterS1M2::setValueLeft(int value)
{
    if(value > 1)
    {
        return;
    }
    if(mTimerMeterLeft.isActive())
    {
        mTimerMeterLeft.stop();
    }
    if(value != 1)
    {
        value = qMax(-600, value);
        if(value % 10)
        {
            value = value / 10 - 1;
        }
        else
        {
            value = value / 10;
        }
    }
    if(value == 1)
    {
        mRectMeterLeft.clipStatus = true;
        value = 0;
        mRectMeterLeft.timerClip.start(5000);
    }
    mRectMeterLeft.volumeValue = value >= mRectMeterLeft.volumeValue ? (value) : (qMax(-60, mRectMeterLeft.volumeValue - 4));
    if(mRectMeterLeft.volumeValue > mRectMeterLeft.volumeMax)
    {
        mRectMeterLeft.volumeMax = mRectMeterLeft.volumeValue;
        mRectMeterLeft.timerText.start(2000);
    }
    else
    {
        if(!mRectMeterLeft.timerText.isActive())
        {
            mRectMeterLeft.volumeMax = mRectMeterLeft.volumeValue;
        }
    }
    update();
}
void VolumeMeterS1M2::setValueRight(int value)
{
    if(value > 1)
    {
        return;
    }
    if(mTimerMeterRight.isActive())
    {
        mTimerMeterRight.stop();
    }
    if(value != 1)
    {
        value = qMax(-600, value);
        if(value % 10)
        {
            value = value / 10 - 1;
        }
        else
        {
            value = value / 10;
        }
    }
    if(value == 1)
    {
        mRectMeterRight.clipStatus = true;
        value = 0;
        mRectMeterRight.timerClip.start(5000);
    }
    mRectMeterRight.volumeValue = value >= mRectMeterRight.volumeValue ? (value) : (qMax(-60, mRectMeterRight.volumeValue - 4));
    if(mRectMeterRight.volumeValue > mRectMeterRight.volumeMax)
    {
        mRectMeterRight.volumeMax = mRectMeterRight.volumeValue;
        mRectMeterRight.timerText.start(2000);
    }
    else
    {
        if(!mRectMeterRight.timerText.isActive())
        {
            mRectMeterRight.volumeMax = mRectMeterRight.volumeValue;
        }
    }
    update();
}
void VolumeMeterS1M2::setMeterLeftClear()
{
    mTimerMeterLeft.stop();
    mRectMeterLeft.timerClip.stop();
    mRectMeterLeft.timerText.stop();
    mRectMeterLeft.volumeValue = -60;
    mRectMeterLeft.volumeMax = -60;
    mRectMeterLeft.clipStatus = false;
    update();
}
void VolumeMeterS1M2::setMeterLeftSlip()
{
    mTimerMeterLeft.start(55);
}
void VolumeMeterS1M2::setMeterRightClear()
{
    mTimerMeterRight.stop();
    mRectMeterRight.timerClip.stop();
    mRectMeterRight.timerText.stop();
    mRectMeterRight.volumeValue = -60;
    mRectMeterRight.volumeMax = -60;
    mRectMeterRight.clipStatus = false;
    update();
}
void VolumeMeterS1M2::setMeterRightSlip()
{
    mTimerMeterRight.start(55);
}
void VolumeMeterS1M2::setWidthRatio(int space1, int space2, int meter, int scale)
{
    mSpace1 = space1;
    mSpace2 = space2;
    mMeter = meter;
    mScale = scale;
    QResizeEvent e(size(), size());
    resizeEvent(&e);
    update();
}
void VolumeMeterS1M2::setHeightRatio(int text, int space1, int clip, int space2, int volume, int space3)
{
    mHText = text;
    mHSpace1 = space1;
    mHClip = clip;
    mHSpace2 = space2;
    mHVolume = volume;
    mHSpace3 = space3;
    QResizeEvent e(size(), size());
    resizeEvent(&e);
    update();
}
void VolumeMeterS1M2::setScaleLineHidden(bool hidden)
{
    mScaleLineHidden = hidden;
    update();
}

