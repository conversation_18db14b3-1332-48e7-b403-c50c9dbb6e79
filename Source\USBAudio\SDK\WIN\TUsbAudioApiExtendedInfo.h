/************************************************************************
 *  Module:       TUsbAudioApiExtendedInfo.h
 *  Long name:    TUSBAUDIO_GetExtendedInfo wrapper
 *  Description:  Implements specific TUSBAUDIO_GetExtendedInfo API calls.
 *  Runtime Env.: Win32, Win64
 *  Author(s):    <PERSON>
 *  Company:      Thesycon GmbH, Ilmenau
 ************************************************************************/

#ifndef __TUsbAudioApiExtendedInfo_H__
#define __TUsbAudioApiExtendedInfo_H__

#include "TUsbAudioApiDll.h"


//
// This helper class implements specific TUSBAUDIO_GetExtendedInfo API calls.
//
class TUsbAudioApiExtendedInfo
{
// construction/destruction
public:
    //
    // constructor
    //
    TUsbAudioApiExtendedInfo(TUsbAudioApiDll& apiDll);


// methods
public:

    TUsbAudioStatus
    GetWdmSoundDeviceCount(
        TUsbAudioHandle deviceHandle,
        unsigned int& inputWdmSoundDeviceCount,
        unsigned int& outputWdmSoundDeviceCount
        );

    TUsbAudioStatus
    GetWdmSoundDevices(
        TUsbAudioHandle deviceHandle,
        bool input,
        TUsbAudioWdmSoundDevice* wdmSoundDeviceInfo,
        unsigned int maxNumWdmSoundDeviceInfo,
        unsigned int& numWdmSoundDeviceInfo
        );

    TUsbAudioStatus
    GetWdmInputSoundDevices(
        TUsbAudioHandle deviceHandle,
        TUsbAudioWdmSoundDevice* wdmSoundDeviceInfo,
        unsigned int maxNumWdmSoundDeviceInfo,
        unsigned int& numWdmSoundDeviceInfo
        )
            {
                return GetWdmSoundDevices(deviceHandle, true, wdmSoundDeviceInfo, maxNumWdmSoundDeviceInfo, numWdmSoundDeviceInfo);
            }

    TUsbAudioStatus
    GetWdmOutputSoundDevices(
        TUsbAudioHandle deviceHandle,
        TUsbAudioWdmSoundDevice* wdmSoundDeviceInfo,
        unsigned int maxNumWdmSoundDeviceInfo,
        unsigned int& numWdmSoundDeviceInfo
        )
            {
                return GetWdmSoundDevices(deviceHandle, false, wdmSoundDeviceInfo, maxNumWdmSoundDeviceInfo, numWdmSoundDeviceInfo);
            }

    TUsbAudioStatus
    GetWdmChannelInfo(
        TUsbAudioHandle deviceHandle,
        unsigned __int64 wdmSoundDeviceId,
        TUsbAudioWdmChannelInfo* wdmChannelInfo,
        unsigned int maxNumWdmChannelInfo,
        unsigned int& numWdmChannelInfo
        );

    TUsbAudioStatus
    GetHardwareChannelCount(
        TUsbAudioHandle deviceHandle,
        unsigned int& inputHardwareChannelCount,
        unsigned int& outputHardwareChannelCount
        );

    TUsbAudioStatus
    GetVirtualChannelCount(
        TUsbAudioHandle deviceHandle,
        unsigned int& inputVirtualChannelCount,
        unsigned int& outputVirtualChannelCount
        );

    TUsbAudioStatus
    GetHardwareChannelInfo(
        TUsbAudioHandle deviceHandle,
        bool input,
        TUsbAudioHardwareChannelInfo* hardwareChannelInfo,
        unsigned int maxNumHardwareChannelInfo,
        unsigned int& numHardwareChannelInfo
        );

    TUsbAudioStatus
    GetHardwareInputChannelInfo(
        TUsbAudioHandle deviceHandle,
        TUsbAudioHardwareChannelInfo* hardwareChannelInfo,
        unsigned int maxNumHardwareChannelInfo,
        unsigned int& numHardwareChannelInfo
        )
            {
                return GetHardwareChannelInfo(deviceHandle, true, hardwareChannelInfo, maxNumHardwareChannelInfo, numHardwareChannelInfo);
            }

    TUsbAudioStatus
    GetHardwareOutputChannelInfo(
        TUsbAudioHandle deviceHandle,
        TUsbAudioHardwareChannelInfo* hardwareChannelInfo,
        unsigned int maxNumHardwareChannelInfo,
        unsigned int& numHardwareChannelInfo
        )
            {
                return GetHardwareChannelInfo(deviceHandle, false, hardwareChannelInfo, maxNumHardwareChannelInfo, numHardwareChannelInfo);
            }

    TUsbAudioStatus
    GetHardwareChannelName(
        TUsbAudioHandle deviceHandle,
        unsigned __int64 channelId,
        TUsbAudioHardwareChannelName& hardwareChannelInfo
        );

    TUsbAudioStatus
    GetValidSoundDeviceProfiles(
        TUsbAudioHandle deviceHandle,
        unsigned int streamFormatId,
        TUsbAudioSoundDeviceProfileInfo* soundDeviceProfileInfo,
        unsigned int maxNumSoundDeviceProfileInfo,
        unsigned int& numSoundDeviceProfileInfo
        );

    TUsbAudioStatus
    GetCurrentSoundDeviceProfile(
        TUsbAudioHandle deviceHandle,
        unsigned int streamFormatId,
        TUsbAudioSoundDeviceProfileInfo& soundDeviceProfileInfo
        );

    TUsbAudioStatus
    GetHardwareChannelHiddenFlags(
        TUsbAudioHandle deviceHandle,
        unsigned int streamFormatId,
        TUsbAudioHardwareChannelHiddenFlags& hiddenFlags
        );

    TUsbAudioStatus
    GetValidVirtualSoundDeviceProfiles(
        TUsbAudioHandle deviceHandle,
        bool input,
        TUsbAudioSoundDeviceProfileInfo* soundDeviceProfileInfo,
        unsigned int maxNumSoundDeviceProfileInfo,
        unsigned int& numSoundDeviceProfileInfo
        );

    TUsbAudioStatus
    GetCurrentVirtualSoundDeviceProfile(
        TUsbAudioHandle deviceHandle,
        bool input,
        TUsbAudioSoundDeviceProfileInfo& soundDeviceProfileInfo
        );

protected:
    TUsbAudioApiDll& mApiDll;

};

#endif //__TUsbAudioApiExtendedInfo_H__
