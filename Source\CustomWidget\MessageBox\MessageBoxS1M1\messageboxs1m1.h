#ifndef MESSAGEBOXS1M1_H
#define MESSAGEBOXS1M1_H


#include <QFont>
#include <QLabel>
#include <QString>
#include <QWidget>
#include <QDialog>
#include <QPushButton>
#include <QResizeEvent>

#include "framelesswindow.h"


class MessageBoxS1M1_WidgetS1M1 : public QWidget
{
    Q_OBJECT
public:
    explicit MessageBoxS1M1_WidgetS1M1(QWidget* parent=nullptr);
    ~MessageBoxS1M1_WidgetS1M1() = default;
    MessageBoxS1M1_WidgetS1M1& setFont(QFont font);
    MessageBoxS1M1_WidgetS1M1& setTextTop(QString text);
    MessageBoxS1M1_WidgetS1M1& setTextMiddle(QString text);
    MessageBoxS1M1_WidgetS1M1& setTextBottom(QString text);
    MessageBoxS1M1_WidgetS1M1& setTextButtonYes(QString text);
    MessageBoxS1M1_WidgetS1M1& setTextButtonNo(QString text);
    MessageBoxS1M1_WidgetS1M1& setColorTextTop(QColor color);
    MessageBoxS1M1_WidgetS1M1& setColorTextMiddle(QColor color);
    MessageBoxS1M1_WidgetS1M1& setColorTextBottom(QColor color);
    MessageBoxS1M1_WidgetS1M1& setColorTextButtonYes(QColor color, QColor colorBG);
    MessageBoxS1M1_WidgetS1M1& setColorTextButtonNo(QColor color, QColor colorBG);
protected:
    void resizeEvent(QResizeEvent* e) override;
private:
    QFont mFont;
    QLabel mLabelTop;
    QLabel mLabelMiddle;
    QLabel mLabelBottom;
    QPushButton mPushButtonYes;
    QPushButton mPushButtonNo;
private slots:
    void in_mPushButtonAll_clicked();
signals:
    void buttonClicked(QString button);
};


class MessageBoxS1M1 : public FramelessWindow
{
    Q_OBJECT
public:
    explicit MessageBoxS1M1(FramelessWindow* parent=nullptr);
    ~MessageBoxS1M1() = default;
    MessageBoxS1M1& setFont(QFont font);
    MessageBoxS1M1& setTextTop(QString text);
    MessageBoxS1M1& setTextMiddle(QString text);
    MessageBoxS1M1& setTextBottom(QString text);
    MessageBoxS1M1& setTextButtonYes(QString text);
    MessageBoxS1M1& setTextButtonNo(QString text);
    MessageBoxS1M1& setColorTitle(QColor color, QColor colorBG);
    MessageBoxS1M1& setColorBody(QColor color);
    MessageBoxS1M1& setColorTextTop(QColor color);
    MessageBoxS1M1& setColorTextMiddle(QColor color);
    MessageBoxS1M1& setColorTextBottom(QColor color);
    MessageBoxS1M1& setColorTextButtonYes(QColor color, QColor colorBG);
    MessageBoxS1M1& setColorTextButtonNo(QColor color, QColor colorBG);
    enum Result
    {
        Rejected=QDialog::Rejected,
        Yes,
        No
    };
private:
    MessageBoxS1M1_WidgetS1M1 mWidget;
private slots:
    void in_mWidget_buttonClicked(QString button);
};


#endif // MESSAGEBOXS1M1_H

