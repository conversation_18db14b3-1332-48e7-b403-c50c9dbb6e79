#ifndef PUSHBUTTONS1M10_H
#define PUSHBUTTONS1M10_H


#include <QLabel>
#include <QWidget>
#include <QPushButton>
#include <QResizeEvent>


class PushButtonS1M10 : public QWidget
{
    Q_OBJECT
public:
    explicit PushButtonS1M10(QWidget* parent=nullptr);
    ~PushButtonS1M10();
    enum ButtonID
    {
        buttonMic1=0,
        buttonMic35,
        buttonMicHP,
        button48V,
        buttonAUTO
    };
    PushButtonS1M10& setFont(QFont font);
    PushButtonS1M10& setPushButtonStateMic1(bool state);
    PushButtonS1M10& setPushButtonStateMic35(bool state);
    PushButtonS1M10& setPushButtonStateMicHP(bool state);
    PushButtonS1M10& setPushButtonState48V(bool state);
    PushButtonS1M10& setPushButtonStateAUTO(bool state);
    bool getPushButtonStateMic1();
    bool getPushButtonStateMic35();
    bool getPushButtonStateMicHP();
    bool getPushButtonState48V();
    bool getPushButtonStateAUTO();
protected:
    void resizeEvent(QResizeEvent* e) override;
private:
    QFont mFont;
    bool mPushButtonStateMic1=false;
    bool mPushButtonStateMic35=false;
    bool mPushButtonStateMicHP=false;
    bool mPushButtonState48V=false;
    bool mPushButtonStateAUTO=false;
    QPushButton mPushButtonMic1;
    QPushButton mPushButtonMic35;
    QPushButton mPushButtonMicHP;
    QPushButton mPushButton48V;
    QPushButton mPushButtonAUTO;
    QLabel mLabelMic1;
    QLabel mLabelMic35;
    QLabel mLabelMicHP;
    int mRadius=0;
private slots:
    void in_mPushButtonMic1_clicked();
    void in_mPushButtonMic35_clicked();
    void in_mPushButtonMicHP_clicked();
    void in_mPushButton48V_clicked();
    void in_mPushButtonAUTO_clicked();
signals:
    void buttonStateChanged(ButtonID button, bool state);
};


#endif // PUSHBUTTONS1M10_H

