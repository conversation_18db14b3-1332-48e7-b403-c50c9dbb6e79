#ifndef EQWIDGETITEMDATA_H
#define EQWIDGETITEMDATA_H

#include <QString>
#include <QVector>
#include <QPair>

struct EqWidgetItemData {
    int index;
    QString type;
    float gain;
    float frequency;
    float qValue;
    bool enabled;

    uint8_t changeFlags;

    enum ChangeFlag : uint8_t {
        TYPE_CHANGED      = 0x01,
        GAIN_CHANGED      = 0x02,
        FREQUENCY_CHANGED = 0x04,
        QVALUE_CHANGED    = 0x08,
        ENABLED_CHANGED   = 0x10,
        ALL_CHANGED       = 0x1F
    };

    EqWidgetItemData(int index, const QString& type="0", float gain=0, float frequency=1000, float qValue=0.7, bool enabled=true)
        : index(index)
        , type(type)
        , gain(gain)
        , frequency(frequency)
        , qValue(qValue)
        , enabled(enabled)
        , changeFlags(0)
    {}

    void setChanged(ChangeFlag flag) { changeFlags |= flag; }
    void setChanged(uint8_t flags) { changeFlags |= flags; }

    void clearChanged(ChangeFlag flag) { changeFlags &= ~flag; }
    void clearAllChanged() { changeFlags = 0; }

    bool isChanged(ChangeFlag flag) const { return (changeFlags & flag) != 0; }
    bool hasAnyChanged() const { return changeFlags != 0; }
    uint8_t getChangeFlags() const { return changeFlags; }

    bool operator==(const EqWidgetItemData& other) const {
        return type == other.type &&
               gain == other.gain &&
               frequency == other.frequency &&
               qValue == other.qValue &&
               enabled == other.enabled;
    }

    bool isTypeChanged() const { return isChanged(TYPE_CHANGED); }
    bool isGainChanged() const { return isChanged(GAIN_CHANGED); }
    bool isFrequencyChanged() const { return isChanged(FREQUENCY_CHANGED); }
    bool isQValueChanged() const { return isChanged(QVALUE_CHANGED); }
    bool isEnabledChanged() const { return isChanged(ENABLED_CHANGED); }
    QVector<QPair<QString,QString>> getChangedAttributes() const {
        if(!hasAnyChanged()) return {};
        QVector<QPair<QString,QString>> changedAttributes;
        if (isTypeChanged()) changedAttributes.append({"Type", type});
        if (isGainChanged()) changedAttributes.append({"Gain", QString::number(gain)});
        if (isFrequencyChanged()) changedAttributes.append({"Freq", QString::number(frequency)});
        if (isQValueChanged()) changedAttributes.append({"Q", QString::number(qValue)});
        if (isEnabledChanged()) changedAttributes.append({"Switch", QString::number(enabled)});
        return changedAttributes;
    }

    void updateType(const QString& newType, bool isMarkChanged = true) {
        if (type != newType) {
            type = newType;
            if(isMarkChanged)
                setChanged(TYPE_CHANGED);
        }
    }

    void updateGain(float newGain, bool isMarkChanged = true) {
        if (gain != newGain) {
            gain = newGain;
            if(isMarkChanged)
                setChanged(GAIN_CHANGED);
        }
    }

    void updateFrequency(float newFrequency, bool isMarkChanged = true) {
        if (frequency != newFrequency) {
            frequency = newFrequency;
            if(isMarkChanged)
                setChanged(FREQUENCY_CHANGED);
        }
    }

    void updateQValue(float newQValue, bool isMarkChanged = true) {
        if (qValue != newQValue) {
            qValue = newQValue;
            if(isMarkChanged)
                setChanged(QVALUE_CHANGED);
        }
    }

    void updateEnabled(bool newEnabled, bool isMarkChanged = true) {
        if (enabled != newEnabled) {
            enabled = newEnabled;
            if(isMarkChanged)
                setChanged(ENABLED_CHANGED);
        }
    }

    void updateData(const QString& newType, float newGain, float newFrequency,
                   float newQValue, bool newEnabled, bool isMarkChanged = true) {
        updateType(newType, isMarkChanged);
        updateGain(newGain, isMarkChanged);
        updateFrequency(newFrequency, isMarkChanged);
        updateQValue(newQValue, isMarkChanged);
        updateEnabled(newEnabled, isMarkChanged);
    }
};

#endif // EQWIDGETITEMDATA_H
