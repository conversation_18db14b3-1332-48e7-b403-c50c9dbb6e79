#ifndef ORIGINS1M10__H
#define ORIGINS1M10__H


#include <QFont>
#include <QEvent>
#include <QTimer>
#include <QObject>
#include <QWidget>
#include <QResizeEvent>

#include "originbase.h"
#include "workspace.h"
#include "appsettings.h"
#include "autogains1m1.h"


namespace Ui {
class OriginS1M10;
}


class OriginS1M10 : public OriginBase, public WorkspaceObserver, public AppSettingsObserver
{
    Q_OBJECT
public:
    explicit OriginS1M10(QWidget* parent=nullptr, QString name="");
    ~OriginS1M10();
    OriginS1M10& setName(QString name);
    OriginS1M10& setFont(QFont font);
    OriginS1M10& setVolumeMeter(int value);
    OriginS1M10& setVolumeMeterClear();
    OriginS1M10& setVolumeMeterSlip();
    OriginS1M10& setGain(float value);
    OriginS1M10& setGainLock(bool state=true);
    OriginS1M10& setMuteAffectGain(bool state=true);
    OriginS1M10& setGainAffectMute(bool state=true);
    OriginS1M10& setGainRange(float min, float max);
    OriginS1M10& setGainDefault(float value);
    OriginS1M10& setGainWidgetDisable(float value);
    OriginS1M10& setBalanceDefault(float value);
    OriginS1M10& setChannelNameEditable(bool state=true);
    OriginS1M10& setValueMIC(QString mic);
    OriginS1M10& setValue48V(bool state=true);
    OriginS1M10& setValueDucking(bool state=true);
    OriginS1M10& setValueEQ(bool state=true);
    OriginS1M10& setValueGAIN(float value);
    OriginS1M10& setValueMUTE(bool state=true);
    OriginS1M10& setOverlay(bool state=true);
    OriginS1M10& setAutoGainChannelString(QHash<QString, QString>&& string);
protected:
    bool eventFilter(QObject* obj, QEvent* e) override;
    void resizeEvent(QResizeEvent* e) override;
    void updateAttribute() override;
    void loadSettings() override;
    void AppSettingsChanged(QString objectName, QString attribute, QString value) override;
private:
    Ui::OriginS1M10* ui;
    AutoGainS1M1 mAutoGain;
    QTimer mTimer;
    QFont mFont;
    QString mPreMIC="";
    int mBalanceDefault=100;
    int mPre48V=-2147483648;
    int mPreMUTE=-2147483648;
    float mPreGAIN=-2147483648;
    float mDisableGAIN=-88;
    bool mMuteAffectGain=false;
    bool mGainAffectMute=false;
    void save(QAnyStringView key, const QVariant& value);
private slots:
    void in_mAutoGain_attributeChanged(QString attribute, QString value);
    void in_mTimer_timeout();
    void in_widgetPushButtonGroup1_stateChanged(QString button, QString state);
    void in_widgetPushButtonGroup2_stateChanged(QString button, QString state);
    void in_widgetPushButtonGroup3_stateChanged(QString button, QString state);
    void in_widgetDial_valueChanged(float value);
    void on_lineEdit_textChanged(const QString& arg1);
    void on_lineEdit_editingFinished();
    void on_pushButtonClose_clicked();
};


#endif // ORIGINS1M10__H

