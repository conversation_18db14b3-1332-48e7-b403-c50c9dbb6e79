/************************************************************************
 *
 *  Module:       WnTraceLogFile.h
 *
 *  Description:  LogFile wrapper
 *
 *  Runtime Env.: Win32
 *
 *  Author(s):
 *    <PERSON>, <PERSON>@thesycon.de
 *    Udo <PERSON>,  <EMAIL>
 *
 *  Companies:
 *    Thesycon GmbH, Germany      http://www.thesycon.de
 *
 ************************************************************************/

#ifndef __WnTraceLogFile_h__
#define __WnTraceLogFile_h__

// optionally put everything into a namespace
#ifdef LIBWN_NAMESPACE
namespace LIBWN_NAMESPACE {
#endif

//
// WnTraceLogFile
//
// An object of this class is NOT thread safe!
//
class WnTraceLogFile
{
public:
    // constructor
    WnTraceLogFile();

    // destructor
    ~WnTraceLogFile();


/////////////////////////////////////////
// Defs
/////////////////////////////////////////
public:

    // upper limit for log file size, in bytes
    enum { cMaxLogFileSizeLimit = LONG_MAX }; // 2GB

    // default limit for log file size, in bytes
    enum { cDefaultLogFileSizeLimit = 1024*1024*10 }; // 10MB

    // size, in chars, of an internal temporary buffer used by TranslateLFandWriteToFile
    enum { cTranslateTempBufferSize = 512 };


/////////////////////////////////////////
// Methods
/////////////////////////////////////////
public:

    // open log file
    // returns true if successful, false if failed
    bool
    OpenFile(
        const TCHAR* fileName,    // file name to be passed to fopen() unmodified
        bool appendToExistingFile = true,
        bool insertPidIntoFileName = false  // insert process ID into file name
        );

    // close log file
    void
    CloseFile();


    // returns true if the file is opened
    bool
    IsOpen()
            { return (mFile != NULL); }


    // write the specified number of bytes to the file
    // returns true if successful
    bool
    WriteToFile(
        const char* ptr,
        unsigned int byteCount
        );

    // Perform a LF to CR/LF conversion and
    // write the specified number of bytes to the file
    // returns true if successful
    bool
    TranslateLFandWriteToFile(
        const char* ptr,
        unsigned int byteCount
        );


    // Set max size of log files, in bytes
    // default is cDefaultLogFileSizeLimit
    void
    SetLogFileSizeLimit(
        unsigned int maxSizeInBytes
        );

    // Set flush mode
    void
    SetFlushAfterEachWrite(
        bool enable
        );


/////////////////////////////////////////
// Implementation
/////////////////////////////////////////
protected:

    void
    CreateBackupFile();

    bool
    OpenLogFile();

    bool
    UpdateCurrentFileSize();

    // to be overwritten by subclass
    virtual
    bool
    WriteToFileImpl(
        const char* ptr,
        unsigned int byteCount
        );



/////////////////////////////////////////
// Data Members
/////////////////////////////////////////
protected:

    FILE* mFile;

    // file name
    TCHAR mFileName[MAX_PATH];
    // backup extension added to file name
    TCHAR mFileNameBackupExt[5];

    // current size, in bytes
    unsigned int mCurrentFileSize;
    // max file size, in bytes, 0 for no limit
    unsigned int mMaxFileSize;

    // current state
    bool mCRDetected;

    // append mode
    bool mAppendToExistingFile;
    // flush mode
    bool mFlushAfterEachWrite;


}; // class WnTraceLogFile

#ifdef LIBWN_NAMESPACE
}
#endif

#endif // __WnTraceLogFile_h__

/*************************** EOF **************************************/
