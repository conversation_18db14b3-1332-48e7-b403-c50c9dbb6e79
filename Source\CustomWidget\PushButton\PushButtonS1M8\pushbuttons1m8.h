#ifndef PUSHBUTTONS1M8_H
#define PUSHBUTTONS1M8_H


#include <QLabel>
#include <QWidget>
#include <QPushButton>
#include <QResizeEvent>


class PushButtonS1M8 : public QWidget
{
    Q_OBJECT
public:
    explicit PushButtonS1M8(QWidget* parent=nullptr);
    ~PushButtonS1M8();
    enum ButtonID
    {
        buttonMic1=0,
        buttonMic35,
        buttonMicHP,
        button48V,
        buttonMUTE,
    };
    PushButtonS1M8& setFont(QFont font);
    PushButtonS1M8& setPushButtonStateMic1(bool state);
    PushButtonS1M8& setPushButtonStateMic35(bool state);
    PushButtonS1M8& setPushButtonStateMicHP(bool state);
    PushButtonS1M8& setPushButtonState48V(bool state);
    PushButtonS1M8& setPushButtonStateMUTE(bool state);
    PushButtonS1M8& setPushButtonClickedMUTE(bool state);
    bool getPushButtonStateMic1();
    bool getPushButtonStateMic35();
    bool getPushButtonStateMicHP();
    bool getPushButtonState48V();
    bool getPushButtonStateMUTE();
protected:
    void resizeEvent(QResizeEvent* e) override;
private:
    QFont mFont;
    bool mPushButtonStateMic1=false;
    bool mPushButtonStateMic35=false;
    bool mPushButtonStateMicHP=false;
    bool mPushButtonState48V=false;
    bool mPushButtonStateMUTE=false;
    QPushButton mPushButtonMic1;
    QPushButton mPushButtonMic35;
    QPushButton mPushButtonMicHP;
    QPushButton mPushButton48V;
    QPushButton mPushButtonMUTE;
    QLabel mLabelMic1;
    QLabel mLabelMic35;
    QLabel mLabelMicHP;
private slots:
    void in_mPushButtonMic1_clicked();
    void in_mPushButtonMic35_clicked();
    void in_mPushButtonMicHP_clicked();
    void in_mPushButton48V_clicked();
    void in_mPushButtonMUTE_clicked();
signals:
    void buttonStateChanged(ButtonID button, bool state);
};


#endif // PUSHBUTTONS1M8_H

