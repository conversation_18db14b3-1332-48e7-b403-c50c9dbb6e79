#include "globalfont.h"
#include "pushbuttongroups1m4.h"
#include "ui_pushbuttongroups1m4.h"


PushButtonGroupS1M4::PushButtonGroupS1M4(QWidget *parent)
    : QWidget(parent)
    , ui(new Ui::PushButtonGroupS1M4)
{
    ui->setupUi(this);
    ui->PushButtonMUTE->setCheckable(true);
    ui->PushButtonMUTE->setAttribute(Qt::WA_LayoutUsesWidgetRect);
    mStyle[0] = "QFrame { image: url(:/Image/PushButtonGroup/PBG1_0.png); }";
    mStyle[1] = "QFrame { image: url(:/Image/PushButtonGroup/PBG1_1.png); }";
    setState("MUTE", "0", false);
    setLanguage("English");
}
PushButtonGroupS1M4::~PushButtonGroupS1M4()
{
    delete ui;
}


// override
void PushButtonGroupS1M4::resizeEvent(QResizeEvent* e)
{
    Q_UNUSED(e);
    mFont.setPointSize(GLBFHandle.getSuitablePointSize(mFont, ui->PushButtonMUTE->height()) - 1);
    ui->PushButtonMUTE->setFont(mFont);
}


// slot
void PushButtonGroupS1M4::on_PushButtonMUTE_clicked(bool checked)
{
    setState("MUTE", QString::number(checked));
}


// setter & getter
PushButtonGroupS1M4& PushButtonGroupS1M4::setFont(QFont font)
{
    mFont = font;
    QResizeEvent e(size(), size());
    resizeEvent(&e);
    update();
    return *this;
}
PushButtonGroupS1M4& PushButtonGroupS1M4::setLanguage(QString language)
{
    if(language == "English")
    {
        // ui->PushButtonMUTE->setText("MUTE");
    }
    else if(language == "Chinese")
    {
        // ui->PushButtonMUTE->setText("MUTE");
    }
    return *this;
}
PushButtonGroupS1M4& PushButtonGroupS1M4::setState(QString button, QString state, bool needEmit)
{
    unsigned int bitMask=0;
    QPushButton* currentButton=nullptr;
    if(button == "MUTE")
    {
        bitMask = 0x0001;
        currentButton = ui->PushButtonMUTE;
    }
    if(currentButton != nullptr)
    {
        mBitmap &= ~bitMask;
        if(state.toInt()) mBitmap |= bitMask;
        ui->frame->setStyleSheet(mStyle.value(mBitmap));
        currentButton->setChecked(state.toInt());
        currentButton->setStyleSheet(state.toInt() ? "QPushButton { color: rgb(222, 222, 222); }" : "QPushButton { color: rgb(161, 161, 161); }");
        if(needEmit) emit stateChanged(button, state);
    }
    return *this;
}
QString PushButtonGroupS1M4::getState(QString button)
{
    QPushButton* currentButton=nullptr;
    if(button == "MUTE")
    {
        currentButton = ui->PushButtonMUTE;
    }
    if(currentButton != nullptr)
    {
        return QString::number(currentButton->isChecked());
    }
    return QString::number(false);
}

