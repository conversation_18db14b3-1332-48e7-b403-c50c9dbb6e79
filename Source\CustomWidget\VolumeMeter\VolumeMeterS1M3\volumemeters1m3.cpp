#include "globalfont.h"
#include "volumemeters1m3.h"


VolumeMeterS1M3::VolumeMeterS1M3(QWidget* parent)
    : QWidget(parent)
{
    mRectMeterLeftOrigin.timerClip.setSingleShot(true);
    mRectMeterLeftGained.timerClip.setSingleShot(true);
    mRectMeterRightOrigin.timerClip.setSingleShot(true);
    mRectMeterRightGained.timerClip.setSingleShot(true);
    connect(&mRectMeterLeftOrigin.timerClip, SIGNAL(timeout()), this, SLOT(in_timerClipLeftOrigin_timeout()), Qt::UniqueConnection);
    connect(&mRectMeterLeftGained.timerClip, SIGNAL(timeout()), this, SLOT(in_timerClipLeftGained_timeout()), Qt::UniqueConnection);
    connect(&mRectMeterRightOrigin.timerClip, SIGNAL(timeout()), this, SLOT(in_timerClipRightOrigin_timeout()), Qt::UniqueConnection);
    connect(&mRectMeterRightGained.timerClip, SIGNAL(timeout()), this, SLOT(in_timerClipRightGained_timeout()), Qt::UniqueConnection);
    connect(&mTimerMeterLeft, SIGNAL(timeout()), this, SLOT(in_mTimerMeterLeft_timeout()), Qt::UniqueConnection);
    connect(&mTimerMeterRight, SIGNAL(timeout()), this, SLOT(in_mTimerMeterRight_timeout()), Qt::UniqueConnection);
}
VolumeMeterS1M3::~VolumeMeterS1M3()
{

}


// override
void VolumeMeterS1M3::resizeEvent(QResizeEvent* e)
{
    Q_UNUSED(e);
    // W
    float wPixelPerRatio=size().width() / 100.0;
    int wMeterLeftOrigin=wPixelPerRatio * mMeterOrigin;
    int wSpace1=wPixelPerRatio * mSpace1;
    int wMeterLeftGained=wPixelPerRatio * mMeterGained;
    int wSpace2=wPixelPerRatio * mSpace2;
    int wScale=wPixelPerRatio * mScale;
    int wSpace3=wPixelPerRatio * mSpace2;
    int wMeterRightGained=wPixelPerRatio * mMeterGained;
    int wSpace4=wPixelPerRatio * mSpace1;
    int wMeterRightOrigin=wPixelPerRatio * mMeterOrigin;
    int wRemain=size().width() - wMeterLeftOrigin - wSpace1 - wMeterLeftGained - wSpace2 - wScale - wSpace3 - wMeterRightGained - wSpace4 - wMeterRightOrigin;
    int xMeterLeftOrigin=0 + wRemain / 2;
    int xMeterLeftGained=xMeterLeftOrigin + wMeterLeftOrigin + wSpace1;
    int xScale=xMeterLeftGained + wMeterLeftGained + wSpace2;
    int xMeterRightGained=xScale + wScale + wSpace3;
    int xMeterRightOrigin=xMeterRightGained + wMeterRightGained + wSpace4;
    // H
    float hPixelPerRatio=size().height() / 100.0;
    int hClip=hPixelPerRatio * mHClip;
    int hSpace2=hPixelPerRatio * mHSpace1;
    int hVolume=hPixelPerRatio * mHVolume;
    int hSpace3=hPixelPerRatio * mHSpace2;
    int hRemain=size().height() - hClip - hSpace2 - hVolume - hSpace3;
    int yClip=0 + hRemain / 2;
    int yClipEnd=yClip + hClip;
    int yVolume=yClipEnd + hSpace2;
    mRectMeterLeftOrigin.clip.setRect(xMeterLeftOrigin, yClip, wMeterLeftOrigin, hClip);
    mRectMeterLeftOrigin.volume.setRect(xMeterLeftOrigin, yVolume, wMeterLeftOrigin, hVolume);
    mRectMeterLeftGained.clip.setRect(xMeterLeftGained, yClip, wMeterLeftGained, hClip);
    mRectMeterLeftGained.volume.setRect(xMeterLeftGained, yVolume, wMeterLeftGained, hVolume);
    mRectScale.setRect(xScale, 0, wScale, size().height());
    mRectMeterRightGained.clip.setRect(xMeterRightGained, yClip, wMeterRightGained, hClip);
    mRectMeterRightGained.volume.setRect(xMeterRightGained, yVolume, wMeterRightGained, hVolume);
    mRectMeterRightOrigin.clip.setRect(xMeterRightOrigin, yClip, wMeterRightOrigin, hClip);
    mRectMeterRightOrigin.volume.setRect(xMeterRightOrigin, yVolume, wMeterRightOrigin, hVolume);
}
void VolumeMeterS1M3::paintEvent(QPaintEvent* e)
{
    Q_UNUSED(e);
    QPainter painter(this);
    painter.setRenderHints(QPainter::Antialiasing | QPainter::TextAntialiasing);
    drawBG(&painter);
    drawElement(&painter);
}
void VolumeMeterS1M3::mouseDoubleClickEvent(QMouseEvent* e)
{
    if(e->button() == Qt::LeftButton)
    {
        if(mRectMeterLeftOrigin.clip.contains(e->pos()))
        {
            mRectMeterLeftOrigin.clipStatus = false;
            update();
        }
        else if(mRectMeterLeftGained.clip.contains(e->pos()))
        {
            mRectMeterLeftGained.clipStatus = false;
            update();
        }
        else if(mRectMeterRightOrigin.clip.contains(e->pos()))
        {
            mRectMeterRightOrigin.clipStatus = false;
            update();
        }
        else if(mRectMeterRightGained.clip.contains(e->pos()))
        {
            mRectMeterRightGained.clipStatus = false;
            update();
        }
    }
}
void VolumeMeterS1M3::drawBG(QPainter* painter)
{
    painter->save();
    painter->setPen(Qt::NoPen);
    painter->setBrush(mColorBG);
    painter->drawRect(rect());
    painter->restore();
}
void VolumeMeterS1M3::drawElement(QPainter* painter)
{
    float pixelPerScale;
    QPointF textPoint;
    QRect rectMeter;
    painter->save();
    // font
    mFont.setPointSize(GLBFHandle.getSuitablePointSize(mFont, "-90", mRectScale.width()));
    painter->setFont(mFont);
    // clip
    painter->setPen(Qt::NoPen);
    mRectMeterLeftOrigin.clipStatus ? (painter->setBrush(QBrush(QColor(246, 72, 71)))) : (painter->setBrush(QBrush(QColor(60, 60, 60))));
    painter->drawRoundedRect(mRectMeterLeftOrigin.clip, mRectMeterLeftOrigin.clip.height() / 3, mRectMeterLeftOrigin.clip.height() / 3);
    mRectMeterLeftGained.clipStatus ? (painter->setBrush(QBrush(QColor(246, 72, 71)))) : (painter->setBrush(QBrush(QColor(60, 60, 60))));
    painter->drawRoundedRect(mRectMeterLeftGained.clip, mRectMeterLeftGained.clip.height() / 3, mRectMeterLeftGained.clip.height() / 3);
    mRectMeterRightOrigin.clipStatus ? (painter->setBrush(QBrush(QColor(246, 72, 71)))) : (painter->setBrush(QBrush(QColor(60, 60, 60))));
    painter->drawRoundedRect(mRectMeterRightOrigin.clip, mRectMeterRightOrigin.clip.height() / 3, mRectMeterRightOrigin.clip.height() / 3);
    mRectMeterRightGained.clipStatus ? (painter->setBrush(QBrush(QColor(246, 72, 71)))) : (painter->setBrush(QBrush(QColor(60, 60, 60))));
    painter->drawRoundedRect(mRectMeterRightGained.clip, mRectMeterRightGained.clip.height() / 3, mRectMeterRightGained.clip.height() / 3);
    // volume
    painter->setBrush(QBrush(QColor(60, 60, 60)));
    painter->drawRoundedRect(mRectMeterLeftOrigin.volume, mRectMeterLeftOrigin.volume.width() / 3, mRectMeterLeftOrigin.volume.width() / 3);
    painter->drawRoundedRect(mRectMeterLeftGained.volume, mRectMeterLeftGained.volume.width() / 3, mRectMeterLeftGained.volume.width() / 3);
    painter->drawRoundedRect(mRectMeterRightOrigin.volume, mRectMeterRightOrigin.volume.width() / 3, mRectMeterRightOrigin.volume.width() / 3);
    painter->drawRoundedRect(mRectMeterRightGained.volume, mRectMeterRightGained.volume.width() / 3, mRectMeterRightGained.volume.width() / 3);

    painter->setPen(Qt::NoPen);
    QLinearGradient gradient(mRectMeterLeftOrigin.volume.topLeft(), mRectMeterLeftOrigin.volume.bottomLeft());
    gradient.setColorAt(0.0, QColor("#f64847"));
    gradient.setColorAt(0.0666, QColor("#a77d43"));
    gradient.setColorAt(0.1333, QColor("#0f9640"));
    gradient.setColorAt(1.0, QColor("#009641"));
    painter->setBrush(gradient);
    pixelPerScale = mRectMeterLeftOrigin.volume.height() / 90.0;
    rectMeter = mRectMeterLeftOrigin.volume;
    rectMeter.setY(rectMeter.y() + pixelPerScale * (0 - mRectMeterLeftOrigin.volumeValue));
    rectMeter.setHeight(rectMeter.bottomLeft().y() - rectMeter.y());
    if(mRectMeterLeftOrigin.volumeValue != -90)
    {
        painter->drawRoundedRect(rectMeter, rectMeter.width() / 3, rectMeter.width() / 3);
    }
    gradient.setStart(mRectMeterLeftGained.volume.topLeft());
    gradient.setFinalStop(mRectMeterLeftGained.volume.bottomLeft());
    painter->setBrush(gradient);
    pixelPerScale = mRectMeterLeftGained.volume.height() / 90.0;
    rectMeter = mRectMeterLeftGained.volume;
    rectMeter.setY(rectMeter.y() + pixelPerScale * (0 - mRectMeterLeftGained.volumeValue));
    rectMeter.setHeight(rectMeter.bottomLeft().y() - rectMeter.y());
    if(mRectMeterLeftGained.volumeValue != -90)
    {
        painter->drawRoundedRect(rectMeter, rectMeter.width() / 3, rectMeter.width() / 3);
    }
    gradient.setStart(mRectMeterRightOrigin.volume.topLeft());
    gradient.setFinalStop(mRectMeterRightOrigin.volume.bottomLeft());
    painter->setBrush(gradient);
    pixelPerScale = mRectMeterRightOrigin.volume.height() / 90.0;
    rectMeter = mRectMeterRightOrigin.volume;
    rectMeter.setY(rectMeter.y() + pixelPerScale * (0 - mRectMeterRightOrigin.volumeValue));
    rectMeter.setHeight(rectMeter.bottomLeft().y() - rectMeter.y());
    if(mRectMeterRightOrigin.volumeValue != -90)
    {
        painter->drawRoundedRect(rectMeter, rectMeter.width() / 3, rectMeter.width() / 3);
    }
    gradient.setStart(mRectMeterRightGained.volume.topLeft());
    gradient.setFinalStop(mRectMeterRightGained.volume.bottomLeft());
    painter->setBrush(gradient);
    pixelPerScale = mRectMeterRightGained.volume.height() / 90.0;
    rectMeter = mRectMeterRightGained.volume;
    rectMeter.setY(rectMeter.y() + pixelPerScale * (0 - mRectMeterRightGained.volumeValue));
    rectMeter.setHeight(rectMeter.bottomLeft().y() - rectMeter.y());
    if(mRectMeterRightGained.volumeValue != -90)
    {
        painter->drawRoundedRect(rectMeter, rectMeter.width() / 3, rectMeter.width() / 3);
    }
    // scale
    int wScaleLine=mRectMeterLeftGained.volume.width() / 2;
    painter->setPen(QColor(161, 161, 161));
    painter->setBrush(Qt::NoBrush);
    textPoint.setX(mRectScale.x() + (mRectScale.width() - painter->fontMetrics().horizontalAdvance("0")) / 2);
    textPoint.setY(mRectMeterLeftGained.volume.y() + 0 * pixelPerScale);
    if(!mScaleLineHidden) painter->drawLine(mRectMeterLeftGained.volume.x() + mRectMeterLeftGained.volume.width(), textPoint.y() + 1, mRectMeterLeftGained.volume.x() + mRectMeterLeftGained.volume.width() + wScaleLine, textPoint.y() + 1);
    if(!mScaleLineHidden) painter->drawLine(mRectMeterRightGained.volume.x(), textPoint.y() + 1, mRectMeterRightGained.volume.x() - wScaleLine, textPoint.y() + 1);
    painter->drawText(textPoint, "0");
    textPoint.setX(mRectScale.x() + (mRectScale.width() - painter->fontMetrics().horizontalAdvance("-6")) / 2);
    textPoint.setY(mRectMeterLeftGained.volume.y() + 6 * pixelPerScale);
    if(!mScaleLineHidden) painter->drawLine(mRectMeterLeftGained.volume.x() + mRectMeterLeftGained.volume.width(), textPoint.y() + 1, mRectMeterLeftGained.volume.x() + mRectMeterLeftGained.volume.width() + wScaleLine, textPoint.y() + 1);
    if(!mScaleLineHidden) painter->drawLine(mRectMeterRightGained.volume.x(), textPoint.y() + 1, mRectMeterRightGained.volume.x() - wScaleLine, textPoint.y() + 1);
    painter->drawText(textPoint, "-6");
    textPoint.setX(mRectScale.x() + (mRectScale.width() - painter->fontMetrics().horizontalAdvance("-12")) / 2);
    textPoint.setY(mRectMeterLeftGained.volume.y() + 12 * pixelPerScale);
    if(!mScaleLineHidden) painter->drawLine(mRectMeterLeftGained.volume.x() + mRectMeterLeftGained.volume.width(), textPoint.y() + 1, mRectMeterLeftGained.volume.x() + mRectMeterLeftGained.volume.width() + wScaleLine, textPoint.y() + 1);
    if(!mScaleLineHidden) painter->drawLine(mRectMeterRightGained.volume.x(), textPoint.y() + 1, mRectMeterRightGained.volume.x() - wScaleLine, textPoint.y() + 1);
    painter->drawText(textPoint, "-12");
    textPoint.setX(mRectScale.x() + (mRectScale.width() - painter->fontMetrics().horizontalAdvance("-24")) / 2);
    textPoint.setY(mRectMeterLeftGained.volume.y() + 24 * pixelPerScale);
    if(!mScaleLineHidden) painter->drawLine(mRectMeterLeftGained.volume.x() + mRectMeterLeftGained.volume.width(), textPoint.y() + 1, mRectMeterLeftGained.volume.x() + mRectMeterLeftGained.volume.width() + wScaleLine, textPoint.y() + 1);
    if(!mScaleLineHidden) painter->drawLine(mRectMeterRightGained.volume.x(), textPoint.y() + 1, mRectMeterRightGained.volume.x() - wScaleLine, textPoint.y() + 1);
    painter->drawText(textPoint, "-24");
    textPoint.setX(mRectScale.x() + (mRectScale.width() - painter->fontMetrics().horizontalAdvance("-36")) / 2);
    textPoint.setY(mRectMeterLeftGained.volume.y() + 36 * pixelPerScale);
    if(!mScaleLineHidden) painter->drawLine(mRectMeterLeftGained.volume.x() + mRectMeterLeftGained.volume.width(), textPoint.y() + 1, mRectMeterLeftGained.volume.x() + mRectMeterLeftGained.volume.width() + wScaleLine, textPoint.y() + 1);
    if(!mScaleLineHidden) painter->drawLine(mRectMeterRightGained.volume.x(), textPoint.y() + 1, mRectMeterRightGained.volume.x() - wScaleLine, textPoint.y() + 1);
    painter->drawText(textPoint, "-36");
    textPoint.setX(mRectScale.x() + (mRectScale.width() - painter->fontMetrics().horizontalAdvance("-48")) / 2);
    textPoint.setY(mRectMeterLeftGained.volume.y() + 48 * pixelPerScale);
    if(!mScaleLineHidden) painter->drawLine(mRectMeterLeftGained.volume.x() + mRectMeterLeftGained.volume.width(), textPoint.y() + 1, mRectMeterLeftGained.volume.x() + mRectMeterLeftGained.volume.width() + wScaleLine, textPoint.y() + 1);
    if(!mScaleLineHidden) painter->drawLine(mRectMeterRightGained.volume.x(), textPoint.y() + 1, mRectMeterRightGained.volume.x() - wScaleLine, textPoint.y() + 1);
    painter->drawText(textPoint, "-48");
    textPoint.setX(mRectScale.x() + (mRectScale.width() - painter->fontMetrics().horizontalAdvance("-60")) / 2);
    textPoint.setY(mRectMeterLeftGained.volume.y() + 60 * pixelPerScale);
    if(!mScaleLineHidden) painter->drawLine(mRectMeterLeftGained.volume.x() + mRectMeterLeftGained.volume.width(), textPoint.y() + 1, mRectMeterLeftGained.volume.x() + mRectMeterLeftGained.volume.width() + wScaleLine, textPoint.y() + 1);
    if(!mScaleLineHidden) painter->drawLine(mRectMeterRightGained.volume.x(), textPoint.y() + 1, mRectMeterRightGained.volume.x() - wScaleLine, textPoint.y() + 1);
    painter->drawText(textPoint, "-60");
    textPoint.setX(mRectScale.x() + (mRectScale.width() - painter->fontMetrics().horizontalAdvance("-∞")) / 2);
    textPoint.setY(mRectMeterLeftGained.volume.y() + mRectMeterLeftGained.volume.height());
    if(!mScaleLineHidden) painter->drawLine(mRectMeterLeftGained.volume.x() + mRectMeterLeftGained.volume.width(), textPoint.y() + 1, mRectMeterLeftGained.volume.x() + mRectMeterLeftGained.volume.width() + wScaleLine, textPoint.y() + 1);
    if(!mScaleLineHidden) painter->drawLine(mRectMeterRightGained.volume.x(), textPoint.y() + 1, mRectMeterRightGained.volume.x() - wScaleLine, textPoint.y() + 1);
    painter->drawText(textPoint, "-∞");
    painter->restore();
}


// slot
void VolumeMeterS1M3::in_timerClipLeftOrigin_timeout()
{
    mRectMeterLeftOrigin.clipStatus = false;
    update();
}
void VolumeMeterS1M3::in_timerClipLeftGained_timeout()
{
    mRectMeterLeftGained.clipStatus = false;
    update();
}
void VolumeMeterS1M3::in_timerClipRightOrigin_timeout()
{
    mRectMeterRightOrigin.clipStatus = false;
    update();
}
void VolumeMeterS1M3::in_timerClipRightGained_timeout()
{
    mRectMeterRightGained.clipStatus = false;
    update();
}
void VolumeMeterS1M3::in_mTimerMeterLeft_timeout()
{
    if(mRectMeterLeftOrigin.volumeValue > -90 || mRectMeterLeftGained.volumeValue > -90)
    {
        mRectMeterLeftOrigin.volumeValue = -90 >= mRectMeterLeftOrigin.volumeValue ? (-90) : (qMax(-90, mRectMeterLeftOrigin.volumeValue - 4));
        mRectMeterLeftGained.volumeValue = -90 >= mRectMeterLeftGained.volumeValue ? (-90) : (qMax(-90, mRectMeterLeftGained.volumeValue - 4));
        update();
    }
    else
    {
        mTimerMeterLeft.stop();
    }
}
void VolumeMeterS1M3::in_mTimerMeterRight_timeout()
{
    if(mRectMeterRightOrigin.volumeValue > -90 || mRectMeterRightGained.volumeValue > -90)
    {
        mRectMeterRightOrigin.volumeValue = -90 >= mRectMeterRightOrigin.volumeValue ? (-90) : (qMax(-90, mRectMeterRightOrigin.volumeValue - 4));
        mRectMeterRightGained.volumeValue = -90 >= mRectMeterRightGained.volumeValue ? (-90) : (qMax(-90, mRectMeterRightGained.volumeValue - 4));
        update();
    }
    else
    {
        mTimerMeterRight.stop();
    }
}


// setter & getter
void VolumeMeterS1M3::setFont(QFont font)
{
    mFont = font;
    update();
}
void VolumeMeterS1M3::setColorBG(QColor color)
{
    mColorBG = color;
    update();
}
void VolumeMeterS1M3::setValueLeft(int value, int gain)
{
    if(value > 1)
    {
        return;
    }
    if(mTimerMeterLeft.isActive())
    {
        mTimerMeterLeft.stop();
    }
    if(value != 1)
    {
        if(value % 10)
        {
            value = value / 10 - 1;
        }
        else
        {
            value = value / 10;
        }
    }
    int valueGained=value + gain;
    valueGained = qMin(valueGained, 1);
    valueGained = qMax(valueGained, -90);
    value = qMax(-90, value);
    if(value == 1)
    {
        mRectMeterLeftOrigin.clipStatus = true;
        value = 0;
        mRectMeterLeftOrigin.timerClip.start(5000);
    }
    if(valueGained == 1)
    {
        mRectMeterLeftGained.clipStatus = true;
        valueGained = 0;
        mRectMeterLeftGained.timerClip.start(5000);
    }
    mRectMeterLeftOrigin.volumeValue = value >= mRectMeterLeftOrigin.volumeValue ? (value) : (qMax(-90, mRectMeterLeftOrigin.volumeValue - 4));
    mRectMeterLeftGained.volumeValue = valueGained >= mRectMeterLeftGained.volumeValue ? (valueGained) : (qMax(-90, mRectMeterLeftGained.volumeValue - 4));
    update();
}
void VolumeMeterS1M3::setValueLeftOrigin(int value)
{
    if(value > 1)
    {
        return;
    }
    if(mTimerMeterLeft.isActive())
    {
        mTimerMeterLeft.stop();
    }
    if(value != 1)
    {
        if(value % 10)
        {
            value = value / 10 - 1;
        }
        else
        {
            value = value / 10;
        }
    }
    value = qMax(-90, value);
    if(value == 1)
    {
        mRectMeterLeftOrigin.clipStatus = true;
        value = 0;
        mRectMeterLeftOrigin.timerClip.start(5000);
    }
    mRectMeterLeftOrigin.volumeValue = value >= mRectMeterLeftOrigin.volumeValue ? (value) : (qMax(-90, mRectMeterLeftOrigin.volumeValue - 4));
    update();
}
void VolumeMeterS1M3::setValueLeftGained(int value)
{
    if(value > 1)
    {
        return;
    }
    if(mTimerMeterLeft.isActive())
    {
        mTimerMeterLeft.stop();
    }
    if(value != 1)
    {
        if(value % 10)
        {
            value = value / 10 - 1;
        }
        else
        {
            value = value / 10;
        }
    }
    value = qMax(-90, value);
    if(value == 1)
    {
        mRectMeterLeftGained.clipStatus = true;
        value = 0;
        mRectMeterLeftGained.timerClip.start(5000);
    }
    mRectMeterLeftGained.volumeValue = value >= mRectMeterLeftGained.volumeValue ? (value) : (qMax(-90, mRectMeterLeftGained.volumeValue - 4));
    update();
}
void VolumeMeterS1M3::setValueRight(int value, int gain)
{
    if(value > 1)
    {
        return;
    }
    if(mTimerMeterRight.isActive())
    {
        mTimerMeterRight.stop();
    }
    if(value != 1)
    {
        if(value % 10)
        {
            value = value / 10 - 1;
        }
        else
        {
            value = value / 10;
        }
    }
    int valueGained=value + gain;
    valueGained = qMin(valueGained, 1);
    valueGained = qMax(valueGained, -90);
    value = qMax(-90, value);
    if(value == 1)
    {
        mRectMeterRightOrigin.clipStatus = true;
        value = 0;
        mRectMeterRightOrigin.timerClip.start(5000);
    }
    if(valueGained == 1)
    {
        mRectMeterRightGained.clipStatus = true;
        valueGained = 0;
        mRectMeterRightGained.timerClip.start(5000);
    }
    mRectMeterRightOrigin.volumeValue = value >= mRectMeterRightOrigin.volumeValue ? (value) : (qMax(-90, mRectMeterRightOrigin.volumeValue - 4));
    mRectMeterRightGained.volumeValue = valueGained >= mRectMeterRightGained.volumeValue ? (valueGained) : (qMax(-90, mRectMeterRightGained.volumeValue - 4));
    update();
}
void VolumeMeterS1M3::setValueRightOrigin(int value)
{
    if(value > 1)
    {
        return;
    }
    if(mTimerMeterRight.isActive())
    {
        mTimerMeterRight.stop();
    }
    if(value != 1)
    {
        if(value % 10)
        {
            value = value / 10 - 1;
        }
        else
        {
            value = value / 10;
        }
    }
    value = qMax(-90, value);
    if(value == 1)
    {
        mRectMeterRightOrigin.clipStatus = true;
        value = 0;
        mRectMeterRightOrigin.timerClip.start(5000);
    }
    mRectMeterRightOrigin.volumeValue = value >= mRectMeterRightOrigin.volumeValue ? (value) : (qMax(-90, mRectMeterRightOrigin.volumeValue - 4));
    update();
}
void VolumeMeterS1M3::setValueRightGained(int value)
{
    if(value > 1)
    {
        return;
    }
    if(mTimerMeterRight.isActive())
    {
        mTimerMeterRight.stop();
    }
    if(value != 1)
    {
        if(value % 10)
        {
            value = value / 10 - 1;
        }
        else
        {
            value = value / 10;
        }
    }
    value = qMax(-90, value);
    if(value == 1)
    {
        mRectMeterRightGained.clipStatus = true;
        value = 0;
        mRectMeterRightGained.timerClip.start(5000);
    }
    mRectMeterRightGained.volumeValue = value >= mRectMeterRightGained.volumeValue ? (value) : (qMax(-90, mRectMeterRightGained.volumeValue - 4));
    update();
}
void VolumeMeterS1M3::setMeterLeftClear()
{
    mTimerMeterLeft.stop();
    mRectMeterLeftOrigin.timerClip.stop();
    mRectMeterLeftGained.timerClip.stop();
    mRectMeterLeftOrigin.volumeValue = -90;
    mRectMeterLeftGained.volumeValue = -90;
    mRectMeterLeftOrigin.clipStatus = false;
    mRectMeterLeftGained.clipStatus = false;
    update();
}
void VolumeMeterS1M3::setMeterLeftSlip()
{
    mTimerMeterLeft.start(55);
}
void VolumeMeterS1M3::setMeterRightClear()
{
    mTimerMeterRight.stop();
    mRectMeterRightOrigin.timerClip.stop();
    mRectMeterRightGained.timerClip.stop();
    mRectMeterRightOrigin.volumeValue = -90;
    mRectMeterRightGained.volumeValue = -90;
    mRectMeterRightOrigin.clipStatus = false;
    mRectMeterRightGained.clipStatus = false;
    update();
}
void VolumeMeterS1M3::setMeterRightSlip()
{
    mTimerMeterRight.start(55);
}
void VolumeMeterS1M3::setWidthRatio(int space1, int space2, int meterOrigin, int meterGained, int scale)
{
    mSpace1 = space1;
    mSpace2 = space2;
    mMeterOrigin = meterOrigin;
    mMeterGained = meterGained;
    mScale = scale;
    QResizeEvent e(size(), size());
    resizeEvent(&e);
    update();
}
void VolumeMeterS1M3::setHeightRatio(int clip, int space1, int volume, int space2)
{
    mHClip = clip;
    mHSpace1 = space1;
    mHVolume = volume;
    mHSpace2 = space2;
    QResizeEvent e(size(), size());
    resizeEvent(&e);
    update();
}
void VolumeMeterS1M3::setScaleLineHidden(bool hidden)
{
    mScaleLineHidden = hidden;
    update();
}

