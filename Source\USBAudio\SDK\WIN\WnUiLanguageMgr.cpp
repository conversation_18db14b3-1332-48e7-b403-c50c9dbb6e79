/************************************************************************
 *  The module manages UI languages.
 *  
 *  Thesycon GmbH, Germany      
 *  http://www.thesycon.de
 *
 ************************************************************************/

#include "libwn_min_global.h"

 // Module is empty if .h file was not included (category turned off).
#ifdef __WnUiLanguageMgr_h__

 // optionally put everything into a namespace
#ifdef LIBWN_NAMESPACE
namespace LIBWN_NAMESPACE {
#endif

WnUiLanguageMgr::WnUiLanguageMgr()
{
}

WnUiLanguageMgr::~WnUiLanguageMgr()
{
}


WNERR WnUiLanguageMgr::Initialize( const WString& srcPath )
{
    //error check
    if ((mUiLanguagesSupportedByApp.size() > 0) || (mSelectedUILanguage.IsValid())) {
        WNTRACE(TRCERR, tprint(__FUNCTION__ _T(": Manager is already initialized.\n")));
        return TSTATUS_REJECTED;
    }

    WNERR status = LoadUiLanguagesSupportedByApp(srcPath);
    if (status != TSTATUS_SUCCESS) {
        WNTRACE(TRCERR, tprint(__FUNCTION__ _T(": LoadUiLanguagesSupportedByApp failed with 0x%08X.\n"), status));
        return status;
    }

    //select the default language to use
    status = CalcSelectedUiLanguage();
    if (status != TSTATUS_SUCCESS) {
        WNTRACE(TRCERR, tprint(__FUNCTION__ _T(": CalcSelectedUiLanguage failed with 0x%08X.\n"), status));
        return status;
    }

    //OK
    mUiLanguageSrcPath = srcPath;
    return TSTATUS_SUCCESS;
}


WNERR WnUiLanguageMgr::LoadUiLanguagesSupportedByApp( const WString& srcPath )
{
    //initialization
    mUiLanguagesSupportedByApp.clear();    
    WNERR status = TSTATUS_SUCCESS;
    WnWow64FsRedirection fsRedirection;

    //Are we a 32-bit application running an 64-bit Windows?
    bool isWow64 = false;
    status = WnIsWow64Process(isWow64);
    if (status != TSTATUS_SUCCESS) {        
        WNTRACE(TRCERR, tprint(__FUNCTION__ _T(": WnIsWow64Process failed with 0x%08X.\n"), status));
        return status;
    }

    //disable file system redirection, if required
    if (isWow64) {
        status = fsRedirection.DisableWow64FsRedirection();
        if (status != TSTATUS_SUCCESS) {
            WNTRACE(TRCERR, tprint(__FUNCTION__ _T(": DisableWow64FsRedirection failed with 0x%08X.\n"), status));
            return status;
        }
    }    

    //create a search patter for the language files in the given folder
    WString searchPattern = srcPath;
    searchPattern += L"\\*";
    searchPattern += mUiLanguageTextFileExtension;

    //find the first subdirectory in the given folder
    WIN32_FIND_DATA foundData;
    TbZeroMemory(&foundData, sizeof(WIN32_FIND_DATA));
    HANDLE searchHandle = ::FindFirstFile(searchPattern.c_str(), &foundData);
    if (searchHandle == INVALID_HANDLE_VALUE) {
        status = GetLastError();
        if ((status == ERROR_FILE_NOT_FOUND) || (status == ERROR_PATH_NOT_FOUND)) {
            WNTRACE(TRCEXTINF, tprint(__FUNCTION__ _T(": No supported UI languages found in '%s'.\n"), srcPath.c_str()));
            status = TSTATUS_SUCCESS;
        }
        else {
            WNTRACE(TRCERR, tprint(__FUNCTION__ _T(": FindFirstFile failed with 0x%08X.\n"), status));            
        }
        //cleanup
        if (isWow64) {
            fsRedirection.RevertWow64FsRedirection();
        }
        return status;
    }      

    WString fileName;
    size_t extStartPos = WString::npos;
    do {        
        //examine the subdirectory name and create the corresponding UI language instance from it, if appropriate
        fileName = foundData.cFileName;
        if ((foundData.dwFileAttributes & FILE_ATTRIBUTE_DIRECTORY) == 0) {

            //Get the start index of the expected file extension.
            //If the file doesn't contain the expected extension it is ignored.
            extStartPos = fileName.rfind(mUiLanguageTextFileExtension);
            if ((extStartPos != WString::npos) && (extStartPos > 0)) {
                
                WnUiLanguage lang;
                status = lang.SetIdentifierStr(fileName.substr(0, extStartPos));
                if (status != TSTATUS_SUCCESS) {
                    WNTRACE(TRCERR, tprint(__FUNCTION__ _T(": SetIdentifierStr failed with 0x%08X. The file '%s' is ignored.\n"), status, fileName.c_str()));
                    //ignore it and try the next                
                }
                else {
                    //add the language to the corresponding list
                    mUiLanguagesSupportedByApp.push_back(lang);
                }
            }            
        }

        //find the next subdirectory in the given folder
        TbZeroMemory(&foundData, sizeof(WIN32_FIND_DATA));
        if (!::FindNextFile(searchHandle, &foundData)) {
            status = GetLastError();
            if (status == ERROR_NO_MORE_FILES) {                
                break;
            }
            else {
                WNTRACE(TRCERR, tprint(__FUNCTION__ _T(": FindNextFile failed with 0x%08X.\n"), status));
                //cleanup
                if (isWow64) {
                    fsRedirection.RevertWow64FsRedirection();
                }
                FindClose(searchHandle);
                return status;
            }            
        }       

    } while (true);


    if (!FindClose(searchHandle)) {
        WNTRACE(TRCERR, tprint(__FUNCTION__ _T(": FindClose failed with 0x%08X.\n"), GetLastError()));
        //not critical, it's only cleanup
    }

    //enable file system redirection again, if required
    //disable file system redirection, if required
    if (isWow64) {
        status = fsRedirection.RevertWow64FsRedirection();
        if (status != TSTATUS_SUCCESS) {
            WNTRACE(TRCERR, tprint(__FUNCTION__ _T(": RevertWow64FsRedirection failed with 0x%08X.\n"), status));
            //not critical, it's only cleanup
        }
    }

    //OK
    return TSTATUS_SUCCESS;
}


WNERR WnUiLanguageMgr::CalcSelectedUiLanguage()
{    
    //initialization
    mSelectedUILanguage.Clear();
    WNERR status = TSTATUS_SUCCESS;    

    //Something to do?
    if (mUiLanguagesSupportedByApp.size() == 0) {
        WNTRACE(TRCEXTINF, tprint(__FUNCTION__ _T(": No supported UI languages.\n")));
        return TSTATUS_SUCCESS;
    }

    //get the local identifier string by means of the current users UI language
    //NOTE: GetUserDefaultLocaleName does not return the expected result. E.g., if the display language is 
    //English (United States) but all other language settings of the user are German (Germany) the method
    //GetUserDefaultLocaleName() returns de-DE instead of en-US.   
    wchar_t buffer[LOCALE_NAME_MAX_LENGTH] = { 0 };
    if (::LCIDToLocaleName(MAKELCID(GetUserDefaultUILanguage(), SORT_DEFAULT), buffer, LOCALE_NAME_MAX_LENGTH, 0) == 0) {
        status = GetLastError();
        WNTRACE(TRCERR, tprint(__FUNCTION__ _T(": LCIDToLocaleName failed with 0x%08X.\n"), status));
        return status;
    }   
    WNTRACE(TRCEXTINF, tprint(__FUNCTION__ _T(": Default UI language of the user is '%s'.\n"), buffer));

    //create an UI language instance from the local name
    WnUiLanguage userUiLang;
    status = userUiLang.SetIdentifierStr(buffer);
    if (status != TSTATUS_SUCCESS) {
        WNTRACE(TRCERR, tprint(__FUNCTION__ _T(": SetIdentifierStr for current user local failed with 0x%08X.\n"), status));
        return status;
    }

    //try to find a supported UI language that matches exactly
    if ((userUiLang.GetScriptDesignator().length() > 0) && (userUiLang.GetRegionDesignator().length() > 0)) {
        for (const auto& uiLang : mUiLanguagesSupportedByApp) {
            if ((uiLang.GetLanguageDesignator() == userUiLang.GetLanguageDesignator())
                && (uiLang.GetScriptDesignator() == userUiLang.GetScriptDesignator())
                && (uiLang.GetRegionDesignator() == userUiLang.GetRegionDesignator())) {
                //We select the first found language in the list.
                mSelectedUILanguage = uiLang;
                break;
            }
        }
    }
    
    //if no UI language matched until now, try to find a supported UI language where at least the language and the script matches 
    if (!mSelectedUILanguage.IsValid()) {
        if (userUiLang.GetScriptDesignator().length() > 0) {
            for (const auto& uiLang : mUiLanguagesSupportedByApp) {
                if ((uiLang.GetLanguageDesignator() == userUiLang.GetLanguageDesignator())
                    && (uiLang.GetScriptDesignator() == userUiLang.GetScriptDesignator())) {
                    //We select the first found language in the list.
                    mSelectedUILanguage = uiLang;
                    break;
                }
            }
        }
    }

    //if no UI language matched until now, try to find a supported UI language where at least the language and the region matches 
    if (!mSelectedUILanguage.IsValid()) {
        if (userUiLang.GetRegionDesignator().length() > 0) {
            for (const auto& uiLang : mUiLanguagesSupportedByApp) {
                if ((uiLang.GetLanguageDesignator() == userUiLang.GetLanguageDesignator())
                    && (uiLang.GetRegionDesignator() == userUiLang.GetRegionDesignator())) {
                    //We select the first found language in the list.
                    mSelectedUILanguage = uiLang;
                    break;
                }
            }
        }
    }

    //if no UI language matched until now, try to find a supported UI language where at least the language matches 
    if (!mSelectedUILanguage.IsValid()) {        
        for (const auto& uilang : mUiLanguagesSupportedByApp) {
            if (uilang.GetLanguageDesignator() == userUiLang.GetLanguageDesignator()) {
                //We select the first found language in the list.
                mSelectedUILanguage = uilang;
                break;
            }
        }        
    }

    //if no UI language matched until now, try to find a supported UI language for common English, i.e. without specified region and script 
    if (!mSelectedUILanguage.IsValid()) {
        for (const auto& uilang : mUiLanguagesSupportedByApp) {
            if ((uilang.GetLanguageDesignator() == L"en") && (uilang.GetRegionDesignator().length() == 0) && (uilang.GetScriptDesignator().length() == 0))  {                
                mSelectedUILanguage = uilang;
                break;
            }
        }
    }

    //if no UI language matched until now, try to find any other supported UI language for English
    if (!mSelectedUILanguage.IsValid()) {
        for (const auto& uilang : mUiLanguagesSupportedByApp) {
            if (uilang.GetLanguageDesignator() == L"en") {
                //We select the first found language in the list.
                mSelectedUILanguage = uilang;
                break;
            }
        }
    }

    //no supported language found
    if (mSelectedUILanguage.IsValid()) {
        WNTRACE(TRCEXTINF, tprint(__FUNCTION__ _T(": Language '%s' selected.\n"), mSelectedUILanguage.GetIdentifierStr().c_str()));
    } else {
        WNTRACE(TRCERR, tprint(__FUNCTION__ _T(": No supported language found for the current user's display language '%s'.\n"), userUiLang.GetIdentifierStr().c_str()));        
    }

    //OK
    return TSTATUS_SUCCESS;
}


WNERR WnUiLanguageMgr::SelectedUiLanguage(const WString& idStr)
{
    for (const auto& uiLang : mUiLanguagesSupportedByApp) {
        if (uiLang.GetIdentifierStr() == idStr) {            
            mSelectedUILanguage = uiLang;
            return TSTATUS_SUCCESS;
        }
    }
    WNTRACE(TRCERR, tprint(__FUNCTION__ _T(": No supported UI language with identifier '%s' found.\n"), idStr.c_str()));
    return TSTATUS_FAILED;
}


UiLanguageTextHandle WnUiLanguageMgr::FindUILanguageText(const wchar_t* idStr) const
{
    for (const auto& group : mUiLanguageTextGroups) {
        UiLanguageTextHandle uiText = group.FindUILanguageText(idStr);
        if (uiText) {
            return uiText;
        }
    }
    return nullptr;
}


const WnUiLanguageTextGroup* WnUiLanguageMgr::FindUILanguageTextGroup(const wchar_t* idStr) const
{       
    for (const auto& group : mUiLanguageTextGroups) {
        if (TbStringEqual(group.GetIdentifierStr(), idStr)) {
            return &group;
        }
    }
    return nullptr;
}


WString WnUiLanguageMgr::GetUiLanguageText(const UiLanguageTextHandle& handle) const
{
    return (handle ? handle->GetLanguageTextToDisplay() : L"");
}


WString WnUiLanguageMgr::GetFileForSelectedUiLanguageTexts() const
{    
    WString filePathAndName(mUiLanguageSrcPath);
    filePathAndName += L"\\";
    filePathAndName += mSelectedUILanguage.GetIdentifierStr();
    filePathAndName += mUiLanguageTextFileExtension;
    return filePathAndName;
}


WNERR WnUiLanguageMgr::LoadTextsForSelectedUiLanguage()
{
    //error check
    if (!mSelectedUILanguage.IsValid()) {
        WNTRACE(TRCERR, tprint(__FUNCTION__ _T(": No supported UI language selected.\n")));
        return TSTATUS_REJECTED;
    }
    if (mUiLanguageTextGroups.size() == 0) {
        WNTRACE(TRCERR, tprint(__FUNCTION__ _T(": No UI language texts defined to load.\n")));
        return TSTATUS_REJECTED;
    }

    //initialization    
    WNERR status = TSTATUS_SUCCESS;    

    //read the texts from the file containing the texts for the selected UI language
    WnUiLanguageFile file;
    file.SetFilePathAndName(GetFileForSelectedUiLanguageTexts());
    status = file.Read(mUiLanguageTextGroups);
    if (status != TSTATUS_SUCCESS) {
        WNTRACE(TRCERR, tprint(__FUNCTION__ _T(": Read failed with 0x%08X.\n"), status));
        return status;
    }

    //OK
    return TSTATUS_SUCCESS;
}


void WnUiLanguageMgr::GetMissingTextsOfSelectedUiLanguage( WnUiLanguageTextGroupVector& missingTexts ) const
{    
    for (const auto& orgGroup : mUiLanguageTextGroups) {

        //copy the text container
        WnUiLanguageTextGroup group(orgGroup.GetIdentifierStr(), orgGroup.IsOptional());

        for (auto uiText : orgGroup.GetUiLanguageTexts()) {
            if (!uiText->IsLanguageTextValid()) {
                group.AddUiLanguageText(*uiText);
            }
        }

        if (group.GetUiLanguageTextCount() > 0) {
            missingTexts.push_back(group);
        }
    }
}


WNERR WnUiLanguageMgr::WriteDefaultUiLanguageFile( WString filePathAndName )
{
    //error check    
    if (mUiLanguageTextGroups.size() == 0) {
        WNTRACE(TRCERR, tprint(__FUNCTION__ _T(": No UI language texts defined to write.\n")));
        return TSTATUS_REJECTED;
    }

    //initialization    
    WNERR status = TSTATUS_SUCCESS;
   
    //write the texts to the file
    WnUiLanguageFile file;
    file.SetFilePathAndName(filePathAndName);
    status = file.Write(mUiLanguageTextGroups, /*onlyDefault*/true);
    if (status != TSTATUS_SUCCESS) {
        WNTRACE(TRCERR, tprint(__FUNCTION__ _T(": Write failed with 0x%08X.\n"), status));
        return status;
    }

    //OK
    return TSTATUS_SUCCESS;
}


#ifdef LIBWN_NAMESPACE
}
#endif

#endif // __WnUiLanguageMgr_h__

/*************************** EOF **************************************/
