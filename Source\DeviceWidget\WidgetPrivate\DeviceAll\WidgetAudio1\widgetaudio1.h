#ifndef WidgetAudio1_H
#define WidgetAudio1_H

#include <QWidget>
#include "appsettings.h"

namespace Ui {
class WidgetAudio1;
}

class WidgetAudio1 : public QWidget, public AppSettingsObserver
{
    Q_OBJECT

public:
    explicit WidgetAudio1(QWidget *parent = nullptr, const QString &name = "");
    ~WidgetAudio1();
    void setName(const QString &name);
    void setFont(const QFont& font);
    void changeLanguage(QString language);
    void modifySampleRateList(QVector<unsigned int> list, unsigned int defaultItem);
    void modifyBufferSizeList(QVector<unsigned int> list, unsigned int defaultItem);
    void setSafeMode(bool enabled);

    int getSampleRate() const;
    int getBufferSize() const;
    bool getSafeMode() const;
    int itemCount()const;

protected:
    void resizeEvent(QResizeEvent *event) override;
    void AppSettingsChanged(QString objectName, QString attribute, QString value) override;

signals:
    void attributeChanged(const QString &objectName, const QString &attribute, const QString &value);

private:
    Ui::WidgetAudio1 *ui;
    QFont m_font;
};

#endif // WidgetAudio1_H
