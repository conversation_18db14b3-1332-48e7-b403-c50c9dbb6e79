#ifndef OriginS1M3_H
#define OriginS1M3_H

#include "originbase.h"
#include "workspace.h"
#include "appsettings.h"

class QToolButton;
class MenuS1M1;
class OriginS1M3 : public OriginBase, public WorkspaceObserver, public AppSettingsObserver
{
    Q_OBJECT
public:
    explicit OriginS1M3(QWidget *parent = nullptr, const QString& name={});
    OriginS1M3& setName(const QString& name);
    OriginS1M3& setFont(const QFont &font);
    OriginS1M3& setValueDuckingSwitch(bool state);

protected:
    void loadSettings() override;
    void AppSettingsChanged(QString objectName, QString attribute, QString value) override;
    void updateAttribute() override;
    void resizeEvent(QResizeEvent *event) override;
    void paintEvent(QPaintEvent *event) override;
    void saveAttribute(QString value) ;

private:
    void in_mWidgetListAll_attributeChanged(QString objectName, QString attribute, QString value);
    void initSigConnect();
    void reset();

private:
    QWidget* mWidget;
    MenuS1M1* mMenuDuck;
    MenuS1M1* mMenuConfig;
    QToolButton* mButtonRouteSpecification;
    int mOFF;
};

#endif // OriginS1M3_H
