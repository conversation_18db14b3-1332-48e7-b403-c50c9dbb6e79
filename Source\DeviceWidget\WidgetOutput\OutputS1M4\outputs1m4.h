#ifndef OUTPUTS1M4_H
#define OUTPUTS1M4_H


#include <QFont>
#include <QEvent>
#include <QTimer>
#include <QObject>
#include <QWidget>
#include <QResizeEvent>

#include "workspace.h"
#include "outputbase.h"
#include "appsettings.h"
#include "equalizerbase.h"


namespace Ui {
class OutputS1M4;
}


class OutputS1M4 : public OutputBase, public WorkspaceObserver, public AppSettingsObserver
{
    Q_OBJECT
public:
    explicit OutputS1M4(QWidget* parent=nullptr, QString name="");
    ~OutputS1M4();
    OutputS1M4& setName(QString name);
    OutputS1M4& setFont(QFont font);
    OutputS1M4& setVolumeMeterLeft(int value);
    OutputS1M4& setVolumeMeterLeftOrigin(int value);
    OutputS1M4& setVolumeMeterLeftGained(int value);
    OutputS1M4& setVolumeMeterLeftClear();
    OutputS1M4& setVolumeMeterLeftSlip();
    OutputS1M4& setVolumeMeterRight(int value);
    OutputS1M4& setVolumeMeterRightOrigin(int value);
    OutputS1M4& setVolumeMeterRightGained(int value);
    OutputS1M4& setVolumeMeterRightClear();
    OutputS1M4& setVolumeMeterRightSlip();
    OutputS1M4& setGain(float value);
    OutputS1M4& setGainLock(bool state=true);
    OutputS1M4& setMuteAffectGain(bool state=true);
    OutputS1M4& setGainAffectMute(bool state=true);
    OutputS1M4& setGainRange(int valueStart, int valueEnd05, int valueEnd10, int valueEnd20);
    OutputS1M4& setGainDefault(float value);
    OutputS1M4& setGainWidgetDisable(float value);
    OutputS1M4& setAudioSourceDefault(QString audioSourceDefault);
    OutputS1M4& setAudioSourceColor(QColor color);
    OutputS1M4& setChannelNameEditable(bool state=true);
    OutputS1M4& setValueEQ(bool state=true);
    OutputS1M4& setValueGAIN(float value);
    OutputS1M4& setValueMUTE(bool state=true);
    OutputS1M4& setOverlay(bool state=true);
    OutputS1M4& setEqualizer(EqualizerBase* equalizer);
    OutputS1M4& addAudioSource(QString audioClass, QVector<QString>& audioSourceList);
    QString getAudioSource();
protected:
    bool eventFilter(QObject* obj, QEvent* e) override;
    void resizeEvent(QResizeEvent* e) override;
    void updateAttribute() override;
    void loadSettings() override;
    void AppSettingsChanged(QString objectName, QString attribute, QString value) override;
private:
    Ui::OutputS1M4* ui;
    QTimer mTimer;
    QFont mFont;
    QString mAudioSourceDefault="";
    QString mPreAudioSource="";
    int mPreMUTE=-2147483648;
    float mPreGAIN=-2147483648;
    float mDisableGAIN=-88;
    bool mMuteAffectGain=false;
    bool mGainAffectMute=false;
    EqualizerBase* mEqualizer=nullptr;
    void save(QAnyStringView key, const QVariant& value);
private slots:
    void in_mTimer_timeout();
    void in_widgetLinkedVSlider_valueChanged(float value);
    void in_widgetToolButton_actionChanged(QString actionName);
    void in_widgetPushButtonGroup1_stateChanged(QString button, QString state);
    void in_widgetPushButtonGroup2_stateChanged(QString button, QString state);
    void on_lineEdit_textChanged(const QString& arg1);
    void on_lineEdit_editingFinished();
    void on_pushButtonClose_clicked();
};


#endif // OUTPUTS1M4_H

