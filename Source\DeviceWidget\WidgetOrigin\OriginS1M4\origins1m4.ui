<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>OriginS1M4</class>
 <widget class="QWidget" name="OriginS1M4">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>168</width>
    <height>270</height>
   </rect>
  </property>
  <property name="sizePolicy">
   <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
    <horstretch>0</horstretch>
    <verstretch>0</verstretch>
   </sizepolicy>
  </property>
  <property name="minimumSize">
   <size>
    <width>80</width>
    <height>220</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QGridLayout" name="gridLayout">
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <property name="spacing">
    <number>0</number>
   </property>
   <item row="0" column="0">
    <widget class="QFrame" name="frame">
     <property name="styleSheet">
      <string notr="true"/>
     </property>
     <property name="frameShape">
      <enum>QFrame::Shape::StyledPanel</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Shadow::Raised</enum>
     </property>
     <widget class="QLabel" name="labelNC">
      <property name="geometry">
       <rect>
        <x>80</x>
        <y>10</y>
        <width>18</width>
        <height>16</height>
       </rect>
      </property>
      <property name="text">
       <string>NC</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignmentFlag::AlignCenter</set>
      </property>
     </widget>
     <widget class="QWidget" name="widget" native="true">
      <property name="geometry">
       <rect>
        <x>10</x>
        <y>49</y>
        <width>141</width>
        <height>201</height>
       </rect>
      </property>
      <widget class="DialS1M1" name="DialReverb" native="true">
       <property name="geometry">
        <rect>
         <x>50</x>
         <y>120</y>
         <width>41</width>
         <height>16</height>
        </rect>
       </property>
       <property name="sizePolicy">
        <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="minimumSize">
        <size>
         <width>0</width>
         <height>0</height>
        </size>
       </property>
      </widget>
      <widget class="QLabel" name="labelReverb">
       <property name="geometry">
        <rect>
         <x>40</x>
         <y>90</y>
         <width>41</width>
         <height>16</height>
        </rect>
       </property>
       <property name="text">
        <string>Reverb</string>
       </property>
       <property name="alignment">
        <set>Qt::AlignmentFlag::AlignCenter</set>
       </property>
      </widget>
      <widget class="QLabel" name="labelDialLeft">
       <property name="geometry">
        <rect>
         <x>10</x>
         <y>130</y>
         <width>20</width>
         <height>16</height>
        </rect>
       </property>
       <property name="text">
        <string>Dry</string>
       </property>
       <property name="alignment">
        <set>Qt::AlignmentFlag::AlignRight|Qt::AlignmentFlag::AlignTrailing|Qt::AlignmentFlag::AlignVCenter</set>
       </property>
      </widget>
      <widget class="QLabel" name="labelDialRight">
       <property name="geometry">
        <rect>
         <x>100</x>
         <y>140</y>
         <width>23</width>
         <height>16</height>
        </rect>
       </property>
       <property name="text">
        <string>Wet</string>
       </property>
       <property name="alignment">
        <set>Qt::AlignmentFlag::AlignLeading|Qt::AlignmentFlag::AlignLeft|Qt::AlignmentFlag::AlignVCenter</set>
       </property>
      </widget>
      <widget class="QPushButton" name="buttonNCOFF">
       <property name="geometry">
        <rect>
         <x>40</x>
         <y>10</y>
         <width>75</width>
         <height>23</height>
        </rect>
       </property>
       <property name="text">
        <string>OFF</string>
       </property>
       <property name="checkable">
        <bool>true</bool>
       </property>
      </widget>
      <widget class="QPushButton" name="buttonNC1">
       <property name="geometry">
        <rect>
         <x>30</x>
         <y>30</y>
         <width>75</width>
         <height>23</height>
        </rect>
       </property>
       <property name="text">
        <string>NC1</string>
       </property>
       <property name="checkable">
        <bool>true</bool>
       </property>
      </widget>
      <widget class="QPushButton" name="buttonNC2">
       <property name="geometry">
        <rect>
         <x>30</x>
         <y>50</y>
         <width>75</width>
         <height>23</height>
        </rect>
       </property>
       <property name="text">
        <string>NC2</string>
       </property>
       <property name="checkable">
        <bool>true</bool>
       </property>
      </widget>
      <widget class="QPushButton" name="buttonReverbOFF">
       <property name="geometry">
        <rect>
         <x>10</x>
         <y>160</y>
         <width>41</width>
         <height>16</height>
        </rect>
       </property>
       <property name="text">
        <string>OFF</string>
       </property>
       <property name="checkable">
        <bool>true</bool>
       </property>
      </widget>
      <widget class="QPushButton" name="buttonLive">
       <property name="geometry">
        <rect>
         <x>70</x>
         <y>160</y>
         <width>41</width>
         <height>16</height>
        </rect>
       </property>
       <property name="text">
        <string>LIVE</string>
       </property>
       <property name="checkable">
        <bool>true</bool>
       </property>
      </widget>
      <widget class="QPushButton" name="buttonHALL">
       <property name="geometry">
        <rect>
         <x>70</x>
         <y>180</y>
         <width>41</width>
         <height>16</height>
        </rect>
       </property>
       <property name="text">
        <string>HALL</string>
       </property>
       <property name="checkable">
        <bool>true</bool>
       </property>
      </widget>
      <widget class="QPushButton" name="buttonSTU">
       <property name="geometry">
        <rect>
         <x>10</x>
         <y>180</y>
         <width>41</width>
         <height>16</height>
        </rect>
       </property>
       <property name="text">
        <string>STU</string>
       </property>
       <property name="checkable">
        <bool>true</bool>
       </property>
      </widget>
      <widget class="QPushButton" name="buttonNC3">
       <property name="geometry">
        <rect>
         <x>30</x>
         <y>70</y>
         <width>75</width>
         <height>23</height>
        </rect>
       </property>
       <property name="text">
        <string>NC3</string>
       </property>
       <property name="checkable">
        <bool>true</bool>
       </property>
      </widget>
     </widget>
    </widget>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>DialS1M1</class>
   <extends>QWidget</extends>
   <header location="global">dials1m1.h</header>
   <container>1</container>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
