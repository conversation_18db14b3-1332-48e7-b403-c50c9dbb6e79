#include "globalfont.h"
#include "pushbuttongroups1m11.h"
#include "ui_pushbuttongroups1m11.h"


PushButtonGroupS1M11::PushButtonGroupS1M11(QWidget *parent)
    : QWidget(parent)
    , ui(new Ui::PushButtonGroupS1M11)
{
    ui->setupUi(this);
    mTimer.setSingleShot(true);
    mTimer.setInterval(200);
    connect(&mTimer, &QTimer::timeout, this, [this](){
        QString value;
        if(mMouseButton == Qt::LeftButton) value = "LeftButtonSingle";
        else if(mMouseButton == Qt::RightButton) value = "RightButtonSingle";
        else if(mMouseButton == Qt::MiddleButton) value = "MiddleButtonSingle";
        if(mButton == ui->PushButtonEQ) emit mouseClickEvent("EQ", value);
        else if(mButton == ui->PushButtonDUCKING) emit mouseClickEvent("DUCKING", value);
        if(mMouseButton == Qt::LeftButton) mButton->click();
    });
    ui->PushButton48V->setCheckable(true);
    ui->PushButtonAUTO->setCheckable(true);
    ui->PushButtonEQ->setCheckable(true);
    ui->PushButtonDUCKING->setCheckable(true);
    ui->PushButton48V->setAttribute(Qt::WA_LayoutUsesWidgetRect);
    ui->PushButtonAUTO->setAttribute(Qt::WA_LayoutUsesWidgetRect);
    ui->PushButtonEQ->setAttribute(Qt::WA_LayoutUsesWidgetRect);
    ui->PushButtonDUCKING->setAttribute(Qt::WA_LayoutUsesWidgetRect);
    ui->PushButtonEQ->installEventFilter(this);
    ui->PushButtonDUCKING->installEventFilter(this);
    mStyle[0] = "QFrame { image: url(:/Image/PushButtonGroup/PBG15_0.png); }";
    mStyle[1] = "QFrame { image: url(:/Image/PushButtonGroup/PBG15_1.png); }";
    mStyle[2] = "QFrame { image: url(:/Image/PushButtonGroup/PBG15_9.png); }";
    mStyle[3] = "QFrame { image: url(:/Image/PushButtonGroup/PBG15_2.png); }";
    mStyle[4] = "QFrame { image: url(:/Image/PushButtonGroup/PBG15_13.png); }";
    mStyle[5] = "QFrame { image: url(:/Image/PushButtonGroup/PBG15_8.png); }";
    mStyle[6] = "QFrame { image: url(:/Image/PushButtonGroup/PBG15_10.png); }";
    mStyle[7] = "QFrame { image: url(:/Image/PushButtonGroup/PBG15_3.png); }";
    mStyle[8] = "QFrame { image: url(:/Image/PushButtonGroup/PBG15_15.png); }";
    mStyle[9] = "QFrame { image: url(:/Image/PushButtonGroup/PBG15_6.png); }";
    mStyle[10] = "QFrame { image: url(:/Image/PushButtonGroup/PBG15_12.png); }";
    mStyle[11] = "QFrame { image: url(:/Image/PushButtonGroup/PBG15_5.png); }";
    mStyle[12] = "QFrame { image: url(:/Image/PushButtonGroup/PBG15_14.png); }";
    mStyle[13] = "QFrame { image: url(:/Image/PushButtonGroup/PBG15_7.png); }";
    mStyle[14] = "QFrame { image: url(:/Image/PushButtonGroup/PBG15_11.png); }";
    mStyle[15] = "QFrame { image: url(:/Image/PushButtonGroup/PBG15_4.png); }";
    setState("48V", "0", false);
    setState("AUTO", "0", false);
    setState("EQ", "0", false);
    setState("DUCKING", "0", false);
    setLanguage("English");
}
PushButtonGroupS1M11::~PushButtonGroupS1M11()
{
    delete ui;
}


// override
bool PushButtonGroupS1M11::eventFilter(QObject* obj, QEvent* e)
{
    if(e->type() == QEvent::MouseButtonRelease)
    {
        QMouseEvent* mouseEvent=static_cast<QMouseEvent*>(e);
        if(mTimer.isActive())
        {
            mTimer.stop();
            if(mMouseButton == mouseEvent->button() && obj == mButton)
            {
                QString value;
                if(mMouseButton == Qt::LeftButton) value = "LeftButtonDouble";
                else if(mMouseButton == Qt::RightButton) value = "RightButtonDouble";
                else if(mMouseButton == Qt::MiddleButton) value = "MiddleButtonDouble";
                if(mButton == ui->PushButtonEQ) emit mouseClickEvent("EQ", value);
                else if(mButton == ui->PushButtonDUCKING) emit mouseClickEvent("DUCKING", value);
            }
        }
        else
        {
            mTimer.start();
            mMouseButton = mouseEvent->button();
            mButton = qobject_cast<QPushButton*>(obj);
        }
        return true;
    }
    return QWidget::eventFilter(obj, e);
}
void PushButtonGroupS1M11::resizeEvent(QResizeEvent* e)
{
    Q_UNUSED(e);
    mFont.setPointSize(GLBFHandle.getSuitablePointSize(mFont, ui->PushButtonDUCKING->text(), ui->PushButtonDUCKING->rect()) - 1);
    ui->PushButton48V->setFont(mFont);
    ui->PushButtonAUTO->setFont(mFont);
    ui->PushButtonEQ->setFont(mFont);
    ui->PushButtonDUCKING->setFont(mFont);
}


// slot
void PushButtonGroupS1M11::on_PushButton48V_clicked(bool checked)
{
    setState("48V", QString::number(checked));
}
void PushButtonGroupS1M11::on_PushButtonAUTO_clicked(bool checked)
{
    setState("AUTO", QString::number(checked));
}
void PushButtonGroupS1M11::on_PushButtonEQ_clicked(bool checked)
{
    setState("EQ", QString::number(checked));
}
void PushButtonGroupS1M11::on_PushButtonDUCKING_clicked(bool checked)
{
    setState("DUCKING", QString::number(checked));
}


// setter & getter
PushButtonGroupS1M11& PushButtonGroupS1M11::setFont(QFont font)
{
    mFont = font;
    QResizeEvent e(size(), size());
    resizeEvent(&e);
    update();
    return *this;
}
PushButtonGroupS1M11& PushButtonGroupS1M11::setLanguage(QString language)
{
    if(language == "English")
    {
        // ui->PushButton48V->setText("48V");
        // ui->PushButtonAUTO->setText("AUTO");
        // ui->PushButtonEQ->setText("EQ");
        // ui->PushButtonDUCKING->setText("DUCKING");
    }
    else if(language == "Chinese")
    {
        // ui->PushButton48V->setText("48V");
        // ui->PushButtonAUTO->setText("AUTO");
        // ui->PushButtonEQ->setText("EQ");
        // ui->PushButtonDUCKING->setText("闪避");
    }
    return *this;
}
PushButtonGroupS1M11& PushButtonGroupS1M11::setState(QString button, QString state, bool needEmit)
{
    unsigned int bitMask=0;
    QPushButton* currentButton=nullptr;
    if(button == "48V")
    {
        bitMask = 0x0001;
        currentButton = ui->PushButton48V;
    }
    else if(button == "AUTO")
    {
        bitMask = 0x0002;
        currentButton = ui->PushButtonAUTO;
    }
    else if(button == "EQ")
    {
        bitMask = 0x0004;
        currentButton = ui->PushButtonEQ;
    }
    else if(button == "DUCKING")
    {
        bitMask = 0x0008;
        currentButton = ui->PushButtonDUCKING;
    }
    if(currentButton != nullptr)
    {
        mBitmap &= ~bitMask;
        if(state.toInt()) mBitmap |= bitMask;
        ui->frame->setStyleSheet(mStyle.value(mBitmap));
        currentButton->setChecked(state.toInt());
        currentButton->setStyleSheet(state.toInt() ? "QPushButton { color: rgb(222, 222, 222); }" : "QPushButton { color: rgb(161, 161, 161); }");
        if(needEmit) emit stateChanged(button, state);
    }
    return *this;
}
QString PushButtonGroupS1M11::getState(QString button)
{
    QPushButton* currentButton=nullptr;
    if(button == "48V")
    {
        currentButton = ui->PushButton48V;
    }
    else if(button == "AUTO")
    {
        currentButton = ui->PushButtonAUTO;
    }
    else if(button == "EQ")
    {
        currentButton = ui->PushButtonEQ;
    }
    else if(button == "DUCKING")
    {
        currentButton = ui->PushButtonDUCKING;
    }
    if(currentButton != nullptr)
    {
        return QString::number(currentButton->isChecked());
    }
    return QString::number(false);
}

