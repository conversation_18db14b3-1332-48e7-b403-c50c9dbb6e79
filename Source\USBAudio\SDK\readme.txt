USBAudio/SDK/WIN移植步骤：
{
    将ToppingProUsbAudio_XXX_SDK移植到qt工程的步骤:
    注:[XXX]为版本和日期

    1:创建qt工程，在工程同级目录中创建目录USBAudio/SDK/WIN，以保存SDK头文件和源文件以及库文件(.lib)

    2:解压ToppingProUsbAudio_XXX_SDK，进入ToppingProUsbAudio_XXX_SDK一级目录

    3:进入目录lib，根据需要选择不同版本和不同平台的库文件(.lib)，将其拷贝到qt工程目录USBAudio/SDK/WIN中

    4:退出目录lib，进入目录source

    5:将libwn_min目录中的所有文件拷贝到qt工程目录USBAudio/SDK/WIN中，和VS相关的文件可以不拷贝(只拷贝.cpp和.h文件即可)

    6:将tusbaudioapi_inc目录中的所有文件拷贝到qt工程目录USBAudio/SDK/WIN中，和VS相关的文件可以不拷贝(只拷贝.cpp和.h文件即可)

    7:关闭ToppingProUsbAudio_XXX_SDK目录，进入qt工程目录USBAudio/SDK/WIN中，移除_libwn_min_compile_all.cpp文件(不移除编译报错)，更改TUsbAudioApiDll.h和TUsbAudioApiDll.cpp文件的编码为UTF-8(不更改编译警告)

    8:使用qtcreator打开步骤1创建的qt工程，然后将目录USBAudio/SDK/WIN添加到qt工程中
    注:qmake和cmake添加的方法不一样

    9:编译运行
    注:使用此SDK，qt工程构建套件需要选择MSVC，平台需要根据步骤3中的库文件是32位还是64位进行选择


    [ 注意 ] 如需添加混音相关API，在步骤6后继续如下步骤，然后继续步骤7

    add-1：将tusbaudiodsp_inc目录中的所有文件拷贝到qt工程目录USBAudio/SDK/WIN中，和VS相关的文件可以不拷贝(只拷贝.cpp和.h文件即可)

    add-2：将tusbaudiodsp_mixer_api目录中的所有文件拷贝到qt工程目录USBAudio/SDK/WIN中，和VS相关的文件可以不拷贝(只拷贝.cpp和.h文件即可)
}
USBAudio/SDK/MAC移植步骤：
{

}