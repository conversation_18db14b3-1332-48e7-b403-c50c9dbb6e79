#ifndef PUSHBUTTONS1M12_H
#define PUSHBUTTONS1M12_H


#include <QFont>
#include <QWidget>
#include <QPushButton>
#include <QResizeEvent>


class PushButtonS1M12 : public QWidget
{
    Q_OBJECT
public:
    explicit PushButtonS1M12(QWidget* parent=nullptr);
    ~PushButtonS1M12();
    enum ButtonID
    {
        button48V=0,
        buttonAUTO
    };
    PushButtonS1M12& setFont(QFont font);
    PushButtonS1M12& setPushButtonState48V(bool state);
    PushButtonS1M12& setPushButtonStateAUTO(bool state);
    bool getPushButtonState48V();
    bool getPushButtonStateAUTO();
protected:
    void resizeEvent(QResizeEvent* e) override;
private:
    QFont mFont;
    bool mPushButtonState48V=false;
    bool mPushButtonStateAUTO=false;
    QPushButton mPushButton48V;
    QPushButton mPushButtonAUTO;
    int mRadius=0;
private slots:
    void in_mPushButton48V_clicked();
    void in_mPushButtonAUTO_clicked();
signals:
    void buttonStateChanged(ButtonID button, bool state);
};


#endif // PUSHBUTTONS1M12_H

