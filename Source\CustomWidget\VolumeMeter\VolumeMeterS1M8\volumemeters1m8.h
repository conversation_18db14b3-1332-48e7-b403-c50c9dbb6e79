#ifndef VOLUMEMETERS1M8_H
#define VOLUMEMETERS1M8_H


#include <QFont>
#include <QRect>
#include <QTimer>
#include <QWidget>
#include <QPainter>
#include <QMouseEvent>
#include <QPaintEvent>
#include <QResizeEvent>


class VolumeMeterS1M8 : public QWidget
{
    Q_OBJECT
public:
    explicit VolumeMeterS1M8(QWidget* parent=nullptr);
    ~VolumeMeterS1M8();
    void setFont(QFont font);
    void setColorBG(QColor color);
    void setValueLeft(int value);
    void setValueRight(int value);
    void setMeterLeftClear();
    void setMeterLeftSlip();
    void setMeterRightClear();
    void setMeterRightSlip();
    void setWidthRatio(int space1, int meter, int scale);
    void setHeightRatio(int clip, int space1, int volume, int space2);
    void setScaleLineHidden(bool hidden=true);
protected:
    void resizeEvent(QResizeEvent* e) override;
    void paintEvent(QPaintEvent* e) override;
    void mouseDoubleClickEvent(QMouseEvent* e) override;
private:
    struct RectMeter
    {
        int volumeValue=-60;
        bool clipStatus=false;
        QRect clip;
        QRect volume;
        QTimer timerClip;
    };
    QTimer mTimerMeterLeft;
    QTimer mTimerMeterRight;
    RectMeter mRectMeterLeft;
    RectMeter mRectMeterRight;
    QRect mRectScale;
    QFont mFont;
    QColor mColorBG=QColor(22, 22, 22);
    int mSpace1=15;
    int mMeter=20;
    int mScale=30;
    int mHClip=3;
    int mHSpace1=2;
    int mHVolume=93;
    int mHSpace2=2;
    bool mScaleLineHidden=false;
    void drawBG(QPainter* painter);
    void drawElement(QPainter* painter);
private slots:
    void in_timerClipLeft_timeout();
    void in_timerClipRight_timeout();
    void in_mTimerMeterLeft_timeout();
    void in_mTimerMeterRight_timeout();
};


#endif // VOLUMEMETERS1M8_H

