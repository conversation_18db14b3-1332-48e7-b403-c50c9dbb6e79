#ifndef EQWIDGET_H
#define EQWIDGET_H

#include <QWidget>
#include "eqwidgetitemdata.h"

class QScrollArea;
class QLabel;
class QPushButton;
class EqWidgetIem;
class EqualizerControllerS1M1 : public QWidget
{
    Q_OBJECT

public:
    explicit EqualizerControllerS1M1(QWidget* parent = nullptr);
    ~EqualizerControllerS1M1();
    EqualizerControllerS1M1& setName(QString name);
    EqualizerControllerS1M1& setFont(QFont font);
    void setSizeFactor(double sizeFactor);
    void setItemMinimumWidth(int width);
    void setLanguage(QString language);

    void setItemCount(int count);
    int getItemCount() const { return mItems.size(); }

    void addItem();
    void removeItem(int index = -1);
    void removeAllItems();

    void setEqualizerData(const QString& index, const QString& type, const QString& data);
    void setEqualizerData(const QVector<EqWidgetItemData>& data);
    QVector<EqWidgetItemData> getEqualizerData() const;
    
protected:
    void resizeEvent(QResizeEvent* event) override;
    void paintEvent(QPaintEvent* e) override;
    void adjustFontAndSize();
    void updateItemSize();
    void setItemStretch(double sizeFactor);

private:
    void setupUI();
    void updateItemIndices();

    QFont mFont;
    double mSizeFactor;
    int mMinimumItemWidth; 
    bool mUpdateSize;
    double mLRMarginRatio;
    int mMarginX;

    QScrollArea* mScrollArea;
    QWidget* mScrollWidget;
    QLabel* mTitleTypeLabel;
    QLabel* mTitleGainLabel;
    QLabel* mTitleFrequencyLabel;
    QLabel* mTitleQLabel;

    QVector<EqWidgetIem*> mItems;
    QHash<QObject*,QHash<QString,QString>> mHashLanguages;

signals:
    void itemDataChanged(QString index, const EqWidgetItemData& data);
    void ItemCountChanged(int count);
    void attributeChanged(QString objectName, QString attribute, QString value);
};

#endif // EQWIDGET_H
