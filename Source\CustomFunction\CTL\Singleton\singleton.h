#ifndef SINGLETON_H
#define SINGLETON_H


// Method-1
template <typename T>
class Singleton
{
public:
    static T& instance()
    {
        static T mInstance;
        return mInstance;
    }
    Singleton(const Singleton&) = delete;
    Singleton& operator=(const Singleton&) = delete;
protected:
    Singleton() = default;
    virtual ~Singleton() = default;
};

// Example
// class SubClass : public Singleton<SubClass>
// {
//     friend class Singleton<SubClass>;
// public:
//     void doSomething() { }
// private:
//     SubClass() { }
//     ~SubClass() { }
// };


// Method-2
#define BE_SINGLETON(ClassName)                 \
public:                                              \
    static ClassName& instance()                     \
    {                                                \
        static ClassName mInstance;                  \
        return mInstance;                            \
    }                                                \
private:                                             \
    ClassName(const ClassName&) = delete;            \
    ClassName& operator=(const ClassName&) = delete;

// Example
// class SubClass : public QObject
// {
//     Q_OBJECT
//     BE_SINGLETON(SubClass);
// public:
//     void doSomething() { }
// private:
//     SubClass() { }
//     ~SubClass() { }
// };


#endif // SINGLETON_H

