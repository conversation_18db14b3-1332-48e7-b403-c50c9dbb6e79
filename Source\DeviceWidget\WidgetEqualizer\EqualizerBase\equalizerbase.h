#ifndef EQUALIZERBASE_H
#define EQUALIZERBASE_H


#include "framelesswindow.h"


class EqualizerBase : public FramelessWindow
{
    Q_OBJECT
public:
    explicit EqualizerBase(QWidget* parent=nullptr) : FramelessWindow(parent) { }
    virtual ~EqualizerBase() = default;
    virtual void setStateTarget(bool state) = 0;
    virtual void setStateSource(bool state) = 0;
    virtual void setStateEachFilter(bool state) = 0;
    virtual void setStateCombined(bool state) = 0;
    virtual void setStateFiltered(bool state) = 0;
    virtual void setFilteredType(int type) = 0;
    virtual void setTargetFile(QString path) = 0;
    virtual void setSourceFile(QString path) = 0;
    virtual void setGainInputLeft(float value) = 0;
    virtual void setGainInputRight(float value) = 0;
    virtual void setGainOutputLeft(float value) = 0;
    virtual void setGainOutputRight(float value) = 0;
    virtual void setVolumeMeterInputLeft(int value) = 0;
    virtual void setVolumeMeterInputRight(int value) = 0;
    virtual void setVolumeMeterOutputLeft(int value) = 0;
    virtual void setVolumeMeterOutputRight(int value) = 0;
    virtual void setSwitchStatus(bool is, bool isSendSig = true) = 0;
signals:
    void attributeChanged(QString objectName, QString attribute, QString value);
};


#endif // EQUALIZERBASE_H

