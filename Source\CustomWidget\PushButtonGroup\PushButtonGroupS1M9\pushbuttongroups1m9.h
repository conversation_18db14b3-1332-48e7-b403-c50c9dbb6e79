#ifndef PUSHBUTTONGROUPS1M9_H
#define PUSHBUTTONGROUPS1M9_H


#include <QHash>
#include <QWidget>
#include <QResizeEvent>


namespace Ui {
class PushButtonGroupS1M9;
}


class PushButtonGroupS1M9 : public QWidget
{
    Q_OBJECT
public:
    explicit PushButtonGroupS1M9(QWidget* parent=nullptr);
    ~PushButtonGroupS1M9();
    PushButtonGroupS1M9& setFont(QFont font);
    PushButtonGroupS1M9& setLanguage(QString language);
    PushButtonGroupS1M9& setState(QString button, QString state, bool needEmit=true);
    QString getState(QString button);
protected:
    void resizeEvent(QResizeEvent* e) override;
    void showEvent(QShowEvent*) override { resizeEvent(nullptr); }
private:
    Ui::PushButtonGroupS1M9* ui;
    QFont mFont;
    QString mCurrentItem="";
    QHash<unsigned int, QString> mStyle;
private slots:
    void on_PushButtonMic1_clicked(bool checked);
    void on_PushButtonMic35_clicked(bool checked);
    void on_PushButtonMicHP_clicked(bool checked);
signals:
    void stateChanged(QString button, QString state);
};


#endif // PUSHBUTTONGROUPS1M9_H

