#ifndef USBAUDIOM<PERSON>AGER_H
#define USBAUDIOM<PERSON>AGER_H


#include <QTimer>
#include <QObject>


class USBAudioManager : public QObject
{
    Q_OBJECT
    Q_DISABLE_COPY(USBAudioManager)
public:
    static USBAudioManager& instance()
    {
        static USBAudioManager mInstance;
        return mInstance;
    }
    bool init();
    void start();
    void stop();
private:
    QTimer mTimer;
    int mSampleRate=-1;
    int mBufferSize=-1;
    int mSafeMode=-1;
    USBAudioManager();
    ~USBAudioManager();
private slots:
    void in_mTimer_timeout();
signals:
    void attributeChanged(QString attribute, QString value);
};


#define USAMHandle USBAudioManager::instance()


#endif // USBAUDIOMANAGER_H

