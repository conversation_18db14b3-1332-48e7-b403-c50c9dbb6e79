#ifndef HSliderS2M1_H
#define HSliderS2M1_H


#include <QFont>
#include <QEvent>
#include <QSlider>
#include <QObject>
#include <QWidget>
#include <QLineEdit>
#include <QResizeEvent>


class HSliderS2M1 : public QWidget
{
    Q_OBJECT
public:
    explicit HSliderS2M1(QWidget *parent=nullptr);
    ~HSliderS2M1();
    HSliderS2M1& setFont(QFont font);
    HSliderS2M1& setValue(int value);
    HSliderS2M1& setStep(int value);
    HSliderS2M1& setDefault(int value);
    HSliderS2M1& setRange(int minValue, int maxValue);
    HSliderS2M1& setHeightRatio(int text, int space, int slider);
    int getValue() { return mValue; }
    int getDefault() { return mValueDefault; }
    int getMin() { return mSlider.minimum(); }
    int getMax() { return mSlider.maximum(); }
    HSliderS2M1& showInfinity(bool state=true);
    HSliderS2M1& showInfinitesimal(bool state=true);
protected:
    bool eventFilter(QObject* obj, QEvent* e) override;
    void resizeEvent(QResizeEvent* e) override;
private:
    QLineEdit mLineEdit;
    QSlider mSlider;
    QFont mFont;
    bool mShowInfinity=false;
    bool mShowInfinitesimal=false;
    bool mEmitOpen=false;
    int mValue=0;
    int mValueDefault=0;
    int mHText=8;
    int mHSpace=2;
    int mHSlider=90;
private slots:
    void in_mLineEdit_editingFinished();
    void in_mSlider_valueChanged(int value);
signals:
    void valueChanged(int value);
};


#endif // HSliderS2M1_H

