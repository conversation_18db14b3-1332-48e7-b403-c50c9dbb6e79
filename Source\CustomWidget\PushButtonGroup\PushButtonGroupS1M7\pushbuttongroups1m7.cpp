#include "globalfont.h"
#include "pushbuttongroups1m7.h"
#include "ui_pushbuttongroups1m7.h"


PushButtonGroupS1M7::PushButtonGroupS1M7(QWidget *parent)
    : QWidget(parent)
    , ui(new Ui::PushButtonGroupS1M7)
{
    ui->setupUi(this);
    ui->PushButtonANTI->setCheckable(true);
    ui->PushButtonSOLO->setCheckable(true);
    ui->PushButtonMUTE->setCheckable(true);
    ui->PushButtonANTI->setAttribute(Qt::WA_LayoutUsesWidgetRect);
    ui->PushButtonSOLO->setAttribute(Qt::WA_LayoutUsesWidgetRect);
    ui->PushButtonMUTE->setAttribute(Qt::WA_LayoutUsesWidgetRect);
    mStyle[0] = "QFrame { image: url(:/Image/PushButtonGroup/PBG10_0.png); }";
    mStyle[1] = "QFrame { image: url(:/Image/PushButtonGroup/PBG10_1.png); }";
    mStyle[2] = "QFrame { image: url(:/Image/PushButtonGroup/PBG10_2.png); }";
    mStyle[3] = "QFrame { image: url(:/Image/PushButtonGroup/PBG10_4.png); }";
    mStyle[4] = "QFrame { image: url(:/Image/PushButtonGroup/PBG10_3.png); }";
    mStyle[5] = "QFrame { image: url(:/Image/PushButtonGroup/PBG10_6.png); }";
    mStyle[6] = "QFrame { image: url(:/Image/PushButtonGroup/PBG10_7.png); }";
    mStyle[7] = "QFrame { image: url(:/Image/PushButtonGroup/PBG10_5.png); }";
    setState("ANTI", "0", false);
    setState("SOLO", "0", false);
    setState("MUTE", "0", false);
    setLanguage("English");
}
PushButtonGroupS1M7::~PushButtonGroupS1M7()
{
    delete ui;
}


// override
void PushButtonGroupS1M7::resizeEvent(QResizeEvent* e)
{
    Q_UNUSED(e);
    mFont.setPointSize(GLBFHandle.getSuitablePointSize(mFont, ui->PushButtonMUTE->height()) - 1);
    ui->PushButtonSOLO->setFont(mFont);
    ui->PushButtonMUTE->setFont(mFont);
    mFont.setPointSize(mFont.pointSize() + 2);
    ui->PushButtonANTI->setFont(mFont);
}


// slot
void PushButtonGroupS1M7::on_PushButtonANTI_clicked(bool checked)
{
    setState("ANTI", QString::number(checked));
}
void PushButtonGroupS1M7::on_PushButtonSOLO_clicked(bool checked)
{
    setState("SOLO", QString::number(checked));
}
void PushButtonGroupS1M7::on_PushButtonMUTE_clicked(bool checked)
{
    setState("MUTE", QString::number(checked));
}


// setter & getter
PushButtonGroupS1M7& PushButtonGroupS1M7::setFont(QFont font)
{
    mFont = font;
    QResizeEvent e(size(), size());
    resizeEvent(&e);
    update();
    return *this;
}
PushButtonGroupS1M7& PushButtonGroupS1M7::setLanguage(QString language)
{
    if(language == "English")
    {
        // ui->PushButtonANTI->setText("∅");
        // ui->PushButtonSOLO->setText("SOLO");
        // ui->PushButtonMUTE->setText("MUTE");
    }
    else if(language == "Chinese")
    {
        // ui->PushButtonANTI->setText("∅");
        // ui->PushButtonSOLO->setText("SOLO");
        // ui->PushButtonMUTE->setText("MUTE");
    }
    return *this;
}
PushButtonGroupS1M7& PushButtonGroupS1M7::setState(QString button, QString state, bool needEmit)
{
    unsigned int bitMask=0;
    QPushButton* currentButton=nullptr;
    if(button == "ANTI")
    {
        bitMask = 0x0001;
        currentButton = ui->PushButtonANTI;
    }
    else if(button == "SOLO")
    {
        bitMask = 0x0002;
        currentButton = ui->PushButtonSOLO;
    }
    else if(button == "MUTE")
    {
        bitMask = 0x0004;
        currentButton = ui->PushButtonMUTE;
    }
    if(currentButton != nullptr)
    {
        mBitmap &= ~bitMask;
        if(state.toInt()) mBitmap |= bitMask;
        ui->frame->setStyleSheet(mStyle.value(mBitmap));
        currentButton->setChecked(state.toInt());
        currentButton->setStyleSheet(state.toInt() ? "QPushButton { color: rgb(222, 222, 222); }" : "QPushButton { color: rgb(161, 161, 161); }");
        if(needEmit) emit stateChanged(button, state);
    }
    return *this;
}
QString PushButtonGroupS1M7::getState(QString button)
{
    QPushButton* currentButton=nullptr;
    if(button == "ANTI")
    {
        currentButton = ui->PushButtonANTI;
    }
    else if(button == "SOLO")
    {
        currentButton = ui->PushButtonSOLO;
    }
    else if(button == "MUTE")
    {
        currentButton = ui->PushButtonMUTE;
    }
    if(currentButton != nullptr)
    {
        return QString::number(currentButton->isChecked());
    }
    return QString::number(false);
}

