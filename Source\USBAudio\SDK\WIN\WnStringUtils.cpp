/************************************************************************
 *
 *  Module:       WnStringUtils.cpp
 *
 *  Description:  String Utility Functions
 *
 *  Runtime Env.: Win32
 *
 *  Author(s):
 *    <PERSON><PERSON>,  <EMAIL>
 *
 *  Companies:
 *    Thesycon GmbH, Germany      http://www.thesycon.de
 *
 ************************************************************************/

#include "libwn_min_global.h"

// Module is empty if .h file was not included (category turned off).
#ifdef __WnStringUtils_h__

#if !defined(UNDER_CE)
#include <errno.h>
#endif

#include "WnStringUtils_impl.h"

// optionally put everything into a namespace
#ifdef LIBWN_NAMESPACE
namespace LIBWN_NAMESPACE {
#endif

// char version
WNERR
WnStringPrintf(
    char* buffer,
    unsigned int maxChars,
    const char* format,
    ...
    )
{
    WNASSERT(buffer!=NULL);
    WNASSERT(format!=NULL);

    va_list argptr;
    va_start(argptr, format);
    WNERR err = WnStringVPrintf_impl(buffer, maxChars, format, argptr);
    if ( !SUCC(err) ) {
        switch ( err ) {
        case ERROR_BUFFER_OVERFLOW:
            WNTRACE(TRCWRN,tprint(__FUNCTION__": string truncated to '%s'\n", buffer));
            break;
        case ERROR_INVALID_PARAMETER:
            WNTRACE(TRCERR,tprint(__FUNCTION__": invalid parameter\n"));
            break;
        default:
            WNTRACE(TRCERR,tprint(__FUNCTION__": failed, err=0x%08X\n", err));
            break;
        }
    }
    va_end(argptr);
    return err;
}

// wide char version
WNERR
WnStringPrintf(
    wchar_t* buffer,
    unsigned int maxChars,
    const wchar_t* format,
    ...
    )
{
    WNASSERT(buffer!=NULL);
    WNASSERT(format!=NULL);

    va_list argptr;
    va_start(argptr, format);
    WNERR err = WnStringVPrintf_impl(buffer, maxChars, format, argptr);
    if ( !SUCC(err) ) {
        switch ( err ) {
        case ERROR_BUFFER_OVERFLOW:
            WNTRACE(TRCWRN,tprint(__FUNCTION__": string truncated to '%S'\n", buffer));
            break;
        case ERROR_INVALID_PARAMETER:
            WNTRACE(TRCERR,tprint(__FUNCTION__": invalid parameter\n"));
            break;
        default:
            WNTRACE(TRCERR,tprint(__FUNCTION__": failed, err=0x%08X\n", err));
            break;
        }
    }
    va_end(argptr);
    return err;
}


// char version
WNERR
WnStringPrintf_impl(
    char* buffer,
    unsigned int maxChars,
    const char* format,
    ...
    )
{
    // No calls to the trace module (WNASSERT, WNTRACE) allowed in this function!
    va_list argptr;
    va_start(argptr, format);
    WNERR err = WnStringVPrintf_impl(buffer, maxChars, format, argptr);
    va_end(argptr);
    return err;
}

// wide char version
WNERR
WnStringPrintf_impl(
    wchar_t* buffer,
    unsigned int maxChars,
    const wchar_t* format,
    ...
    )
{
    // No calls to the trace module (WNASSERT, WNTRACE) allowed in this function!
    va_list argptr;
    va_start(argptr, format);
    WNERR err = WnStringVPrintf_impl(buffer, maxChars, format, argptr);
    va_end(argptr);
    return err;
}


// char version
WNERR
WnStringVPrintf(
    char* buffer,
    unsigned int maxChars,
    const char* format,
    va_list argList
    )
{
    WNASSERT(buffer!=NULL);
    WNASSERT(format!=NULL);

    WNERR err = WnStringVPrintf_impl(buffer, maxChars, format, argList);
    if ( !SUCC(err) ) {
        switch ( err ) {
        case ERROR_BUFFER_OVERFLOW:
            WNTRACE(TRCWRN,tprint(__FUNCTION__": string truncated to '%s'\n", buffer));
            break;
        case ERROR_INVALID_PARAMETER:
            WNTRACE(TRCERR,tprint(__FUNCTION__": invalid parameter\n"));
            break;
        default:
            WNTRACE(TRCERR,tprint(__FUNCTION__": failed, err=0x%08X\n", err));
            break;
        }
    }
    return err;
}

// wide char version
WNERR
WnStringVPrintf(
    wchar_t* buffer,
    unsigned int maxChars,
    const wchar_t* format,
    va_list argList
    )
{
    WNASSERT(buffer!=NULL);
    WNASSERT(format!=NULL);

    WNERR err = WnStringVPrintf_impl(buffer, maxChars, format, argList);
    if ( !SUCC(err) ) {
        switch ( err ) {
        case ERROR_BUFFER_OVERFLOW:
            WNTRACE(TRCWRN,tprint(__FUNCTION__": string truncated to '%S'\n", buffer));
            break;
        case ERROR_INVALID_PARAMETER:
            WNTRACE(TRCERR,tprint(__FUNCTION__": invalid parameter\n"));
            break;
        default:
            WNTRACE(TRCERR,tprint(__FUNCTION__": failed, err=0x%08X\n", err));
            break;
        }
    }
    return err;
}


// char version
WNERR
WnStringVPrintf_impl(
    char* buffer,
    unsigned int maxChars,
    const char* format,
    va_list argList
    )
{
    // No calls to the trace module (WNASSERT, WNTRACE) allowed in this function!

    // note: parameters will be checked by _vsnprintf_s

#ifdef UNDER_CE
    if ( 0 == maxChars ) {
        return ERROR_INVALID_PARAMETER;
    }
    int ret = _vsnprintf(buffer, maxChars, format, argList);
    buffer[maxChars-1] = 0;
    if ( ret < 0 ) {
        // buffer overflow, string was truncated
        return ERROR_BUFFER_OVERFLOW;
    }
#else
    int ret = _vsnprintf_s(buffer, maxChars, _TRUNCATE, format, argList);
    if ( ret < 0 ) {
        errno_t err;
        _get_errno( &err );
        if ( 0 == err ) {
            // buffer overflow, string was truncated
            return ERROR_BUFFER_OVERFLOW;
        }
        if ( EINVAL == err ) {
            // invalid pointer or count
            return ERROR_INVALID_PARAMETER;
        }
        // misc error
        return err;
    }
#endif

    return ERROR_SUCCESS;
}

// wide char version
WNERR
WnStringVPrintf_impl(
    wchar_t* buffer,
    unsigned int maxChars,
    const wchar_t* format,
    va_list argList
    )
{
    // No calls to the trace module (WNASSERT, WNTRACE) allowed in this function!

    // note: parameters will be checked by _vsnprintf_s

#ifdef UNDER_CE
    if ( 0 == maxChars ) {
        return ERROR_INVALID_PARAMETER;
    }
    int ret = _vsnwprintf(buffer, maxChars, format, argList);
    buffer[maxChars-1] = 0;
    if ( ret < 0 ) {
        // buffer overflow, string was truncated
        return ERROR_BUFFER_OVERFLOW;
    }
#else
    int ret = _vsnwprintf_s(buffer, maxChars, _TRUNCATE, format, argList);
    if ( ret < 0 ) {
        errno_t err;
        _get_errno( &err );
        if ( 0 == err ) {
            // buffer overflow, string was truncated
            return ERROR_BUFFER_OVERFLOW;
        }
        if ( EINVAL == err ) {
            // invalid pointer or count
            return ERROR_INVALID_PARAMETER;
        }
        // misc error
        return err;
    }
#endif

    return ERROR_SUCCESS;
}

#ifdef LIBWN_NAMESPACE
}
#endif

#endif

/********************************* EOF *********************************/
