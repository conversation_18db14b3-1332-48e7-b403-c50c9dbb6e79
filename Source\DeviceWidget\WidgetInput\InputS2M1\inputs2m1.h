#ifndef InputS2M1_H
#define InputS2M1_H


#include <QFont>
#include <QEvent>
#include <QTimer>
#include <QObject>
#include <QWidget>
#include <QResizeEvent>
#include <QButtonGroup>

#include "originbase.h"
#include "workspace.h"
#include "appsettings.h"
#include "pushbuttons1m2.h"


namespace Ui {
class InputS2M1;
}


class InputS2M1 : public OriginBase, public WorkspaceObserver, public AppSettingsObserver
{
    Q_OBJECT
public:
    explicit InputS2M1(QWidget* parent=nullptr, QString name="");
    ~InputS2M1();
    InputS2M1& setName(QString name);
    InputS2M1& setFont(QFont font);
    InputS2M1& setHideMic();
    void setVolumeMeter(int value);
    void setMute(bool state);
    void set48V(bool state);
    void setGain(float value);
    void setMicType(const QString& micType);

protected:
    void resizeEvent(QResizeEvent* e) override;
    void updateAttribute() override;
    void loadSettings() override;
    void AppSettingsChanged(QString objectName, QString attribute, QString value) override;
private:
    Ui::InputS2M1* ui;
    QFont mFont;
    QButtonGroup* mButtonGroup;
    QString mPreMIC;
    int mPreMUTE=-2147483648;
    int mPre48V=-2147483648;
    float mPreGAIN=-2147483648;
    void save(QAnyStringView key, const QVariant& value);
private slots:
    void in_widgetPushButtonGroup2_buttonStateChanged(PushButtonS1M2::ButtonID button, bool state);
    void on_lineEdit_textChanged(const QString& arg1);
    void on_lineEdit_editingFinished();
    void on_button48V_clicked(bool checked);
    void on_buttonMute_clicked(bool checked);
    void on_slider_valueChanged(int value);
};


#endif // InputS2M1_H

