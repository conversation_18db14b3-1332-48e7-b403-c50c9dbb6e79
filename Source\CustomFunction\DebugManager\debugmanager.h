#ifndef DEBUGMANAGER_H
#define DEBUGMANAGER_H


#include <QDebug>


#define DebugLevel  5


class DebugManager
{
public:
    static DebugManager& instance() { return mInstance; }
    static void installMessageHandler();
private:
    static DebugManager mInstance;
    DebugManager() = default;
    DebugManager(const DebugManager&) = delete;
    ~DebugManager() { }
    DebugManager& operator=(const DebugManager&) = delete;
    static void customMessageHandler(QtMsgType type, const QMessageLogContext& context, const QString& msg);
};


#define DBGMHandle DebugManager::instance()


#endif // DEBUGMANAGER_H

