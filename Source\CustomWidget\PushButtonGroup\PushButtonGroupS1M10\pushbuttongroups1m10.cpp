#include "globalfont.h"
#include "pushbuttongroups1m10.h"
#include "ui_pushbuttongroups1m10.h"


PushButtonGroupS1M10::PushButtonGroupS1M10(QWidget *parent)
    : QWidget(parent)
    , ui(new Ui::PushButtonGroupS1M10)
{
    ui->setupUi(this);
    mTimer.setSingleShot(true);
    mTimer.setInterval(200);
    connect(&mTimer, &QTimer::timeout, this, [this](){
        QString value;
        if(mMouseButton == Qt::LeftButton) value = "LeftButtonSingle";
        else if(mMouseButton == Qt::RightButton) value = "RightButtonSingle";
        else if(mMouseButton == Qt::MiddleButton) value = "MiddleButtonSingle";
        if(mButton == ui->PushButtonEQ) emit mouseClickEvent("EQ", value);
        if(mMouseButton == Qt::LeftButton) mButton->click();
    });
    ui->PushButtonEQ->setCheckable(true);
    ui->PushButtonEQ->setAttribute(Qt::WA_LayoutUsesWidgetRect);
    ui->PushButtonEQ->installEventFilter(this);
    mStyle[0] = "QFrame { image: url(:/Image/PushButtonGroup/PBG14_0.png); }";
    mStyle[1] = "QFrame { image: url(:/Image/PushButtonGroup/PBG14_1.png); }";
    setState("EQ", "0", false);
    setLanguage("English");
}
PushButtonGroupS1M10::~PushButtonGroupS1M10()
{
    delete ui;
}


// override
bool PushButtonGroupS1M10::eventFilter(QObject* obj, QEvent* e)
{
    if(e->type() == QEvent::MouseButtonRelease)
    {
        QMouseEvent* mouseEvent=static_cast<QMouseEvent*>(e);
        if(mTimer.isActive())
        {
            mTimer.stop();
            if(mMouseButton == mouseEvent->button() && obj == mButton)
            {
                QString value;
                if(mMouseButton == Qt::LeftButton) value = "LeftButtonDouble";
                else if(mMouseButton == Qt::RightButton) value = "RightButtonDouble";
                else if(mMouseButton == Qt::MiddleButton) value = "MiddleButtonDouble";
                if(mButton == ui->PushButtonEQ) emit mouseClickEvent("EQ", value);
            }
        }
        else
        {
            mTimer.start();
            mMouseButton = mouseEvent->button();
            mButton = qobject_cast<QPushButton*>(obj);
        }
        return true;
    }
    return QWidget::eventFilter(obj, e);
}
void PushButtonGroupS1M10::resizeEvent(QResizeEvent* e)
{
    Q_UNUSED(e);
    mFont.setPointSize(GLBFHandle.getSuitablePointSize(mFont, ui->PushButtonEQ->height()) - 1);
    ui->PushButtonEQ->setFont(mFont);
}


// slot
void PushButtonGroupS1M10::on_PushButtonEQ_clicked(bool checked)
{
    setState("EQ", QString::number(checked));
}


// setter & getter
PushButtonGroupS1M10& PushButtonGroupS1M10::setFont(QFont font)
{
    mFont = font;
    QResizeEvent e(size(), size());
    resizeEvent(&e);
    update();
    return *this;
}
PushButtonGroupS1M10& PushButtonGroupS1M10::setLanguage(QString language)
{
    if(language == "English")
    {
        // ui->PushButtonEQ->setText("EQ");
    }
    else if(language == "Chinese")
    {
        // ui->PushButtonEQ->setText("EQ");
    }
    return *this;
}
PushButtonGroupS1M10& PushButtonGroupS1M10::setState(QString button, QString state, bool needEmit)
{
    unsigned int bitMask=0;
    QPushButton* currentButton=nullptr;
    if(button == "EQ")
    {
        bitMask = 0x0001;
        currentButton = ui->PushButtonEQ;
    }
    if(currentButton != nullptr)
    {
        mBitmap &= ~bitMask;
        if(state.toInt()) mBitmap |= bitMask;
        ui->frame->setStyleSheet(mStyle.value(mBitmap));
        currentButton->setChecked(state.toInt());
        currentButton->setStyleSheet(state.toInt() ? "QPushButton { color: rgb(222, 222, 222); }" : "QPushButton { color: rgb(161, 161, 161); }");
        if(needEmit) emit stateChanged(button, state);
    }
    return *this;
}
QString PushButtonGroupS1M10::getState(QString button)
{
    QPushButton* currentButton=nullptr;
    if(button == "EQ")
    {
        currentButton = ui->PushButtonEQ;
    }
    if(currentButton != nullptr)
    {
        return QString::number(currentButton->isChecked());
    }
    return QString::number(false);
}

