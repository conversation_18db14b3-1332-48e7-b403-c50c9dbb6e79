#include "effectbase.h"


// setter & getter
EffectBase& EffectBase::setChannelName(QString name)
{
    mChannelName = name;
    return *this;
}
EffectBase& EffectBase::setWidgetEnable(bool state)
{
    mEnable = state;
    return *this;
}
EffectBase& EffectBase::setWidgetEnableWithUpdate(bool state)
{
    mEnable = state;
    if(mEmitAction)
    {
        emit attributeChanged(this->objectName(), "Save_Enable", QString::number(mEnable));
    }
    updateAttribute();
    return *this;
}
EffectBase& EffectBase::setWidgetMovable(bool state)
{
    mMovable = state;
    return *this;
}
EffectBase& EffectBase::setWidgetReady(bool state)
{
    mReady = state;
    return *this;
}
EffectBase& EffectBase::setWidgetEmitAction(bool state)
{
    mEmitAction = state;
    return *this;
}

