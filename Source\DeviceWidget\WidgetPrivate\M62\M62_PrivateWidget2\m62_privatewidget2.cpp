#include "m62_privatewidget2.h"
#include <QTimer>
#include <QGridLayout>
#include <QMenu>
#include <QWidgetAction>
#include <QPushButton>
#include <QStyleOption>
#include <QPainter>
#include "menus1m1.h"
#include <float.h>

M62_PrivateWidget2::M62_PrivateWidget2(QWidget *parent, const QString& name)
    : QWidget(parent),
    WorkspaceObserver(name),
    AppSettingsObserver(name)
{
    setStyleSheet("background:transparent");
    mWidget = new QWidget(this);
    mWidget->setMinimumSize(53, 160);
    mWidget->setStyleSheet("background: rgba(31, 31, 31, 1);");
    mMenuScene = new MenuS1M1(mWidget, "Scene");
    mMenuScene->setButtonExlusive(true);
    mMenuScene->addButton({"Chat", "Chat", false});
    mMenuScene->addButton({"Vocal", "Vocal", false});
    mMenuScene->setButtonChecked("Chat", true);
    mMenuScene->setStretchFactor(30, 70);
    mMenuScene->setButtonFontSizeRatio(0.5);
    mMenuPresets = new MenuS1M1(mWidget, "User presets");
    mMenuPresets->addButton({"Load preset1", "Load preset1", false});
    mMenuPresets->addButton({"Load preset2", "Load preset2", false});
    mMenuPresets->addButton({"Save preset1", "Save preset1", false});
    mMenuPresets->addButton({"Save preset2", "Save preset2", false});
    mMenuPresets->setStretchFactor(18, 82);
    mMenuPresets->setButtonFontSizeRatio(0.5);
    initSigConnect();
}

M62_PrivateWidget2::~M62_PrivateWidget2()
{

}

M62_PrivateWidget2& M62_PrivateWidget2::setName(const QString &name)
{
    setObjectName(name);
    WorkspaceObserver::setObserverName(name);
    AppSettingsObserver::setObserverName(name);
    return *this;
}

M62_PrivateWidget2& M62_PrivateWidget2::setFont(const QFont& font)
{
    setAllChildFont(this, font);
    return *this;
}

void M62_PrivateWidget2::paintEvent(QPaintEvent *)
{
    QStyleOption option;
    option.initFrom(this);
    QPainter painter(this);
    style()->drawPrimitive(QStyle::PE_Widget, &option, &painter, this);
}

void M62_PrivateWidget2::resizeEvent(QResizeEvent *e)
{
    setMinimumWidth(size().height() / 3);
    setMaximumWidth(size().height() / 3);
    auto getWHHoldRatio = [](QWidget* parent, QWidget* child, double wRatio=1.0f, double hRatio=1.0f){
        double aspectRatio = static_cast<double>(child->minimumWidth()) / child->minimumHeight();
        double widthScale = static_cast<double>(parent->width()) *wRatio / child->minimumWidth();
        double heightScale = static_cast<double>(parent->height()) *hRatio / child->minimumHeight();
        double scale = std::min(widthScale, heightScale);
        double newWidth = child->minimumWidth() * scale;
        int newHeight = static_cast<int>(newWidth / aspectRatio);
        return std::make_tuple(newWidth, newHeight);
    };
    auto childCenterToParentAndHoldRatio = [=](QWidget* parent, QWidget* child){
        auto wh =  getWHHoldRatio(parent, child);
        int w =std::get<0>(wh);
        int h =std::get<1>(wh);
        child->setGeometry((parent->width()-w)/2,(parent->height()-h)/2,w, h);
    };
    childCenterToParentAndHoldRatio(this, mWidget);

    double hPixelPerRatio = mWidget->height() / 100.0;
    double wPixelPerRatio = mWidget->width() / 100.0;
    double spacing = 3 * hPixelPerRatio;

    mMenuScene->setGeometry(10*wPixelPerRatio, 6 * hPixelPerRatio, 80 * wPixelPerRatio, 24.5 * hPixelPerRatio);
    mMenuPresets->setGeometry(10*wPixelPerRatio, (6 + 24.5 + 4) * hPixelPerRatio, 80 * wPixelPerRatio, 41.9 * hPixelPerRatio);
}

void M62_PrivateWidget2::loadSettings()
{

}

void M62_PrivateWidget2::AppSettingsChanged(QString objectName, QString attribute, QString value)
{
    if (attribute == "Language") {
        if(value == "English") {
            mMenuScene->setTitle("Scene");
            mMenuPresets->setTitle("User presets");
            mMenuScene->setButtonText("Chat", "Chat");
            mMenuScene->setButtonText("Vocal", "Vocal");
            mMenuPresets->setButtonText("Load preset1", "Load preset1");
            mMenuPresets->setButtonText("Load preset2", "Load preset2");
            mMenuPresets->setButtonText("Save preset1", "Save preset1");
            mMenuPresets->setButtonText("Save preset2", "Save preset2");
        } else if (value == "Chinese") {
            mMenuScene->setTitle("场景");
            mMenuPresets->setTitle("用户配置");
            mMenuScene->setButtonText("Chat", "聊天");
            mMenuScene->setButtonText("Vocal", "唱歌");
            mMenuPresets->setButtonText("Load preset1", "调用配置1");
            mMenuPresets->setButtonText("Load preset2", "调用配置2");
            mMenuPresets->setButtonText("Save preset1", "保存配置1");
            mMenuPresets->setButtonText("Save preset2", "保存配置2");
        }
    }
}

void M62_PrivateWidget2::setAllChildFont(QWidget* widget, const QFont& font)
{
    for (auto child : widget->children()) {
        QWidget *widget = qobject_cast<QWidget *>(child);
        if (widget) {
            widget->setFont(font);
            setAllChildFont(widget, font);
        }
    }
}

void M62_PrivateWidget2::initSigConnect()
{
    connect(mMenuScene, &MenuS1M1::attributeChanged, this, [this](QString objectName, QString attribute, QString value){
        emit attributeChanged(this->objectName(), attribute, value);        
    });
    connect(mMenuPresets, &MenuS1M1::attributeChanged, this, [this](QString objectName, QString attribute, QString value){
        emit attributeChanged(this->objectName(), attribute, value);
    });
}