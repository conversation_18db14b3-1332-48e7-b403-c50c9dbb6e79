#ifndef PUSHBUTTONS1M5_H
#define PUSHBUTTONS1M5_H


#include <QLabel>
#include <QWidget>
#include <QPushButton>
#include <QResizeEvent>


class PushButtonS1M5 : public QWidget
{
    Q_OBJECT
public:
    explicit PushButtonS1M5(QWidget* parent=nullptr);
    ~PushButtonS1M5();
    enum ButtonID
    {
        buttonNC=0,
        buttonIN1,
        buttonIN2,
        buttonIN12,
        buttonOFF,
        buttonSTU,
        buttonLIVE,
        buttonHALL
    };
    PushButtonS1M5& setFont(QFont font);
    PushButtonS1M5& setPushButtonStateNC(bool state);
    PushButtonS1M5& setPushButtonStateIN1(bool state);
    PushButtonS1M5& setPushButtonStateIN2(bool state);
    PushButtonS1M5& setPushButtonStateIN12(bool state);
    PushButtonS1M5& setPushButtonStateOFF(bool state);
    PushButtonS1M5& setPushButtonStateSTU(bool state);
    PushButtonS1M5& setPushButtonStateLIVE(bool state);
    PushButtonS1M5& setPushButtonStateHALL(bool state);
    bool getPushButtonStateNC();
    bool getPushButtonStateIN1();
    bool getPushButtonStateIN2();
    bool getPushButtonStateIN12();
    bool getPushButtonStateOFF();
    bool getPushButtonStateSTU();
    bool getPushButtonStateLIVE();
    bool getPushButtonStateHALL();
protected:
    void resizeEvent(QResizeEvent* e) override;
private:
    QFont mFont;
    bool mPushButtonStateNC=false;
    bool mPushButtonStateIN1=false;
    bool mPushButtonStateIN2=false;
    bool mPushButtonStateIN12=false;
    bool mPushButtonStateOFF=false;
    bool mPushButtonStateSTU=false;
    bool mPushButtonStateLIVE=false;
    bool mPushButtonStateHALL=false;
    QPushButton mPushButtonNC;
    QPushButton mPushButtonIN1;
    QPushButton mPushButtonIN2;
    QPushButton mPushButtonIN12;
    QPushButton mPushButtonOFF;
    QPushButton mPushButtonSTU;
    QPushButton mPushButtonLIVE;
    QPushButton mPushButtonHALL;
    QLabel mLabelNC;
    QLabel mLabelIN1;
    QLabel mLabelIN2;
    QLabel mLabelIN12;
private slots:
    void in_mPushButtonNC_clicked();
    void in_mPushButtonIN1_clicked();
    void in_mPushButtonIN2_clicked();
    void in_mPushButtonIN12_clicked();
    void in_mPushButtonOFF_clicked();
    void in_mPushButtonSTU_clicked();
    void in_mPushButtonLIVE_clicked();
    void in_mPushButtonHALL_clicked();
signals:
    void buttonStateChanged(ButtonID button, bool state);
};


#endif // PUSHBUTTONS1M5_H

