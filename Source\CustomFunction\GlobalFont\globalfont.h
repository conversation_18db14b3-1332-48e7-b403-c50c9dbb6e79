#ifndef GLOBALFONT_H
#define GLOBALFONT_H


#include <QFont>
#include <QRect>
#include <QString>


class GlobalFont
{
public:
    static GlobalFont& instance() { return mInstance; }
    bool load(const QStringList& files);
    QFont font();
    QFont font(QFont::Weight weight);
    QFont font(QString family, QFont::Weight weight);
    GlobalFont& setPixelSize(int size);
    GlobalFont& setPointSize(int size);
    int getPixelSize();
    int getPointSize();
    int getSuitablePixelSize(QFont font, QString text, QRect rect);
    int getSuitablePixelSize(QString text, QRect rect);
    int getSuitablePixelSize(QFont font, QString text, int width);
    int getSuitablePixelSize(QFont font, int height);
    int getSuitablePixelSize(QString text, int width);
    int getSuitablePixelSize(int height);
    int getSuitablePointSize(<PERSON>Font font, QString text, QRect rect);
    int getSuitablePointSize(QString text, QRect rect);
    int getSuitablePointSize(QFont font, QString text, int width);
    int getSuitablePointSize(QFont font, int height);
    int getSuitablePointSize(QString text, int width);
    int getSuitablePointSize(int height);
    int getSuitableWidth(QFont font, QString text, int height);
    int getSuitableWidth(QString text, int height);
    QString getLongerInPointSize(QFont font, QString text1, QString text2);
    QString getLongerInPointSize(QString text1, QString text2);
    QString getLongerInPixelSize(QFont font, QString text1, QString text2);
    QString getLongerInPixelSize(QString text1, QString text2);
    QString getShorterInPointSize(QFont font, QString text1, QString text2);
    QString getShorterInPointSize(QString text1, QString text2);
    QString getShorterInPixelSize(QFont font, QString text1, QString text2);
    QString getShorterInPixelSize(QString text1, QString text2);
    bool isSuitable(QFont font, QString text, QRect rect);
    bool isSuitable(QFont font, QString text, int width);
    bool isSuitable(QFont font, int height);
    bool isSuitable(QString text, QRect rect);
    bool isSuitable(QString text, int width);
    bool isSuitable(int height);
    GlobalFont& showSupported();
private:
    static GlobalFont mInstance;
    QFont mGlobal;
    GlobalFont() = default;
    GlobalFont(const GlobalFont&) = delete;
    ~GlobalFont() { }
    GlobalFont& operator=(const GlobalFont&) = delete;
};


#define GLBFHandle GlobalFont::instance()


#endif // GLOBALFONT_H

