/************************************************************************
 *  Generic utilities for basic_string handling.
 *
 *  Thesycon GmbH, Germany
 *  http://www.thesycon.de
 *
 ************************************************************************/

#ifndef __TbStdStringUtils_h__
#define __TbStdStringUtils_h__

// optionally put everything into a namespace
#ifdef LIBTB_NAMESPACE
namespace LIBTB_NAMESPACE {
#endif

//
// Replace all occurrences of substring 'oldSub' within 'src' by the substring 'newSub' and returns the resulting string. 
// Note: To indicate an error the returned string is empty (e.g. if oldSub is empty).
//
std::wstring
TbStdReplaceAll(
    const std::wstring src,
    const std::wstring& oldSub,
    const std::wstring& newSub
    );


#include <vector>


// Implementation helper. DO NOT USE DIRECTLY.
template <typename CharT> // char, wchar_t, ...
std::vector<std::basic_string<CharT>>
TbSplitStringImplT(
    const std::basic_string<CharT>& source,
    const std::vector<std::basic_string<CharT>>& delimiters
    )
{
    using StringT = std::basic_string<CharT>;

    std::vector<std::basic_string<CharT>> ret;
    typename StringT::size_type curPos {0};

    for (;;) {
        typename StringT::size_type delimPos {StringT::npos};
        typename StringT::size_type delimLen {0};
        bool found {false};

        // search for a delimiter occurrence
        for ( const auto& delimiter : delimiters ) {
            typename StringT::size_type p {source.find(delimiter, curPos)};
            if ( StringT::npos != p ) {
                // delimiter found at position p
                found = true;
                if ( StringT::npos == delimPos ) {
                    // first match
                    delimPos = p;
                    delimLen = delimiter.length();
                } else {
                    // subsequent match
                    if ( p < delimPos ) { 
                        // match is at a lower position
                        delimPos = p;
                        delimLen = delimiter.length();
                    } else {
                        if ( p == delimPos ) {
                            // match is at the same position, use the longest delimiter sequence
                            delimLen = TbMax(delimiter.length(), delimLen);
                        } else {
                            // match is at a higher position, ignore match
                        }
                    }
                }
            }
        }

        if (!found) {
            // no more delimiters found
            ret.push_back(source.substr(curPos));
            break;
        }

        // delimiter found
        ret.push_back(source.substr(curPos, delimPos - curPos));
        // continue after the delimiter
        curPos = delimPos + delimLen;
    }

    return ret;
}

//
// Split a string according to the specified delimiters.
//
// If multiple delimiters are found at the same position, the longest delimiter string is preferred.
//
// some examples:
// source: "xx,yy;zz"  delimiters: [";", ","]   will be split into: ["xx", "yy", "zz"]
// source: "xx##yy;zz" delimiters: [";", "##"]  will be split into: ["xx", "yy", "zz"]
// source: "xxabcyy" delimiters: ["ab", "bc"]  will be split into: ["xx", "cyy"]
// source: "xxabcyy" delimiters: ["ab", "abc"] will be split into: ["xx", "yy"]
// source: "xxabcyy" delimiters: ["bc", "abc"] will be split into: ["xx", "yy"]
// source: "xxabcyy" delimiters: ["ab", "bc", "abc"] will be split into: ["xx", "yy"]
// source: ".aaa.bbb.ccc." delimiters: ["."] will be split into: ["", "aaa", "bbb", "ccc", ""]
// source: "key=value" delimiters: ["="] will be split into: ["key", "value"]
// source: "key = value" delimiters: ["="] will be split into: ["key ", " value"]
// source: "key=" delimiters: ["="] will be split into: ["key", ""]
// source: "key=value=x" delimiters: ["="] will be split into: ["key", "value", "x"]
//
template <typename CharT> // char, wchar_t, ...
std::vector<std::basic_string<CharT>>
TbSplitString(
    const std::basic_string<CharT>& source,
    const std::vector<std::basic_string<CharT>>& delimiters
    )
{
    return TbSplitStringImplT<CharT>(source, delimiters);
}

template <typename CharT> // char, wchar_t, ...
std::vector<std::basic_string<CharT>>
TbSplitString(
    const CharT* source,
    const std::vector<std::basic_string<CharT>>& delimiters
    )
{
    return TbSplitStringImplT<CharT>(std::basic_string<CharT>{source}, delimiters);
}

#ifdef LIBTB_NAMESPACE
}
#endif

#endif // __TbStdStringUtils_h__

/******************************** EOF ***********************************/
