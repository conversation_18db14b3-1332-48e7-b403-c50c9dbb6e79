#ifndef COMBOBOXS1M3_H
#define COMBOBOXS1M3_H


#include <QComboBox>

class ComboBoxS1M3 : public QComboBox
{
    Q_OBJECT
public:
    explicit ComboBoxS1M3(QWidget* parent=nullptr, Qt::Alignment textAlignment=Qt::AlignRight | Qt::AlignVCenter);
    void setFont(const QFont& font);
    void setIndicatorWHRatio(double ratio);
    void setColor(const QColor& color);

protected:
    bool eventFilter(QObject *watched, QEvent *event)override;
    void mousePressEvent(QMouseEvent* event)override;
    void resizeEvent(QResizeEvent *event) override;
    void wheelEvent(QWheelEvent* event) override;
    void showPopup() override;

private:
    QWidget* m_container=nullptr;
    double m_indicatorWH = 1.0;
    QColor m_color = Qt::white;
};


#endif // COMBOBOXS1M3_H

