/************************************************************************

    Description:

    Author(s):
        Reinhard <PERSON>ck
        
    Thesycon Software Solutions GmbH & Co. KG, Germany, www.thesycon.de

************************************************************************/

#ifndef __tlusbdfusdk_h__
#define __tlusbdfusdk_h__


//
// default compile time configuration
//

// 
// By default, load the API library (DLL) dynamically at runtime.
// Alternatively, link directly with the library and call API functions directly.
// 
#ifndef TLDFUAPI_USE_DYNAMIC_LIBRARY_LOADER
#define TLDFUAPI_USE_DYNAMIC_LIBRARY_LOADER 1
#endif

//
// NOTE: Automatic unload in destructor can cause problems because the API loader is global (singleton pattern)
// and Dynlib functions might be called from a destructor of another global object.
// The execution order of destructors for global objects is not deterministic in all cases.
// Hence automatic unloading is suppressed by default. We leave Dynlib unloading to the OS in this case.
//
#ifndef TLDFUAPI_UNLOAD_IN_DESTRUCTOR
#define TLDFUAPI_UNLOAD_IN_DESTRUCTOR 0
#endif


// API definition
#include "tlusbdfu_api.h"


#if TLDFUAPI_USE_DYNAMIC_LIBRARY_LOADER
#include "TLDynlibLoader.h"
#include "TLDfuApi.h"
#endif

#include "TLDfuEnumerator.h"
#include "TLDfuImage.h"
#include "TLDfuDevice.h"
#include "TLDfuNotification.h"




#endif

/*** EOF ***/
