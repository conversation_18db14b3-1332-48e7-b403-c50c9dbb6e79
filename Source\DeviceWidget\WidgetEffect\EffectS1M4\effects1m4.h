#ifndef EffectS1M4_H
#define EffectS1M4_H


#include <QFont>
#include "effectbase.h"
#include "workspace.h"
#include "appsettings.h"


namespace Ui {
class EffectS1M4;
}


class EffectS1M4 : public EffectBase, public WorkspaceObserver, public AppSettingsObserver
{
    Q_OBJECT

public:
    explicit EffectS1M4(QWidget* parent=nullptr, QString name="");
    ~EffectS1M4();
    EffectS1M4& setName(QString name);
    EffectS1M4& setFont(QFont font);
    void setReverbIcon(const QString& text);
    EffectS1M4& setReverbType(QString type);
    EffectS1M4& setValueDryWet(float value);
    EffectS1M4& setValueDryWetRange(float min, float max);
    EffectS1M4& setValueRoom(float value);
    EffectS1M4& setValueRoomRange(float min, float max);
    QString getValueReverbKey(QString type);
    int getValueReverb(QString type);
    EffectS1M4& updateReverbType(QString type);
    EffectS1M4& setValueDecay(float value);
    EffectS1M4& setValueDecayRange(float min, float max);
    EffectS1M4& setReverbTitle(const QString& title);

protected:
    void resizeEvent(QResizeEvent* e) override;
    void updateStyle();
    void updateAttribute() override;
    void loadSettings() override;
    void AppSettingsChanged(QString objectName, QString attribute, QString value) override;
    void save(const QString& key, const QVariant& value);

private:
    Ui::EffectS1M4* ui;
    QFont mFont;
    QString mReverbTypePre;
    QString mReverbType;
    int mReverbStudio=-2147483648;
    int mReverbLive=-2147483648;
    int mReverbHall=-2147483648;
    int mReverbBypass=-2147483648;
    float mPreDryWet=-2147483648;
    float mPreDecay=-2147483648;
};


#endif // EffectS1M4_H

