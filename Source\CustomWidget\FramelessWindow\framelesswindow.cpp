#include "framelesswindow.h"
#include <qdialog.h>
#include <QPainter>
#include <QPainterPath>
#include <QMouseEvent>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QLabel>
#include <QPushButton>
#include <QFrame>
#include <QGraphicsDropShadowEffect>
#include <QDialog>
#include <QEvent>
#include <QTimer>
#include "globalfont.h"

FramelessWindow::FramelessWindow(QWidget *parent)
    : QDialog(parent)
{
    setParent(parent);

    setAttribute(Qt::WA_TranslucentBackground);
    setAttribute(Qt::WA_Hover);
#ifdef Q_OS_MACOS
    setAttribute(Qt::WA_MacAlwaysShowToolWindow, true);
#endif

    initializeUI();
    initializeLayout();
    initializeTitleBar();
    initializeShadow();
    applyConfig();
}

void FramelessWindow::initializeUI()
{
    setStyleSheet("QWidget{background:transparent;}");

    m_mainLayout = new QVBoxLayout(this);
    m_mainLayout->setSpacing(0);
    m_mainLayout->setContentsMargins(m_config.shadowRadius, m_config.shadowRadius,
                                     m_config.shadowRadius, m_config.shadowRadius);

    m_contentWidget = new QFrame(this);
    m_contentWidget->installEventFilter(this);
    m_contentWidget->setObjectName("contentWidget");
    m_contentWidget->setStyleSheet("QFrame#contentWidget{border:1px solid rgb(70,70,70);}");
    m_mainLayout->addWidget(m_contentWidget);
}

void FramelessWindow::initializeLayout()
{
    m_contentWidgetLayout = new QVBoxLayout(m_contentWidget);
    m_contentWidgetLayout->setSpacing(0);
    m_contentWidgetLayout->setContentsMargins(0, 0, 0, 0);

    m_titleBar = new QWidget(m_contentWidget);
    m_contentWidgetLayout->addWidget(m_titleBar);

    m_centralWidgetLayout = new QVBoxLayout();
    m_centralWidgetLayout->setSpacing(0);
    m_centralWidgetLayout->setContentsMargins(0, 0, 0, 0);
    m_contentWidgetLayout->addLayout(m_centralWidgetLayout);

    setHeightRatio(m_config.titleHeightRatio, m_config.centralHeightRatio);
}

void FramelessWindow::initializeTitleBar()
{
    m_titleBarLayout = new QHBoxLayout(m_titleBar);
    m_titleBarLayout->setSpacing(5);
    m_titleBarLayout->setContentsMargins(10, 0, 5, 0);

    m_titleLabel = new QLabel("", m_titleBar);
    m_titleLabel->setAlignment(Qt::AlignCenter);
    m_titleLabel->setStyleSheet("background:transparent; color: rgb(161,161,161);");

    m_minimizeButton = new QPushButton(m_titleBar);
    m_minimizeButton->setMinimumSize(1, 1);
    m_minimizeButton->setSizePolicy(QSizePolicy::Preferred, QSizePolicy::Preferred);
    m_minimizeButton->setStyleSheet("QPushButton { border: none; background: transparent; image: url(:/Icon/min.svg); }");
    connect(m_minimizeButton, &QPushButton::clicked, this, [this](){
        showMinimized();
        emit buttonClicked(WindowButtonType::Minimize);
    });
    m_minimizeButton->hide();

    m_closeButton = new QPushButton(m_titleBar);
    m_closeButton->setMinimumSize(1, 1);
    m_closeButton->setSizePolicy(QSizePolicy::Preferred, QSizePolicy::Preferred);
    m_closeButton->setStyleSheet("QPushButton { border: none; background: transparent; image: url(:/Icon/close.svg); }");
    connect(m_closeButton, &QPushButton::clicked, this, [this](){
        done(m_config.closeButtonReturnCode);
        emit buttonClicked(WindowButtonType::Close);
    });
}

void FramelessWindow::initializeShadow()
{
    m_shadow = new QGraphicsDropShadowEffect(this);
    m_shadow->setBlurRadius(m_config.shadowRadius);
    m_shadow->setColor(m_config.shadowColor);
    m_shadow->setOffset(m_config.shadowOffset);
    m_shadow->setEnabled(m_config.shadowVisible);
    m_contentWidget->setGraphicsEffect(m_shadow);
}

void FramelessWindow::setMovable(bool movable)
{
    m_config.movable = movable;
}

void FramelessWindow::setResizable(bool resizable)
{
    m_config.resizable = resizable;
}
void FramelessWindow::setRightBottomDraggable(bool enable)
{
    m_config.rightBottomDraggable = enable;
    if (enable) {
        m_targetResizeRegion = static_cast<ResizeRegion>(ResizeRight | ResizeBottom);
    } else {
        m_targetResizeRegion = ResizeNone;
    }
}

void FramelessWindow::setTitle(const QString &title)
{
    if (m_titleLabel) {
        m_titleLabel->setText(title);
    }
}
void FramelessWindow::setCentralWidget(QWidget *widget)
{
    if (!widget) {
        return;
    }

    if (m_centralWidget) {
        m_centralWidgetLayout->removeWidget(m_centralWidget);
        m_centralWidget->deleteLater();
    }

    m_centralWidget = widget;
    m_centralWidgetLayout->addWidget(m_centralWidget);
}

void FramelessWindow::setHeightRatio(int titleRatio, int centralRatio)
{
    m_config.titleHeightRatio = titleRatio;
    m_config.centralHeightRatio = centralRatio;

    if (m_contentWidgetLayout) {
        m_contentWidgetLayout->setStretch(0, titleRatio);
        m_contentWidgetLayout->setStretch(1, centralRatio);
    }
}

void FramelessWindow::setTitleColor(const QColor &color)
{
    m_config.titleColor = color;
    applyConfig();
}
void FramelessWindow::setTitleBackground(const QColor &background)
{
    m_config.titleBackground = background;
    applyConfig();
}

void FramelessWindow::setTitleFont(const QFont &font)
{
    m_config.titleFont = font;
    if (m_titleLabel) {
        m_titleLabel->setFont(font);
    }
}

void FramelessWindow::setWindowMode(WindowMode mode)
{
    m_config.m_mode = mode;

    if (m_config.m_mode == WindowMode::Normal) {
        if (m_titleBar) {
            m_titleBar->setVisible(true);
        }
        if (m_contentWidget) {
            m_contentWidget->setStyleSheet("QFrame#contentWidget{border:1px solid rgb(70,70,70);}");
        }
    } else if (m_config.m_mode == WindowMode::Custom) {
        if (m_titleBar) {
            m_titleBar->setVisible(false);
        }
        if (m_contentWidget) {
            m_contentWidget->setStyleSheet("");
        }
    }
}

void FramelessWindow::setCloseButtonReturnCode(int code)
{
    m_config.closeButtonReturnCode = code;
}

int FramelessWindow::getCloseButtonReturnCode() const
{
    return m_config.closeButtonReturnCode;
}

void FramelessWindow::setShadowRadius(int radius)
{
    m_config.shadowRadius = radius;
    m_resizeEdgeWidth = radius + 10;

    if (m_mainLayout) {
        m_mainLayout->setContentsMargins(radius, radius, radius, radius);
    }
    if (m_shadow) {
        m_shadow->setBlurRadius(radius);
    }
}

void FramelessWindow::setShadowOffset(const QPoint& offset)
{
    m_config.shadowOffset = offset;
    if (m_shadow) {
        m_shadow->setOffset(offset);
    }
}

void FramelessWindow::setShadowColor(const QColor& color)
{
    m_config.shadowColor = color;
    if (m_shadow) {
        m_shadow->setColor(color);
    }
}

void FramelessWindow::setShadowVisible(bool visible)
{
    m_config.shadowVisible = visible;
    if (m_shadow) {
        m_shadow->setEnabled(visible);
    }
}

void FramelessWindow::setParent(QWidget *parent)
{
    QDialog::setParent(parent);
#ifdef Q_OS_MACOS
    setWindowFlags(Qt::FramelessWindowHint | Qt::Tool);
#elif defined(Q_OS_WIN)
    setWindowFlags(Qt::FramelessWindowHint | Qt::Dialog);
#endif
}

void FramelessWindow::setRestoreFlag(bool restore)
{
    m_restoreFlag = restore;
}

void FramelessWindow::restoreWindow()
{
    if (m_restoreFlag) {
        m_restoreFlag = false;
        show();
        raise();
    }
}

void FramelessWindow::setTitleAlign(TitleAlignment align)
{
    m_config.titleAlign = align;

    if (!m_titleLabel) {
        return;
    }

    switch(align) {
        case TitleAlignment::Left:
            m_titleLabel->setAlignment(Qt::AlignLeft | Qt::AlignVCenter);
            break;
        case TitleAlignment::Center:
            m_titleLabel->setAlignment(Qt::AlignCenter);
            break;
        case TitleAlignment::Right:
            m_titleLabel->setAlignment(Qt::AlignRight | Qt::AlignVCenter);
            break;
    }
    adjustGeometry();
}

void FramelessWindow::setWindowButtons(WindowButtons buttons)
{
    m_config.windowButtons = buttons;

    if (m_minimizeButton) {
        m_minimizeButton->setVisible(buttons & WindowButtonType::Minimize);
    }
    if (m_closeButton) {
        m_closeButton->setVisible(buttons & WindowButtonType::Close);
    }

    adjustGeometry();
}

bool FramelessWindow::event(QEvent* event)
{
    if (event->type() == QEvent::HoverMove) {
        QHoverEvent* hoverEvent = static_cast<QHoverEvent*>(event);

        if (!m_mousePressed) {
            updateCursorShape(hoverEvent->position());
        } else {
            QPointF delta = hoverEvent->globalPosition() - m_mousePressPos;

            if (m_resizing) {
                handleResizing(delta);
            } else if (m_config.movable) {
                handleMoving(delta);
            }
        }
    }

    return QDialog::event(event);
}

void FramelessWindow::handleResizing(const QPointF& delta)
{
    QRect newRect = m_windowRect;

    if (m_resizeRegion & ResizeLeft) {
        auto offest = newRect.left() + delta.x();
        if(newRect.right() - offest >= minimumSize().width()) {
            newRect.setLeft(newRect.left() + delta.x());
        }
    }
    if (m_resizeRegion & ResizeRight) {
        auto offest = newRect.right() + delta.x();
        if(offest - newRect.left() >= minimumSize().width()) {
            newRect.setRight(newRect.right() + delta.x());
        }
    }
    if (m_resizeRegion & ResizeTop) {
        auto offest = newRect.top() + delta.y();
        if(newRect.bottom() - offest >= minimumSize().height()) {
            newRect.setTop(newRect.top() + delta.y());
        }
    }
    if (m_resizeRegion & ResizeBottom) {
        auto offest = newRect.bottom() + delta.y();
        if(offest - newRect.top() >= minimumSize().height()) {
            newRect.setBottom(newRect.bottom() + delta.y());
        }
    }

    auto minimumSize = this->minimumSize();
    if (newRect.width() < minimumSize.width()) {
        newRect.setWidth(minimumSize.width());
    }
    if (newRect.height() < minimumSize.height()) {
        newRect.setHeight(minimumSize.height());
    }

    if (m_config.rightBottomDraggable && m_targetResizeRegion != ResizeNone) {
        int aspectWidth = newRect.height() * minimumSize.width() / minimumSize.height();
        newRect.setWidth(aspectWidth);
    }

    move(newRect.topLeft());
    resize(newRect.size());
}

void FramelessWindow::handleMoving(const QPointF& delta)
{
    QPointF newPos = m_windowRect.topLeft() + delta;
    move(newPos.toPoint());
}

void FramelessWindow::mousePressEvent(QMouseEvent *event)
{
    if (event->button() == Qt::LeftButton) {
        m_mousePressed = true;
        m_mousePressPos = event->globalPosition();
        m_windowRect = geometry();

        if (m_resizeRegion != ResizeNone && m_config.resizable) {
            m_resizing = true;
        } else {
            m_resizing = false;
        }
    }
    QWidget::mousePressEvent(event);
}

void FramelessWindow::mouseReleaseEvent(QMouseEvent *event)
{
    m_mousePressed = false;
    m_resizing = false;
    m_resizeRegion = ResizeNone;
    unsetCursor();
    QWidget::mouseReleaseEvent(event);
}

void FramelessWindow::resizeEvent(QResizeEvent *event)
{
    Q_UNUSED(event)
    adjustGeometry();
}

void FramelessWindow::showEvent(QShowEvent *event)
{
    Q_UNUSED(event)
    adjustGeometry();
}

void FramelessWindow::adjustGeometry()
{
    if (!m_titleBar || !m_titleLabel) {
        return;
    }

    int titleHeight = m_titleBar->height();
    int titleWidth = m_titleBar->width();
    int buttonHeight = static_cast<int>(titleHeight * 0.9);
    int buttonSpacing = static_cast<int>(titleHeight * 0.1);

    int buttonWidth = buttonHeight;
    if (m_closeButton && m_closeButton->minimumHeight() > 0) {
        buttonWidth = buttonHeight * m_closeButton->minimumWidth() / m_closeButton->minimumHeight();
    }

    int buttonY = (titleHeight - buttonHeight) / 2;
    int buttonX = titleWidth;

    if ((m_config.windowButtons & WindowButtonType::Close) && m_closeButton && m_closeButton->isVisible()) {
        buttonX = buttonX - buttonWidth - buttonSpacing;
        m_closeButton->setGeometry(buttonX, buttonY, buttonWidth, buttonHeight);
    }

    if ((m_config.windowButtons & WindowButtonType::Minimize) && m_minimizeButton && m_minimizeButton->isVisible()) {
        buttonX = buttonX - buttonWidth - buttonSpacing;
        m_minimizeButton->setGeometry(buttonX, buttonY, buttonWidth, buttonHeight);
    }

    int allButtonWidth = titleWidth - buttonX;
    int labelMargin = buttonSpacing * 2;

    switch(m_config.titleAlign) {
        case TitleAlignment::Left:
            m_titleLabel->setGeometry(labelMargin, 0, titleWidth - allButtonWidth - labelMargin * 2, titleHeight);
            break;
        case TitleAlignment::Center:
            m_titleLabel->setGeometry(allButtonWidth, 0, titleWidth - 2 * allButtonWidth, titleHeight);
            break;
        case TitleAlignment::Right:
            m_titleLabel->setGeometry(labelMargin, 0, titleWidth - allButtonWidth - labelMargin * 2, titleHeight);
            break;
    }


    if (m_titleLabel->height() > 0) {
        QFont font = m_titleLabel->font();
        int suitableSize = GLBFHandle.getSuitablePixelSize(font, m_titleLabel->height()) * 90 / 100;
        font.setPixelSize(suitableSize);
        m_titleLabel->setFont(font);
    }


    m_config.borderRadius = static_cast<int>(titleHeight * 0.2);
    if (m_config.m_mode == WindowMode::Normal) {
        QString styleSheet = QString("QWidget{background:%1;border-radius:%2px;}")
                           .arg(m_config.centralBackground.name())
                           .arg(m_config.borderRadius);
        setStyleSheet(styleSheet);
    }
}

void FramelessWindow::updateResizeRegion(const QPointF &pos)
{
    QRect rect = this->rect();
    m_resizeRegion = ResizeNone;

    if (pos.x() < m_resizeEdgeWidth) {
        m_resizeRegion = static_cast<ResizeRegion>(m_resizeRegion | ResizeLeft);
    }
    if (pos.x() > rect.width() - m_resizeEdgeWidth) {
        m_resizeRegion = static_cast<ResizeRegion>(m_resizeRegion | ResizeRight);
    }
    if (pos.y() < m_resizeEdgeWidth) {
        m_resizeRegion = static_cast<ResizeRegion>(m_resizeRegion | ResizeTop);
    }
    if (pos.y() > rect.height() - m_resizeEdgeWidth) {
        m_resizeRegion = static_cast<ResizeRegion>(m_resizeRegion | ResizeBottom);
    }


    if (m_targetResizeRegion != ResizeNone && m_resizeRegion != m_targetResizeRegion) {
        m_resizeRegion = ResizeNone;
    }
}

void FramelessWindow::updateCursorShape(const QPointF &pos)
{
    if (!m_config.resizable) {
        return;
    }

    updateResizeRegion(pos);

    if (m_resizeRegion == (ResizeLeft | ResizeTop) || m_resizeRegion == (ResizeRight | ResizeBottom)) {
        setCursor(Qt::SizeFDiagCursor);
    } else if (m_resizeRegion == (ResizeRight | ResizeTop) || m_resizeRegion == (ResizeLeft | ResizeBottom)) {
        setCursor(Qt::SizeBDiagCursor);
    } else if (m_resizeRegion == ResizeLeft || m_resizeRegion == ResizeRight) {
        setCursor(Qt::SizeHorCursor);
    } else if (m_resizeRegion == ResizeTop || m_resizeRegion == ResizeBottom) {
        setCursor(Qt::SizeVerCursor);
    } else {
        unsetCursor();
    }
}

void FramelessWindow::applyConfig()
{

    if (m_titleBar) {
        QString titleBarStyle = QString("background: %1; color: %2;")
                              .arg(m_config.titleBackground.name())
                              .arg(m_config.titleColor.name());
        m_titleBar->setStyleSheet(titleBarStyle);
    }


    if (m_titleLabel) {
        m_titleLabel->setFont(m_config.titleFont);
        QString labelStyle = QString("background:transparent; color: %1;")
                           .arg(m_config.titleColor.name());
        m_titleLabel->setStyleSheet(labelStyle);
    }


    if (m_shadow) {
        m_shadow->setBlurRadius(m_config.shadowRadius);
        m_shadow->setColor(m_config.shadowColor);
        m_shadow->setOffset(m_config.shadowOffset);
        m_shadow->setEnabled(m_config.shadowVisible);
    }


    if (m_mainLayout) {
        m_mainLayout->setContentsMargins(m_config.shadowRadius, m_config.shadowRadius,
                                       m_config.shadowRadius, m_config.shadowRadius);
    }
}