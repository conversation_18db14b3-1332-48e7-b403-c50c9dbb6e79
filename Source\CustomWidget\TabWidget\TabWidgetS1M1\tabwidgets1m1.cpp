#include "tabwidgets1m1.h"
#include "appsettings.h"
#include "framelesswindow.h"
#include <QStackedWidget>
#include <QVBoxLayout>

TabWidgetS1M1::TabWidgetS1M1(QWidget *parent, const QString &name)
    : Fr<PERSON>essWindow(parent), AppSettingsObserver(name), centerWidget(new QWidget(this))
{
    AppSettingsSubject::instance().addObserver(this);
    setRightBottomDraggable();
    setMinimumSize(350, 250);

    m_tabBar = new CustomTabBar(centerWidget);
    m_tabBar->setTabStretch({60, 72, 64, 72, 64, 72, 64, 72, 60});
    m_stack = new QStackedWidget(centerWidget);
    m_layout = new QVBoxLayout(centerWidget);
    m_layout->addWidget(m_tabBar);
    m_layout->addWidget(m_stack);
    m_layout->setStretch(0,36);
    m_layout->setStretch(1,373);
    m_layout->setSpacing(0);
    m_layout->setContentsMargins(0, 0, 0, 0);

    setCentralWidget(centerWidget);

    QString buttonStyle = QString(R"(
        QWidget{
            border-radius: 0px;
            border-bottom: 1px solid #444444;
        }
        QPushButton {
            border: none;
            background: transparent;
            color: #aaaaaa;
        }
        QPushButton:checked {
            color: #00d37f;
            border-bottom: 3px solid #00d37f;
        }
        QPushButton:hover {
            color: #00d37f;
        }
    )");
    m_tabBar->setStyleSheet(buttonStyle);

    connect(m_tabBar, &CustomTabBar::currentChanged, this, &TabWidgetS1M1::onTabChanged);
}

void TabWidgetS1M1::addTab(QWidget *widget, const QString &title) {
    m_stack->addWidget(widget);
    m_tabBar->addTab(title);
}

void TabWidgetS1M1::setName(const QString &name) {
    setObjectName(name);
    AppSettingsObserver::setObserverName(name);
}

void TabWidgetS1M1::setFont(const QFont &font) {
    m_font = font;
    m_tabBar->setFont(font);
}

void TabWidgetS1M1::changeLanguage(QString language) {
    const auto& vector = m_langHash.value(language);
    for (const auto& pair : vector) {
        int index = pair.first;
        QString text = pair.second;
        m_tabBar->setTabText(index, text);
    }
}

void TabWidgetS1M1::registerLangage(const QHash<QString, QVector<QPair<int, QString>>> &langHash) {
    m_langHash = langHash;
}

void TabWidgetS1M1::setWidthHeight(bool isCenter)
{
    QRect rect = APPSHandle.getMainWindow()->geometry();
    int h = rect.height() * 0.6;
    int w = h * minimumWidth() / minimumHeight();
    resize(w, h);
    if(isCenter){
        move(QPoint(rect.x(),rect.y()) + QPoint((rect.width() - w) / 2, (rect.height() - h) / 2));
    }
}

void TabWidgetS1M1::show()
{
    setWidthHeight(true);
    setVisible(true);
}

void TabWidgetS1M1::AppSettingsChanged(QString objectName, QString attribute, QString value) {
    if (attribute == "Language") {
        changeLanguage(value);
    }
}

void TabWidgetS1M1::onTabChanged(int index) {
    m_stack->setCurrentIndex(index);
}