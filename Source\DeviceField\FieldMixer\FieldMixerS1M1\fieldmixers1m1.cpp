#include "fieldmixers1m1.h"


FieldMixerS1M1::FieldMixerS1M1(QWidget* parent, QString name)
    : FieldMixerBase1(parent)
    , WorkspaceObserver(name)
    , AppSettingsObserver(name)
{
    connect(this, &FieldMixerBase1::attributeChanged, this, &FieldMixerS1M1::in_widgetBase_attributeChanged, Qt::UniqueConnection);
}
FieldMixerS1M1::~FieldMixerS1M1()
{
    for(auto element : mMixerList)
    {
        delete mVisibleListDefault.value(element);
    }
}


// override
void FieldMixerS1M1::loadSettings()
{
    WorkspaceObserver::getSettings()->beginGroup(objectName());
    bool flag=WorkspaceObserver::getSettings()->contains(objectName());
    WorkspaceObserver::getSettings()->endGroup();
    if(!flag)
    {
        WorkspaceObserver::setValue(objectName(), true);
        WorkspaceObserver::setValue("Mixer", mMixerList.first());
        for(auto element : mMixerList)
        {
            WorkspaceObserver::setValue(element + "_SoloState", 0);
            WorkspaceObserver::setValue(element + "_Visible", *mVisibleListDefault.value(element));
        }
    }
    for(auto element : mMixerList)
    {
        mOriginSoloState.insert(element, WorkspaceObserver::value(element + "_SoloState").toInt());
        QVariantList visibleList=WorkspaceObserver::value(element + "_Visible").toList();
        QString str;
        str.clear();
        str.append('|');
        for(auto element : visibleList)
        {
            str.append(element.toString());
            str.append('|');
        }
        mOriginVisibleList.insert(element, str);
    }
    mMixer = WorkspaceObserver::value("Mixer").toString();
    mSoloState = WorkspaceObserver::value(mMixer + "_SoloState").toInt();
    QVariantList visibleList=WorkspaceObserver::value(mMixer + "_Visible").toList();
    QVector<QString> list;
    for(auto element : visibleList)
    {
        list.append(element.toString());
    }
    setMixer(mMixer);
    setVisibleList(list);
    emit attributeChanged("Mixer_All", "Save_Mixer", mMixer);
    for(auto element : mMixerList)
    {
        emit attributeChanged("Mixer_All", "Save_" + element + "_SoloState", QString::number(mOriginSoloState.value(element)));
    }
}
void FieldMixerS1M1::AppSettingsChanged(QString objectName, QString attribute, QString value)
{
    Q_UNUSED(objectName);
    if(attribute == "Language")
    {
        if(value == "English")
        {
            setFieldTitle("Mixer");
        }
        else if(value == "Chinese")
        {
            setFieldTitle("混音");
        }
    }
}


// slot
void FieldMixerS1M1::in_widgetBase_attributeChanged(QString objectName, QString attribute, QString value)
{
    if(attribute == "Mixer")
    {
        mMixer = value;
        WorkspaceObserver::setValue("Mixer", mMixer);
        mSoloState = WorkspaceObserver::value(mMixer + "_SoloState").toInt();
        QVariantList visibleList=WorkspaceObserver::value(mMixer + "_Visible").toList();
        QVector<QString> list;
        for(auto element : visibleList)
        {
            list.append(element.toString());
        }
        setVisibleList(list);
        for(auto widget : mWidgetList)
        {
            widget->handleFieldMixerChanged(mMixer);
        }
        emit attributeChanged("Mixer_All", "Save_Mixer", mMixer);
    }
    else if(attribute == "Visible")
    {
        if(value.toInt())
        {
            QVariantList visibleList=WorkspaceObserver::value(mMixer + "_Visible").toList();
            visibleList.append(QVariant(objectName));
            WorkspaceObserver::setValue(mMixer + "_Visible", visibleList);
        }
        else
        {
            QVariantList visibleList=WorkspaceObserver::value(mMixer + "_Visible").toList();
            visibleList.removeOne(QVariant(objectName));
            WorkspaceObserver::setValue(mMixer + "_Visible", visibleList);
        }
    }
}
void FieldMixerS1M1::in_widgetList_attributeChanged(QString objectName, QString attribute, QString value)
{
    Q_UNUSED(objectName);
    if(attribute == "Solo")
    {
        if(value.toInt())
        {
            mSoloState++;
            if(mSoloState == 1)
            {
                for(auto widget : mWidgetList)
                {
                    widget->handleFieldSoloStateChanged(mSoloState);
                }
            }
        }
        else
        {
            mSoloState--;
            if(mSoloState == 0)
            {
                for(auto widget : mWidgetList)
                {
                    widget->handleFieldSoloStateChanged(mSoloState);
                }
            }
        }
        WorkspaceObserver::setValue(mMixer + "_SoloState", mSoloState);
        emit attributeChanged("Mixer_All", "Save_" + mMixer + "_SoloState", QString::number(mSoloState));
    }
}


// setter & getter
FieldMixerS1M1& FieldMixerS1M1::setName(QString name)
{
    setObjectName(name);
    WorkspaceObserver::setObserverName(name);
    AppSettingsObserver::setObserverName(name);
    return *this;
}
FieldMixerS1M1& FieldMixerS1M1::modifyMixerList(QVector<MixerInfo> list)
{
    for(auto element : list)
    {
        mMixerList.append(element.name);
        QVariantList* newList=new QVariantList();
        mVisibleListDefault.insert(element.name, newList);
    }
    FieldMixerBase1::modifyMixerList(list);
    return *this;
}
FieldMixerS1M1& FieldMixerS1M1::modifyWidgetList(QVector<MixerBase*> list)
{
    mWidgetList = list;
    for(auto element : list)
    {
        element->setWidgetMovable(true).assignMixer(&mMixer).assignMixerList(&mMixerList).assignOriginSoloState(&mOriginSoloState).assignOriginVisibleList(&mOriginVisibleList).assignSoloState(&mSoloState);
        connect(element, SIGNAL(attributeChanged(QString, QString, QString)), this, SLOT(in_widgetList_attributeChanged(QString, QString, QString)), Qt::UniqueConnection);
    }
    FieldMixerBase1::modifyWidgetList(list);
    return *this;
}
FieldMixerS1M1& FieldMixerS1M1::setVisibleListDefault(QString mixer, QVector<MixerBase*> list)
{
    for(auto element : mMixerList)
    {
        if(element == mixer)
        {
            for(auto widget : list)
            {
                mVisibleListDefault.value(element)->append(QVariant(widget->getChannelName()));
            }
            break;
        }
    }
    return *this;
}

