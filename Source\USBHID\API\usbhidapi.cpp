#include <QDebug>

#include "usbhidapi.h"


USBHIDAPI USBHIDAPI::mInstance;


void USBHIDAPI::init()
{
    hid_init();
}
void USBHIDAPI::exit()
{
    hid_exit();
}
int USBHIDAPI::read(unsigned char* data, int length)
{
    if(mHIDDevice == NULL)
    {
        return -1;
    }
    int ret=hid_read(mHIDDevice, data, length);
    if(ret == -1)
    {
        closeDevice();
    }
    return ret;
}
int USBHIDAPI::send(unsigned char* data, int length)
{
    if(mHIDDevice == NULL)
    {
        return -1;
    }
    int ret=hid_write(mHIDDevice, data, length);
    if(ret == -1)
    {
        closeDevice();
    }
    return ret;
}
bool USBHIDAPI::openDevice(unsigned int deviceVendorID, unsigned int deviceProductID)
{
    mHIDDevice = hid_open(deviceVendorID, deviceProductID, NULL);
    if(mHIDDevice != NULL)
    {
        return true;
    }
    return false;
}
void USBHIDAPI::closeDevice()
{
    if(mHIDDevice != NULL)
    {
        hid_close(mHIDDevice);
        mHIDDevice = NULL;
    }
}

