Source\CMakeFiles\TPCC.dir\TPCC_autogen\mocs_compilation.cpp.obj
Source\CMakeFiles\TPCC.dir\main.cpp.obj
Source\CMakeFiles\TPCC.dir\DeviceConnector\deviceconnector.cpp.obj
Source\CMakeFiles\TPCC.dir\DeviceConnector\DeviceConnectorView\DeviceConnectorViewBase\deviceconnectorviewbase.cpp.obj
Source\CMakeFiles\TPCC.dir\DeviceConnector\DeviceConnectorView\DeviceConnectorViewS1M1\deviceconnectorviews1m1.cpp.obj
Source\CMakeFiles\TPCC.dir\DeviceMainWindow\MainWindow_Base\mainwindow_base.cpp.obj
Source\CMakeFiles\TPCC.dir\DeviceMainWindow\MainWindow_M62\mainwindow_m62.cpp.obj
Source\CMakeFiles\TPCC.dir\DeviceField\FieldEffect\FieldEffectBase1\fieldeffectbase1.cpp.obj
Source\CMakeFiles\TPCC.dir\DeviceField\FieldEffect\FieldEffectS1M1\fieldeffects1m1.cpp.obj
Source\CMakeFiles\TPCC.dir\DeviceField\FieldHead\FieldHeadBase1\fieldheadbase1.cpp.obj
Source\CMakeFiles\TPCC.dir\DeviceField\FieldHead\FieldHeadBase2\fieldheadbase2.cpp.obj
Source\CMakeFiles\TPCC.dir\DeviceField\FieldHead\FieldHeadS1M1\fieldheads1m1.cpp.obj
Source\CMakeFiles\TPCC.dir\DeviceField\FieldHead\FieldHeadS1M2\fieldheads1m2.cpp.obj
Source\CMakeFiles\TPCC.dir\DeviceField\FieldInput\FieldInputBase1\fieldinputbase1.cpp.obj
Source\CMakeFiles\TPCC.dir\DeviceField\FieldInput\FieldInputS1M1\fieldinputs1m1.cpp.obj
Source\CMakeFiles\TPCC.dir\DeviceField\FieldLoopback\FieldLoopbackBase1\fieldloopbackbase1.cpp.obj
Source\CMakeFiles\TPCC.dir\DeviceField\FieldLoopback\FieldLoopbackS1M1\fieldloopbacks1m1.cpp.obj
Source\CMakeFiles\TPCC.dir\DeviceField\FieldMixer\FieldMixerBase1\fieldmixerbase1.cpp.obj
Source\CMakeFiles\TPCC.dir\DeviceField\FieldMixer\FieldMixerS1M1\fieldmixers1m1.cpp.obj
Source\CMakeFiles\TPCC.dir\DeviceField\FieldOrigin\FieldOriginBase1\fieldoriginbase1.cpp.obj
Source\CMakeFiles\TPCC.dir\DeviceField\FieldOrigin\FieldOriginBase2\fieldoriginbase2.cpp.obj
Source\CMakeFiles\TPCC.dir\DeviceField\FieldOrigin\FieldOriginBase3\fieldoriginbase3.cpp.obj
Source\CMakeFiles\TPCC.dir\DeviceField\FieldOrigin\FieldOriginS1M1\fieldorigins1m1.cpp.obj
Source\CMakeFiles\TPCC.dir\DeviceField\FieldOrigin\FieldOriginS1M2\fieldorigins1m2.cpp.obj
Source\CMakeFiles\TPCC.dir\DeviceField\FieldOrigin\FieldOriginS2M1\fieldorigins2m1.cpp.obj
Source\CMakeFiles\TPCC.dir\DeviceField\FieldOutput\FieldOutputBase1\fieldoutputbase1.cpp.obj
Source\CMakeFiles\TPCC.dir\DeviceField\FieldOutput\FieldOutputS1M1\fieldoutputs1m1.cpp.obj
Source\CMakeFiles\TPCC.dir\DeviceWidget\WidgetAutoGain\AutoGainS1M1\autogains1m1.cpp.obj
Source\CMakeFiles\TPCC.dir\DeviceWidget\WidgetEffect\EffectBase\effectbase.cpp.obj
Source\CMakeFiles\TPCC.dir\DeviceWidget\WidgetEffect\EffectS1M1\effects1m1.cpp.obj
Source\CMakeFiles\TPCC.dir\DeviceWidget\WidgetEffect\EffectS1M2\effects1m2.cpp.obj
Source\CMakeFiles\TPCC.dir\DeviceWidget\WidgetEffect\EffectS1M3\effects1m3.cpp.obj
Source\CMakeFiles\TPCC.dir\DeviceWidget\WidgetEffect\EffectS1M4\effects1m4.cpp.obj
Source\CMakeFiles\TPCC.dir\DeviceWidget\WidgetEqualizer\EqualizerBase\equalizerbase.cpp.obj
Source\CMakeFiles\TPCC.dir\DeviceWidget\WidgetEqualizer\EqualizerPanelS1M1\equalizerpanels1m1.cpp.obj
Source\CMakeFiles\TPCC.dir\DeviceWidget\WidgetEqualizer\EqualizerS1M1\equalizers1m1.cpp.obj
Source\CMakeFiles\TPCC.dir\DeviceWidget\WidgetEqualizer\EqualizerS1M2\equalizers1m2.cpp.obj
Source\CMakeFiles\TPCC.dir\DeviceWidget\WidgetInput\InputBase\inputbase.cpp.obj
Source\CMakeFiles\TPCC.dir\DeviceWidget\WidgetInput\InputS1M1\inputs1m1.cpp.obj
Source\CMakeFiles\TPCC.dir\DeviceWidget\WidgetInput\InputS1M2\inputs1m2.cpp.obj
Source\CMakeFiles\TPCC.dir\DeviceWidget\WidgetInput\InputS1M3\inputs1m3.cpp.obj
Source\CMakeFiles\TPCC.dir\DeviceWidget\WidgetInput\InputS1M4\inputs1m4.cpp.obj
Source\CMakeFiles\TPCC.dir\DeviceWidget\WidgetInput\InputS1M5\inputs1m5.cpp.obj
Source\CMakeFiles\TPCC.dir\DeviceWidget\WidgetInput\InputS1M6\inputs1m6.cpp.obj
Source\CMakeFiles\TPCC.dir\DeviceWidget\WidgetInput\InputS2M1\inputs2m1.cpp.obj
Source\CMakeFiles\TPCC.dir\DeviceWidget\WidgetInput\InputS2M2\inputs2m2.cpp.obj
Source\CMakeFiles\TPCC.dir\DeviceWidget\WidgetLoopback\LoopbackBase\loopbackbase.cpp.obj
Source\CMakeFiles\TPCC.dir\DeviceWidget\WidgetLoopback\LoopbackS1M1\loopbacks1m1.cpp.obj
Source\CMakeFiles\TPCC.dir\DeviceWidget\WidgetLoopback\LoopbackS1M2\loopbacks1m2.cpp.obj
Source\CMakeFiles\TPCC.dir\DeviceWidget\WidgetMixer\MixerBase\mixerbase.cpp.obj
Source\CMakeFiles\TPCC.dir\DeviceWidget\WidgetMixer\MixerS1M1\mixers1m1.cpp.obj
Source\CMakeFiles\TPCC.dir\DeviceWidget\WidgetMixer\MixerS1M2\mixers1m2.cpp.obj
Source\CMakeFiles\TPCC.dir\DeviceWidget\WidgetMixer\MixerS1M3\mixers1m3.cpp.obj
Source\CMakeFiles\TPCC.dir\DeviceWidget\WidgetMixer\MixerS1M4\mixers1m4.cpp.obj
Source\CMakeFiles\TPCC.dir\DeviceWidget\WidgetOrigin\OriginBase\originbase.cpp.obj
Source\CMakeFiles\TPCC.dir\DeviceWidget\WidgetOrigin\OriginS1M1\origins1m1.cpp.obj
Source\CMakeFiles\TPCC.dir\DeviceWidget\WidgetOrigin\OriginS1M10\origins1m10.cpp.obj
Source\CMakeFiles\TPCC.dir\DeviceWidget\WidgetOrigin\OriginS1M11\origins1m11.cpp.obj
Source\CMakeFiles\TPCC.dir\DeviceWidget\WidgetOrigin\OriginS1M12\origins1m12.cpp.obj
Source\CMakeFiles\TPCC.dir\DeviceWidget\WidgetOrigin\OriginS1M13\origins1m13.cpp.obj
Source\CMakeFiles\TPCC.dir\DeviceWidget\WidgetOrigin\OriginS1M2\origins1m2.cpp.obj
Source\CMakeFiles\TPCC.dir\DeviceWidget\WidgetOrigin\OriginS1M3\origins1m3.cpp.obj
Source\CMakeFiles\TPCC.dir\DeviceWidget\WidgetOrigin\OriginS1M4\origins1m4.cpp.obj
Source\CMakeFiles\TPCC.dir\DeviceWidget\WidgetOrigin\OriginS1M6\origins1m6.cpp.obj
Source\CMakeFiles\TPCC.dir\DeviceWidget\WidgetOrigin\OriginS1M7\origins1m7.cpp.obj
Source\CMakeFiles\TPCC.dir\DeviceWidget\WidgetOrigin\OriginS1M8\origins1m8.cpp.obj
Source\CMakeFiles\TPCC.dir\DeviceWidget\WidgetOrigin\OriginS1M9\origins1m9.cpp.obj
Source\CMakeFiles\TPCC.dir\DeviceWidget\WidgetOutput\OutputBase\outputbase.cpp.obj
Source\CMakeFiles\TPCC.dir\DeviceWidget\WidgetOutput\OutputS1M1\outputs1m1.cpp.obj
Source\CMakeFiles\TPCC.dir\DeviceWidget\WidgetOutput\OutputS1M2\outputs1m2.cpp.obj
Source\CMakeFiles\TPCC.dir\DeviceWidget\WidgetOutput\OutputS1M3\outputs1m3.cpp.obj
Source\CMakeFiles\TPCC.dir\DeviceWidget\WidgetOutput\OutputS1M4\outputs1m4.cpp.obj
Source\CMakeFiles\TPCC.dir\DeviceWidget\WidgetPrivate\DeviceAll\WidgetAbout1\widgetabout1.cpp.obj
Source\CMakeFiles\TPCC.dir\DeviceWidget\WidgetPrivate\DeviceAll\WidgetAudio1\widgetaudio1.cpp.obj
Source\CMakeFiles\TPCC.dir\DeviceWidget\WidgetPrivate\DeviceAll\WidgetSystem1\widgetsytem1.cpp.obj
Source\CMakeFiles\TPCC.dir\DeviceWidget\WidgetPrivate\M62\M62_PrivateWidget1\m62_privatewidget1.cpp.obj
Source\CMakeFiles\TPCC.dir\DeviceWidget\WidgetPrivate\M62\M62_PrivateWidget1\m62_privatewidget1_1.cpp.obj
Source\CMakeFiles\TPCC.dir\DeviceWidget\WidgetPrivate\M62\M62_PrivateWidget2\m62_privatewidget2.cpp.obj
Source\CMakeFiles\TPCC.dir\DeviceWidget\WidgetPrivate\M62\M62_PrivateWidget3\m62_privatewidget3.cpp.obj
Source\CMakeFiles\TPCC.dir\DeviceWidget\WidgetPrivate\M62\M62_PrivateWidget5\m62_privatewidget5.cpp.obj
Source\CMakeFiles\TPCC.dir\DeviceWidget\WidgetPrivate\M62\M62_PrivateWidget6\m62_privatewidget6.cpp.obj
Source\CMakeFiles\TPCC.dir\DeviceWidget\WidgetPrivate\M62\M62_PrivateWidget7\m62_privatewidget7.cpp.obj
Source\CMakeFiles\TPCC.dir\CustomWidget\Battery\BatteryS1M1\batterydrawstrategy.cpp.obj
Source\CMakeFiles\TPCC.dir\CustomWidget\Battery\BatteryS1M1\batterys1m1.cpp.obj
Source\CMakeFiles\TPCC.dir\CustomWidget\ButtonBox\ButtonBoxS1M1\buttonboxs1m1.cpp.obj
Source\CMakeFiles\TPCC.dir\CustomWidget\Chart\chart.cpp.obj
Source\CMakeFiles\TPCC.dir\CustomWidget\Circle\CircleS1M1\circles1m1.cpp.obj
Source\CMakeFiles\TPCC.dir\CustomWidget\ComboBox\ComboBoxS1M1\comboboxs1m1.cpp.obj
Source\CMakeFiles\TPCC.dir\CustomWidget\ComboBox\ComboBoxS1M2\comboboxs1m2.cpp.obj
Source\CMakeFiles\TPCC.dir\CustomWidget\ComboBox\ComboBoxS1M3\comboboxs1m3.cpp.obj
Source\CMakeFiles\TPCC.dir\CustomWidget\Dial\DialS1M1\dials1m1.cpp.obj
Source\CMakeFiles\TPCC.dir\CustomWidget\Dial\DialS1M2\dials1m2.cpp.obj
Source\CMakeFiles\TPCC.dir\CustomWidget\Dial\DialS1M3\dials1m3.cpp.obj
Source\CMakeFiles\TPCC.dir\CustomWidget\Dial\DialS1M4\dials1m4.cpp.obj
Source\CMakeFiles\TPCC.dir\CustomWidget\Dial\DialS1M5\dials1m5.cpp.obj
Source\CMakeFiles\TPCC.dir\CustomWidget\Dial\DialS1M6\dials1m6.cpp.obj
Source\CMakeFiles\TPCC.dir\CustomWidget\EqualizerController\EqualizerControllerS1M1\equalizercontrollers1m1.cpp.obj
Source\CMakeFiles\TPCC.dir\CustomWidget\EqualizerController\EqualizerControllerS1M1\eqwidgetiem.cpp.obj
Source\CMakeFiles\TPCC.dir\CustomWidget\FramelessWindow\framelesswindow.cpp.obj
Source\CMakeFiles\TPCC.dir\CustomWidget\Menu\MenuS1M1\menus1m1.cpp.obj
Source\CMakeFiles\TPCC.dir\CustomWidget\MessageBox\MessageBoxS1M1\messageboxs1m1.cpp.obj
Source\CMakeFiles\TPCC.dir\CustomWidget\MessageBox\MessageBoxS2M1\messageboxs2m1.cpp.obj
Source\CMakeFiles\TPCC.dir\CustomWidget\MessageBox\MessageBoxS3M1\messageboxs3m1.cpp.obj
Source\CMakeFiles\TPCC.dir\CustomWidget\MessageBox\MessageBoxWidget\MessageBoxWidget1\messageboxwidget1.cpp.obj
Source\CMakeFiles\TPCC.dir\CustomWidget\MessageBox\MessageBoxWidget\MessageBoxWidget2\messageboxwidget2.cpp.obj
Source\CMakeFiles\TPCC.dir\CustomWidget\MessageBox\MessageBoxWidget\MessageBoxWidget3\messageboxwidget3.cpp.obj
Source\CMakeFiles\TPCC.dir\CustomWidget\MessageBox\MessageBoxWidget\MessageBoxWidget4\messageboxwidget4.cpp.obj
Source\CMakeFiles\TPCC.dir\CustomWidget\PushButton\PushButtonS1M1\pushbuttons1m1.cpp.obj
Source\CMakeFiles\TPCC.dir\CustomWidget\PushButton\PushButtonS1M10\pushbuttons1m10.cpp.obj
Source\CMakeFiles\TPCC.dir\CustomWidget\PushButton\PushButtonS1M11\pushbuttons1m11.cpp.obj
Source\CMakeFiles\TPCC.dir\CustomWidget\PushButton\PushButtonS1M12\pushbuttons1m12.cpp.obj
Source\CMakeFiles\TPCC.dir\CustomWidget\PushButton\PushButtonS1M13\pushbuttons1m13.cpp.obj
Source\CMakeFiles\TPCC.dir\CustomWidget\PushButton\PushButtonS1M14\pushbuttons1m14.cpp.obj
Source\CMakeFiles\TPCC.dir\CustomWidget\PushButton\PushButtonS1M15\pushbuttons1m15.cpp.obj
Source\CMakeFiles\TPCC.dir\CustomWidget\PushButton\PushButtonS1M2\pushbuttons1m2.cpp.obj
Source\CMakeFiles\TPCC.dir\CustomWidget\PushButton\PushButtonS1M3\pushbuttons1m3.cpp.obj
Source\CMakeFiles\TPCC.dir\CustomWidget\PushButton\PushButtonS1M4\pushbuttons1m4.cpp.obj
Source\CMakeFiles\TPCC.dir\CustomWidget\PushButton\PushButtonS1M5\pushbuttons1m5.cpp.obj
Source\CMakeFiles\TPCC.dir\CustomWidget\PushButton\PushButtonS1M6\pushbuttons1m6.cpp.obj
Source\CMakeFiles\TPCC.dir\CustomWidget\PushButton\PushButtonS1M7\pushbuttons1m7.cpp.obj
Source\CMakeFiles\TPCC.dir\CustomWidget\PushButton\PushButtonS1M8\pushbuttons1m8.cpp.obj
Source\CMakeFiles\TPCC.dir\CustomWidget\PushButton\PushButtonS1M9\pushbuttons1m9.cpp.obj
Source\CMakeFiles\TPCC.dir\CustomWidget\PushButtonGroup\PushButtonGroupS1M1\pushbuttongroups1m1.cpp.obj
Source\CMakeFiles\TPCC.dir\CustomWidget\PushButtonGroup\PushButtonGroupS1M10\pushbuttongroups1m10.cpp.obj
Source\CMakeFiles\TPCC.dir\CustomWidget\PushButtonGroup\PushButtonGroupS1M11\pushbuttongroups1m11.cpp.obj
Source\CMakeFiles\TPCC.dir\CustomWidget\PushButtonGroup\PushButtonGroupS1M12\pushbuttongroups1m12.cpp.obj
Source\CMakeFiles\TPCC.dir\CustomWidget\PushButtonGroup\PushButtonGroupS1M2\pushbuttongroups1m2.cpp.obj
Source\CMakeFiles\TPCC.dir\CustomWidget\PushButtonGroup\PushButtonGroupS1M3\pushbuttongroups1m3.cpp.obj
Source\CMakeFiles\TPCC.dir\CustomWidget\PushButtonGroup\PushButtonGroupS1M4\pushbuttongroups1m4.cpp.obj
Source\CMakeFiles\TPCC.dir\CustomWidget\PushButtonGroup\PushButtonGroupS1M5\pushbuttongroups1m5.cpp.obj
Source\CMakeFiles\TPCC.dir\CustomWidget\PushButtonGroup\PushButtonGroupS1M6\pushbuttongroups1m6.cpp.obj
Source\CMakeFiles\TPCC.dir\CustomWidget\PushButtonGroup\PushButtonGroupS1M7\pushbuttongroups1m7.cpp.obj
Source\CMakeFiles\TPCC.dir\CustomWidget\PushButtonGroup\PushButtonGroupS1M8\pushbuttongroups1m8.cpp.obj
Source\CMakeFiles\TPCC.dir\CustomWidget\PushButtonGroup\PushButtonGroupS1M9\pushbuttongroups1m9.cpp.obj
Source\CMakeFiles\TPCC.dir\CustomWidget\Slider\HSlider\HSliderS1M1\hsliders1m1.cpp.obj
Source\CMakeFiles\TPCC.dir\CustomWidget\Slider\HSlider\HSliderS2M1\hsliders2m1.cpp.obj
Source\CMakeFiles\TPCC.dir\CustomWidget\Slider\VSlider\VSliderS1M1\vsliders1m1.cpp.obj
Source\CMakeFiles\TPCC.dir\CustomWidget\Slider\VSlider\VSliderS1M2\vsliders1m2.cpp.obj
Source\CMakeFiles\TPCC.dir\CustomWidget\TabWidget\TabWidgetS1M1\tabwidgets1m1.cpp.obj
Source\CMakeFiles\TPCC.dir\CustomWidget\ToolButton\ToolButtonS1M1\toolbuttons1m1.cpp.obj
Source\CMakeFiles\TPCC.dir\CustomWidget\VolumeMeter\VolumeMeterS1M1\volumemeters1m1.cpp.obj
Source\CMakeFiles\TPCC.dir\CustomWidget\VolumeMeter\VolumeMeterS1M2\volumemeters1m2.cpp.obj
Source\CMakeFiles\TPCC.dir\CustomWidget\VolumeMeter\VolumeMeterS1M3\volumemeters1m3.cpp.obj
Source\CMakeFiles\TPCC.dir\CustomWidget\VolumeMeter\VolumeMeterS1M4\volumemeters1m4.cpp.obj
Source\CMakeFiles\TPCC.dir\CustomWidget\VolumeMeter\VolumeMeterS1M5\volumemeters1m5.cpp.obj
Source\CMakeFiles\TPCC.dir\CustomWidget\VolumeMeter\VolumeMeterS1M6\volumemeters1m6.cpp.obj
Source\CMakeFiles\TPCC.dir\CustomWidget\VolumeMeter\VolumeMeterS1M7\volumemeters1m7.cpp.obj
Source\CMakeFiles\TPCC.dir\CustomWidget\VolumeMeter\VolumeMeterS1M8\volumemeters1m8.cpp.obj
Source\CMakeFiles\TPCC.dir\CustomWidget\VolumeMeter\VolumeMeterS2M1\volumemeters2m1.cpp.obj
Source\CMakeFiles\TPCC.dir\ThirdPartyResource\AppIconWin.rc.res
Source\CMakeFiles\TPCC.dir\CustomFunction\AppSettings\appsettings.cpp.obj
Source\CMakeFiles\TPCC.dir\CustomFunction\AutoStartManager\autostartmanager.cpp.obj
Source\CMakeFiles\TPCC.dir\CustomFunction\DebugManager\debugmanager.cpp.obj
Source\CMakeFiles\TPCC.dir\CustomFunction\EqualizerTool\equalizertool.cpp.obj
Source\CMakeFiles\TPCC.dir\CustomFunction\GlobalFont\globalfont.cpp.obj
Source\CMakeFiles\TPCC.dir\CustomFunction\SingleInstanceManager\singleinstancemanager.cpp.obj
Source\CMakeFiles\TPCC.dir\CustomFunction\Solo\solo.cpp.obj
Source\CMakeFiles\TPCC.dir\CustomFunction\TrialManager\trialmanager.cpp.obj
Source\CMakeFiles\TPCC.dir\CustomFunction\USBAudioManager\usbaudiomanager.cpp.obj
Source\CMakeFiles\TPCC.dir\CustomFunction\Updater\UpdaterBase\updaterbase.cpp.obj
Source\CMakeFiles\TPCC.dir\CustomFunction\Updater\UpdaterFirmwareM1\updaterfirmwarem1.cpp.obj
Source\CMakeFiles\TPCC.dir\CustomFunction\Updater\UpdaterSoftware\updatersoftware.cpp.obj
Source\CMakeFiles\TPCC.dir\CustomFunction\Workspace\workspace.cpp.obj
Source\CMakeFiles\TPCC.dir\USBHID\Device\DeviceBase\devicebase.cpp.obj
Source\CMakeFiles\TPCC.dir\USBHID\Device\DeviceBase\devicetype1.cpp.obj
Source\CMakeFiles\TPCC.dir\USBHID\Device\DeviceM62\devicem62.cpp.obj
Source\CMakeFiles\TPCC.dir\USBHID\API\usbhidapi.cpp.obj
Source\CMakeFiles\TPCC.dir\USBHID\SDK\win\hid.c.obj
Source\CMakeFiles\TPCC.dir\USBAudio\SDK\WIN\TUsbAudioApiDll.cpp.obj
Source\CMakeFiles\TPCC.dir\USBAudio\SDK\WIN\TUsbAudioApiExtendedInfo.cpp.obj
Source\CMakeFiles\TPCC.dir\USBAudio\SDK\WIN\TUsbAudioMixer.cpp.obj
Source\CMakeFiles\TPCC.dir\USBAudio\SDK\WIN\TbStdStringUtils.cpp.obj
Source\CMakeFiles\TPCC.dir\USBAudio\SDK\WIN\TbStringUtils.cpp.obj
Source\CMakeFiles\TPCC.dir\USBAudio\SDK\WIN\WnModuleFileName.cpp.obj
Source\CMakeFiles\TPCC.dir\USBAudio\SDK\WIN\WnRegistryKey.cpp.obj
Source\CMakeFiles\TPCC.dir\USBAudio\SDK\WIN\WnStringUtils.cpp.obj
Source\CMakeFiles\TPCC.dir\USBAudio\SDK\WIN\WnThread.cpp.obj
Source\CMakeFiles\TPCC.dir\USBAudio\SDK\WIN\WnTrace.cpp.obj
Source\CMakeFiles\TPCC.dir\USBAudio\SDK\WIN\WnTraceLogContext.cpp.obj
Source\CMakeFiles\TPCC.dir\USBAudio\SDK\WIN\WnTraceLogFile.cpp.obj
Source\CMakeFiles\TPCC.dir\USBAudio\SDK\WIN\WnTraceLogSettings.cpp.obj
Source\CMakeFiles\TPCC.dir\USBAudio\SDK\WIN\WnUiLanguage.cpp.obj
Source\CMakeFiles\TPCC.dir\USBAudio\SDK\WIN\WnUiLanguageFile.cpp.obj
Source\CMakeFiles\TPCC.dir\USBAudio\SDK\WIN\WnUiLanguageMgr.cpp.obj
Source\CMakeFiles\TPCC.dir\USBAudio\SDK\WIN\WnUiLanguageText.cpp.obj
Source\CMakeFiles\TPCC.dir\USBAudio\SDK\WIN\WnUiLanguageTextGroup.cpp.obj
Source\CMakeFiles\TPCC.dir\USBAudio\SDK\WIN\WnUserModeCrashDump.cpp.obj
Source\CMakeFiles\TPCC.dir\USBAudio\SDK\WIN\WnWow64.cpp.obj
Source\CMakeFiles\TPCC.dir\USBAudio\SDK\WIN\libtb_OSEnv_impl.cpp.obj
Source\CMakeFiles\TPCC.dir\USBAudio\API\usbaudioapi.cpp.obj
Source\CMakeFiles\TPCC.dir\TPCC_autogen\QFVAREFS7T\qrc_ThirdPartyResource.cpp.obj  D:\Qt\6.9.0\msvc2022_64\lib\Qt6Networkd.lib  D:\Qt\6.9.0\msvc2022_64\lib\Qt6Chartsd.lib  D:\Qt\6.9.0\msvc2022_64\lib\Qt6Svgd.lib  ws2_32.lib  D:\Qt\6.9.0\msvc2022_64\lib\Qt6OpenGLWidgetsd.lib  D:\Qt\6.9.0\msvc2022_64\lib\Qt6Widgetsd.lib  D:\Qt\6.9.0\msvc2022_64\lib\Qt6OpenGLd.lib  user32.lib  D:\Qt\6.9.0\msvc2022_64\lib\Qt6Guid.lib  D:\Qt\6.9.0\msvc2022_64\lib\Qt6Cored.lib  mpr.lib  userenv.lib  D:\Qt\6.9.0\msvc2022_64\lib\Qt6EntryPointd.lib  shell32.lib  d3d11.lib  dxgi.lib  dxguid.lib  d3d12.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib