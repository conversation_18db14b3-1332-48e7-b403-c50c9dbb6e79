#include "globalfont.h"
#include "pushbuttongroups1m6.h"
#include "ui_pushbuttongroups1m6.h"


PushButtonGroupS1M6::PushButtonGroupS1M6(QWidget *parent)
    : QWidget(parent)
    , ui(new Ui::PushButtonGroupS1M6)
{
    ui->setupUi(this);
    ui->Item1PushButton->setCheckable(true);
    ui->Item2PushButton->setCheckable(true);
    ui->Item3PushButton->setCheckable(true);
    ui->Item1PushButton->setAttribute(Qt::WA_LayoutUsesWidgetRect);
    ui->Item2PushButton->setAttribute(Qt::WA_LayoutUsesWidgetRect);
    ui->Item3PushButton->setAttribute(Qt::WA_LayoutUsesWidgetRect);
    ui->Item3PushButton->installEventFilter(this);
    setState("Mic1", "1", false);
    setLanguage("English");
}
PushButtonGroupS1M6::~PushButtonGroupS1M6()
{
    delete ui;
}


// override
void PushButtonGroupS1M6::resizeEvent(QResizeEvent* e)
{
    Q_UNUSED(e);
}
bool PushButtonGroupS1M6::eventFilter(QObject* watched, QEvent* event)
{
    if(watched == ui->Item3PushButton && event->type() == QEvent::Resize)
    {
        mFont.setPointSize(GLBFHandle.getSuitablePointSize(mFont, ui->Item3PushButton->text(), ui->Item3PushButton->rect()) - 1);
        ui->Item1PushButton->setFont(mFont);
        ui->Item2PushButton->setFont(mFont);
        ui->Item3PushButton->setFont(mFont);
        ui->Item1Label->setFixedHeight(ui->Item3PushButton->height() * 0.7);
        ui->Item2Label->setFixedHeight(ui->Item3PushButton->height() * 0.7);
        ui->Item3Label->setFixedHeight(ui->Item3PushButton->height() * 0.7);
    }
    return QWidget::eventFilter(watched, event);
}


// slot
void PushButtonGroupS1M6::on_Item1PushButton_clicked(bool checked)
{
    Q_UNUSED(checked);
    setState("Mic1", "1");
}
void PushButtonGroupS1M6::on_Item2PushButton_clicked(bool checked)
{
    Q_UNUSED(checked);
    setState("Mic35", "1");
}
void PushButtonGroupS1M6::on_Item3PushButton_clicked(bool checked)
{
    Q_UNUSED(checked);
    setState("MicHP", "1");
}


// setter & getter
PushButtonGroupS1M6& PushButtonGroupS1M6::setFont(QFont font)
{
    mFont = font;
    QResizeEvent e(size(), size());
    resizeEvent(&e);
    update();
    return *this;
}
PushButtonGroupS1M6& PushButtonGroupS1M6::setLanguage(QString language)
{
    if(language == "English")
    {
        ui->Item1PushButton->setText("Mic 1");
        ui->Item2PushButton->setText("Mic-35");
        ui->Item3PushButton->setText("Mic-HP");
    }
    else if(language == "Chinese")
    {
        ui->Item1PushButton->setText("Mic 1");
        ui->Item2PushButton->setText("Mic-35");
        ui->Item3PushButton->setText("Mic-HP");
    }
    return *this;
}
PushButtonGroupS1M6& PushButtonGroupS1M6::setState(QString button, QString state, bool needEmit)
{
    QLabel* currentLabel=nullptr;
    QPushButton* currentButton=nullptr;
    if(button == "Mic1")
    {
        currentLabel = ui->Item1Label;
        currentButton = ui->Item1PushButton;
    }
    else if(button == "Mic35")
    {
        currentLabel = ui->Item2Label;
        currentButton = ui->Item2PushButton;
    }
    else if(button == "MicHP")
    {
        currentLabel = ui->Item3Label;
        currentButton = ui->Item3PushButton;
    }
    if(currentButton != nullptr)
    {
        QString style;
        style = "QLabel {"
                "   background-color: transparent;"
                "   image: url(:/Icon/VerticalRoundedRectBlack.png);"
                "}";
        ui->Item1Label->setStyleSheet(style);
        ui->Item2Label->setStyleSheet(style);
        ui->Item3Label->setStyleSheet(style);
        style = "QLabel {"
                "   background-color: transparent;"
                "   image: url(:/Icon/VerticalRoundedRectGreen.png);"
                "}";
        currentLabel->setStyleSheet(style);
        style = "QPushButton {"
                "   text-align: left;"
                "   color: rgb(161, 161, 161);"
                "   background-color: transparent;"
                "}"
                "QPushButton:hover {"
                "   color: rgb(255, 255, 255);"
                "}";
        ui->Item1PushButton->setChecked(false);
        ui->Item2PushButton->setChecked(false);
        ui->Item3PushButton->setChecked(false);
        ui->Item1PushButton->setStyleSheet(style);
        ui->Item2PushButton->setStyleSheet(style);
        ui->Item3PushButton->setStyleSheet(style);
        style = "QPushButton {"
                "   text-align: left;"
                "   color: rgb(255, 255, 255);"
                "   background-color: transparent;"
                "}"
                "QPushButton:hover {"
                "   color: rgb(255, 255, 255);"
                "}";
        currentButton->setChecked(true);
        currentButton->setStyleSheet(style);
        QString preItem=mCurrentItem;
        mCurrentItem = button;
        if(preItem != mCurrentItem && needEmit) emit stateChanged(button, state);
    }
    return *this;
}
QString PushButtonGroupS1M6::getState(QString button)
{
    Q_UNUSED(button);
    return mCurrentItem;
}

