#ifndef MENUS1M1_H
#define MENUS1M1_H

#include <QWidget>

class QToolButton;
class QLabel;
class QButtonGroup;
class MenuS1M1 : public QWidget
{
    Q_OBJECT

public:
    enum MenuPosition {
        Left,
        Right
    };

    struct ButtonData
    {
        QString buttonText;
        QString buttonData;
        bool isCheckable = true;
    };

    struct ButtonAction
    {
        QString actionText;
        QString actionData;
        bool isEnable;
    };

    explicit MenuS1M1(QWidget *parent, const QString& title);
    void setFont(const QFont &font);
    void setTitle(const QString& title);
    void setTitleBackground(const QColor& background);
    void setTitleTextColor(const QColor& color);
    void setButtonBackground(const QColor& background);
    void setButtonTextColor(const QColor& color);
    void addButton(const ButtonData& buttonData, const QVector<ButtonAction> &buttonActions = {});
    void setMenuPosition(MenuPosition position) {m_menuPosition = position;}
    void setButtonExlusive(bool exclusive);
    void setStretchFactor(int titleHeightRatio, int buttonsHeightRatio);
    void setButtonChecked(const QString& buttonData, bool checked);
    void setButtonText(const QString& buttonData, const QString& text);
    void setWidgetBorderRadius(int radius);
    void setLabelBorderRadius(int radius);
    void setButtonFontSizeRatio(double ratio);
    
protected:
    void resizeEvent(QResizeEvent *event) override;
    void paintEvent(QPaintEvent *event) override;

signals:
    void attributeChanged(QString objectName, QString attribute, QString value);

private:
    void setWidgetStyle(QWidget* widget);
    void setTitleStyle(QWidget* widget);
    void setButtonStyle(QToolButton* button);
    void setMenuStyle(QMenu* menu, QToolButton* button);

private:
    QLabel* m_title;
    QHash<QString, QToolButton*> m_buttonMap;
    QVector<QToolButton*> m_buttons;
    MenuPosition m_menuPosition;
    QVector<QWidget*> m_lines;
    int m_titleHeightRatio;
    int m_buttonsHeightRatio;
    int m_widgetBorderRadius;
    int m_labelBorderRadius;
    QColor m_titleBackgroundColor;
    QColor m_titleTextColor;
    QColor m_buttonBackgroundColor;
    QColor m_buttonTextColor;
    QButtonGroup* m_buttonGroup;
    double m_buttonFontSizeRatio;
};

#endif // MENUS1M1_H
