/************************************************************************
 *
 *  Module:       libbase.h
 *  Description:
 *     library global includes, constants, declarations, etc.
 *
 *  Author(s):
 *    <PERSON>
 *
 *  Companies:
 *    Thesycon GmbH, Germany      http://www.thesycon.de
 *
 ************************************************************************/

#ifndef __libbase_h__
#define __libbase_h__

#include "tbase_platform.h"
#include "tbase_types.h"
#include "tbase_al.h"
#include "tbase_utils.h"


#endif  /* __libbase_h__ */

/*************************** EOF **************************************/
