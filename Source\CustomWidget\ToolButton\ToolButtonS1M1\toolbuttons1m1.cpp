#include "globalfont.h"
#include "toolbuttons1m1.h"
#include <QTimer>
#include <QtCore/qnamespace.h>

#ifdef Q_OS_MAC
#include <QProxyStyle>
#include <QPainter>
#include <QPainterPath>
#include <QStyleOption>
class MacMenuStyle : public QProxyStyle {
public:
    MacMenuStyle(QStyle* style) : QProxyStyle(style) {}

    void drawPrimitive(PrimitiveElement element, const QStyleOption *option, QPainter *painter, const QWidget *widget = nullptr) const override
    {
        // if(element==PE_PanelMenu){
        //     painter->save();
        //     QPen pen(QColor(172,172,172));
        //     pen.setWidth(1);
        //     painter->setPen(pen);
        //     painter->setBrush(QColor(0,0,0));
        //     painter->drawRoundedRect(option->rect.adjusted(0,3,-1,-5), 2, 2);
        //     painter->restore();
        //     return;
        // }
        QProxyStyle::drawPrimitive(element, option, painter, widget);
    }
};
#endif


ToolButtonS1M1::ToolButtonS1M1(QWidget* parent)
    : QWidget(parent)
{
    mToolButton.setParent(this);
    mToolButton.setToolButtonStyle(Qt::ToolButtonTextUnderIcon);
    mToolButton.setPopupMode(QToolButton::InstantPopup);
    mToolButton.setMenu(&mMenu);
    connect(&mMenu, SIGNAL(triggered(QAction*)), this, SLOT(in_mMenu_triggered(QAction*)));
#ifdef Q_OS_MACOS
    mMenu.setStyle(new MacMenuStyle(style()));
    connect(&mMenu, &QMenu::aboutToShow,this,[this](){
        QTimer::singleShot(0, this, [this](){
            mToolButton.setAttribute(Qt::WA_UnderMouse, true);
        });
    });
    connect(&mMenu, &QMenu::aboutToHide,this,[this](){
        mToolButton.setAttribute(Qt::WA_UnderMouse, false);
    });
#endif
}
ToolButtonS1M1::~ToolButtonS1M1()
{

}


// override
void ToolButtonS1M1::resizeEvent(QResizeEvent* e)
{
    Q_UNUSED(e);
    QString style;
    mToolButton.setGeometry(rect());
    style = QString("QToolButton {"
                    "   color: %1;"
                    "}"
                    "QToolButton::menu-indicator {"
#ifdef Q_OS_MAC
                    "   image: url(:/Icon/ArrowDown.png);"
#endif
                    "   width: %2px;"
                    "   height: %2px;"
                    "   subcontrol-origin: padding;"
                    "   subcontrol-position: right center;"
                    "}").arg(mColor).arg(size().height() * 0.3);
    mToolButton.setStyleSheet(style);
    int wPadding=size().width() * 0.1;
#ifdef Q_OS_MAC
    wPadding = size().width() / 10;
#endif
    style = QString(
#ifdef Q_OS_WIN
                "QMenu {"
                    "   color: rgb(216, 216, 216);"
                    "   background-color: rgb(22, 22, 22);"
                    "   border: 1px solid rgb(70, 70, 70);"
                    "   border-radius: %4px;"
                    "}"
#endif
                    "QMenu::item {"
                    "   background-color: transparent;"
                    "   width: %1px;"
                    "   height: %2px;"
                    "   padding: 0px %3px;"
                    "}"
                    "QMenu::item:selected {"
                    "   background-color: rgb(66, 66, 66);"
                    "}"
                    "QMenu::right-arrow {"
                    "   width: %3px;"
                    "   height: %3px;"
                    "   subcontrol-origin: padding;"
                    "   subcontrol-position: right center;"
                    "}").arg(size().width() - 2 - wPadding * 2).arg(size().height()).arg(wPadding).arg(wPadding * 0.5);
    mMenu.setStyleSheet(style);
    mFont.setPointSize(GLBFHandle.getSuitablePointSize(mFont, mLongestAction, QRect(0, 0, size().width() - 2 - wPadding * 1.6, size().height())));
    mToolButton.setFont(mFont);
    mMenu.setFont(mFont);
    for(auto action : mMenu.actions())
    {
        action->menu()->setStyleSheet(style);
        action->menu()->setFont(mFont);
    }
}


// slot
void ToolButtonS1M1::in_mMenu_triggered(QAction* action)
{
    mToolButton.setText(action->text());
    emit actionChanged(action->text());
}


// setter & getter
ToolButtonS1M1& ToolButtonS1M1::setFont(QFont font)
{
    mFont = font;
    QResizeEvent e(size(), size());
    resizeEvent(&e);
    update();
    return *this;
}
ToolButtonS1M1& ToolButtonS1M1::addMenu(QString menuName)
{
    for(auto action : mMenu.actions())
    {
        if(action->text() == menuName)
        {
            return *this;
        }
    }
    mLongestAction = GLBFHandle.getLongerInPointSize(mLongestAction, menuName);
    QMenu* newMenu=new QMenu(menuName);
#ifdef Q_OS_MAC
    newMenu->setStyle(new MacMenuStyle(style()));
#endif
    mMenu.addMenu(newMenu);
    QResizeEvent e(size(), size());
    resizeEvent(&e);
    update();
    return *this;
}
ToolButtonS1M1& ToolButtonS1M1::addAction(QString menuName, QString actionName)
{
    QMenu* targetMenu=nullptr;
    for(auto action : mMenu.actions())
    {
        if(action->text() == menuName)
        {
            targetMenu = action->menu();
            if(action->menu()->actions().contains(action))
            {
                return *this;
            }
            break;
        }
    }
    if(targetMenu == nullptr)
    {
        return *this;
    }
    mLongestAction = GLBFHandle.getLongerInPointSize(mLongestAction, actionName);
    targetMenu->addAction(actionName);
    QResizeEvent e(size(), size());
    resizeEvent(&e);
    update();
    return *this;
}
ToolButtonS1M1& ToolButtonS1M1::setColor(QColor color)
{
    mColor=QString("rgb(%1, %2, %3)").arg(color.red()).arg(color.green()).arg(color.blue());
    QResizeEvent e(size(), size());
    resizeEvent(&e);
    update();
    return *this;
}
ToolButtonS1M1& ToolButtonS1M1::setActionTriggered(QString actionName)
{
    for(auto action : mMenu.actions())
    {
        for(auto actionChild : action->menu()->actions())
        {
            if(actionChild->text() == actionName)
            {
                mToolButton.setText(actionChild->text());
            }
        }
    }
    return *this;
}

