#ifndef M62_PrivateWidget1_1_H
#define M62_PrivateWidget1_1_H


#include <QFont>
#include <QEvent>
#include <QTimer>
#include <QObject>
#include <QWidget>
#include <QResizeEvent>

#include "inputbase.h"
#include "workspace.h"
#include "appsettings.h"

namespace Ui {
class M62_PrivateWidget1_1;
}


class M62_PrivateWidget1_1 : public InputBase, public WorkspaceObserver, public AppSettingsObserver
{
    Q_OBJECT
public:
    explicit M62_PrivateWidget1_1(QWidget* parent=nullptr, QString name="");
    ~M62_PrivateWidget1_1();
    M62_PrivateWidget1_1& setName(QString name);
    M62_PrivateWidget1_1& setFont(QFont font);
    M62_PrivateWidget1_1& setVolumeMeterLeft(int value);
    M62_PrivateWidget1_1& setVolumeMeterRight(int value);
    void setChannelName(const QString& name, bool isSendSig=true);

protected:
    bool eventFilter(QObject* obj, QEvent* e) override;
    void resizeEvent(QResizeEvent* e) override;
    void updateAttribute() override;
    void setSoloState(bool state) override;
    void setSoloStateLeft(bool state) override;
    void setSoloStateRight(bool state) override;
    void setMuteState(bool state) override;
    void setMuteStateLeft(bool state) override;
    void setMuteStateRight(bool state) override;
    void setSoloClicked(bool state) override;
    void setSoloClickedLeft(bool state) override;
    void setSoloClickedRight(bool state) override;
    void setMuteClicked(bool state) override;
    void setMuteClickedLeft(bool state) override;
    void setMuteClickedRight(bool state) override;
    bool getSoloState() override;
    bool getSoloStateLeft() override;
    bool getSoloStateRight() override;
    bool getMuteState() override;
    bool getMuteStateLeft() override;
    bool getMuteStateRight() override;
    void AppSettingsChanged(QString objectName, QString attribute, QString value) override;
    void loadSettings() override;

private:
    Ui::M62_PrivateWidget1_1* ui;
    QTimer mTimer;
    QFont mFont;

private slots:
    void in_mTimer_timeout();
    void on_lineEdit_textChanged(const QString& arg1);
    void on_lineEdit_editingFinished();
    void on_pushButtonClose_clicked();
};


#endif // M62_PrivateWidget1_1_H

